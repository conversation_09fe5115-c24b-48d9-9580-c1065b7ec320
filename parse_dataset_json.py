import json
import os

def parse_dataset_json(json_file, output_dir='.'):
    """
    解析dataset.json文件并生成训练和测试列表文件
    
    参数:
        json_file: dataset.json文件路径
        output_dir: 输出列表文件的目录，默认为当前目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 处理训练数据
    train_list = []
    for item in data.get('training', []):
        # 提取文件名并替换后缀
        image_path = item.get('image', '')
        filename = os.path.basename(image_path)
        assert filename.endswith('.nii.gz')
        filename = filename[:-7] + '.h5'  # 移除 .nii.gz
        train_list.append(filename)
    
    # 处理测试数据
    test_list = []
    for item in data.get('test', []):
        # 提取文件名并替换后缀
        filename = os.path.basename(item)
        assert filename.endswith('.nii.gz')
        filename = filename[:-7] + '.h5'  # 移除 .nii.gz
        test_list.append(filename)
    
    # 写入训练列表文件
    train_file = os.path.join(output_dir, 'train.list')
    with open(train_file, 'w', encoding='utf-8') as f:
        for filename in train_list:
            f.write(filename + '\n')
    
    # 写入测试列表文件
    test_file = os.path.join(output_dir, 'test.list')
    with open(test_file, 'w', encoding='utf-8') as f:
        for filename in test_list:
            f.write(filename + '\n')
    
    print(f"成功生成列表文件:")
    print(f"训练列表: {train_file} ({len(train_list)}条记录)")
    print(f"测试列表: {test_file} ({len(test_list)}条记录)")

if __name__ == "__main__":
    # 示例用法
    json_file = "/home/<USER>/data/tumor/Dataset017_Liver/dataset.json"  # 替换为实际的JSON文件路径
    output_dir = os.path.dirname(json_file)  # 替换为你想要保存列表文件的目录
    parse_dataset_json(json_file, output_dir)