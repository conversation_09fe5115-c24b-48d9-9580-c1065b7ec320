# CT图像和标签可视化工具

本文档介绍了用于可视化CT图像和标签数据的脚本工具。

## 文件说明

### 1. `preprocessing_liver.py` (已修改)
- **功能**: 原始的肝脏数据预处理脚本，已添加可视化功能
- **新增功能**: 
  - 在预处理过程中自动生成可视化图像
  - 可配置是否启用可视化和保存选项
  - 显示处理后的CT切片和标签叠加图

### 2. `visualize_h5_data.py`
- **功能**: 通用H5文件可视化工具
- **特点**: 
  - 支持任意H5文件的图像和标签可视化
  - 可指定特定切片或自动选择中间切片
  - 支持多切片同时显示
  - 提供详细的数据统计信息

### 3. `visualize_liver_slice.py`
- **功能**: 专门针对肝脏数据的可视化工具
- **特点**: 
  - 自动寻找包含肿瘤的切片
  - 分别显示肝脏和肿瘤区域
  - 显示肿瘤在多个切片中的分布
  - 提供详细的统计分析

### 4. `count_label_pixels.py`
- **功能**: 统计H5文件中特定标签的像素数量
- **特点**: 
  - 快速统计任意标签值的像素数
  - 支持批量处理多个文件
  - 提供百分比统计

## 使用方法

### 基本可视化

#### 1. 快速查看liver_0.h5文件
```bash
python visualize_h5_data.py
```

#### 2. 查看特定H5文件
```bash
python visualize_h5_data.py /path/to/your/file.h5
```

#### 3. 查看特定切片
```bash
python visualize_h5_data.py /path/to/your/file.h5 --slice 50
```

#### 4. 保存可视化结果
```bash
python visualize_h5_data.py /path/to/your/file.h5 --save_dir ./output
```

### 肝脏专用可视化

#### 1. 运行肝脏可视化
```bash
python visualize_liver_slice.py
```
这将自动：
- 寻找包含最多肿瘤像素的切片
- 生成详细的分析图像
- 显示肿瘤在多个切片中的分布
- 保存结果到 `liver_visualizations/` 目录

### 像素计数

#### 1. 统计特定标签像素数
```bash
python count_label_pixels.py /path/to/file.h5 --label 2
```

#### 2. 统计所有标签
```bash
python count_label_pixels.py /path/to/file.h5 --all
```

## 可视化结果说明

### liver_0.h5 分析结果

根据对 `/home/<USER>/data/tumor/Dataset017_Liver/h5/liver_0.h5` 的分析：

#### 数据基本信息
- **数据形状**: (266, 209, 177) - 3D体积数据
- **数据类型**: float32
- **图像范围**: [-1.194, 1.126] (已标准化)
- **图像均值**: -0.000, 标准差: 1.000

#### 标签分布
- **Label 0 (背景)**: 8,473,773 像素 (86.11%)
- **Label 1 (肝脏)**: 1,360,010 像素 (13.82%)
- **Label 2 (肿瘤)**: 6,355 像素 (0.06%)

#### 肿瘤分布特征
- **包含肿瘤的切片数**: 80个切片
- **肿瘤最多的切片**: 第90-94切片，每个包含267个肿瘤像素
- **肿瘤分布**: 相对集中在特定的连续切片中

### 可视化图像类型

#### 1. 单切片详细分析
- **CT图像**: 原始的CT切片（灰度显示）
- **标签图像**: 彩色编码的标签（黑色=背景，蓝色=肝脏，红色=肿瘤）
- **叠加图像**: CT图像上叠加半透明的标签
- **分离显示**: 分别显示肝脏和肿瘤区域
- **统计信息**: 详细的像素统计和图像信息

#### 2. 多切片对比
- **肿瘤分布**: 显示肿瘤在不同切片中的分布情况
- **进展分析**: 按肿瘤像素数量排序的切片对比

## 配置选项

### preprocessing_liver.py 配置
```python
ENABLE_VISUALIZATION = True  # 启用可视化
VISUALIZE_FIRST_N = 3        # 只可视化前N个样本
SAVE_VISUALIZATIONS = True   # 保存可视化结果
```

### 颜色编码
- **背景**: 黑色 (Label 0)
- **肝脏**: 蓝色 (Label 1)  
- **肿瘤**: 红色 (Label 2)

## 输出文件

### 自动生成的可视化文件
1. **单切片分析**: `{filename}_single_slice.png`
2. **多切片对比**: `{filename}_multiple_slices.png`
3. **肿瘤分布**: `{filename}_tumor_progression.png`

### 目录结构
```
liver_visualizations/
├── liver_0_best_tumor_slice.png      # 最佳肿瘤切片详细分析
└── liver_0_tumor_progression.png     # 肿瘤分布对比
```

## 技术特点

### 1. 自动切片选择
- 自动寻找包含目标标签的切片
- 按像素数量排序选择最佳切片
- 支持手动指定切片索引

### 2. 多层次可视化
- 原始图像、标签、叠加三种视图
- 分离显示不同标签区域
- 统计信息实时计算

### 3. 灵活的保存选项
- 支持显示或保存到文件
- 高分辨率输出 (150 DPI)
- 自动创建输出目录

### 4. 详细的统计分析
- 像素数量和百分比统计
- 图像强度分析
- 标签分布信息

## 使用建议

### 1. 数据质量检查
- 使用 `visualize_h5_data.py --stats_only` 快速检查数据统计
- 查看标签分布是否合理
- 检查图像强度范围

### 2. 肿瘤分析
- 使用 `visualize_liver_slice.py` 进行详细的肿瘤分析
- 查看肿瘤在3D空间中的分布
- 分析肿瘤与肝脏的空间关系

### 3. 批量处理
- 修改脚本中的文件路径进行批量可视化
- 使用循环处理多个H5文件
- 自动生成报告

## 故障排除

### 常见问题
1. **文件不存在**: 检查H5文件路径是否正确
2. **内存不足**: 对于大文件，考虑只可视化部分切片
3. **显示问题**: 在服务器环境中使用 `--save_dir` 选项保存图像

### 性能优化
- 大文件建议使用切片索引而非全部可视化
- 可以调整图像分辨率以节省存储空间
- 使用统计模式快速检查数据概况

这些工具为CT图像分析提供了全面的可视化支持，帮助理解数据结构和标签分布。
