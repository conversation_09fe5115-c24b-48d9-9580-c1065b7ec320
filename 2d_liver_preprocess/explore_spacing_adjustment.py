#!/usr/bin/env python3
"""
Script to explore spacing_z adjustments to achieve target slice count
"""

import pandas as pd
import numpy as np
import ast
import matplotlib.pyplot as plt

def explore_spacing_adjustment(csv_file, target_slices=160):
    """
    Explore what spacing_z values would result in target slice count
    
    Args:
        csv_file (str): Path to the CSV file
        target_slices (int): Target number of slices
    """
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        print(f"分析 {len(df)} 个文件，目标切片数: {target_slices}")
        print()
        
        # Parse shape information
        shapes = []
        slices_list = []
        
        for idx, row in df.iterrows():
            try:
                shape_str = row['shape']
                if isinstance(shape_str, str):
                    shape = ast.literal_eval(shape_str)
                    shapes.append(shape)
                    if len(shape) >= 3:
                        slices_list.append(shape[2])
                    else:
                        slices_list.append(None)
                else:
                    shapes.append(None)
                    slices_list.append(None)
            except:
                shapes.append(None)
                slices_list.append(None)
        
        df['parsed_shape'] = shapes
        df['current_slices'] = slices_list
        
        # Filter valid data
        valid_df = df[(df['current_slices'].notna()) & (df['spacing_z'].notna())].copy()
        
        print(f"有效数据: {len(valid_df)} 个文件")
        print()
        
        # Calculate physical thickness for each volume
        # Physical thickness = current_slices * current_spacing_z
        valid_df['physical_thickness'] = valid_df['current_slices'] * valid_df['spacing_z']
        
        # Calculate required spacing_z to achieve target slices
        # new_spacing_z = physical_thickness / target_slices
        valid_df['required_spacing_z'] = valid_df['physical_thickness'] / target_slices
        
        # Calculate the ratio of adjustment needed
        valid_df['spacing_ratio'] = valid_df['required_spacing_z'] / valid_df['spacing_z']
        
        # Display results
        print("文件名\t\t当前切片\t当前spacing\t物理厚度\t需要spacing\t调整比例")
        print("-" * 90)
        
        for idx, row in valid_df.iterrows():
            filename = row['filename'][:15]  # Truncate for display
            current_slices = int(row['current_slices'])
            current_spacing = row['spacing_z']
            thickness = row['physical_thickness']
            required_spacing = row['required_spacing_z']
            ratio = row['spacing_ratio']
            
            print(f"{filename:<15}\t{current_slices:<8}\t{current_spacing:<8.3f}\t{thickness:<8.1f}\t{required_spacing:<8.3f}\t{ratio:<8.3f}")
        
        print()
        print("=" * 90)
        
        # Statistics
        print("统计分析:")
        print(f"当前切片数 - 平均: {valid_df['current_slices'].mean():.1f}, 范围: {valid_df['current_slices'].min()}-{valid_df['current_slices'].max()}")
        print(f"当前spacing - 平均: {valid_df['spacing_z'].mean():.3f}, 范围: {valid_df['spacing_z'].min():.3f}-{valid_df['spacing_z'].max():.3f}")
        print(f"物理厚度 - 平均: {valid_df['physical_thickness'].mean():.1f}mm, 范围: {valid_df['physical_thickness'].min():.1f}-{valid_df['physical_thickness'].max():.1f}mm")
        print()
        print(f"为达到{target_slices}切片:")
        print(f"需要spacing - 平均: {valid_df['required_spacing_z'].mean():.3f}mm, 范围: {valid_df['required_spacing_z'].min():.3f}-{valid_df['required_spacing_z'].max():.3f}mm")
        print(f"调整比例 - 平均: {valid_df['spacing_ratio'].mean():.3f}, 范围: {valid_df['spacing_ratio'].min():.3f}-{valid_df['spacing_ratio'].max():.3f}")
        print()
        
        # Analyze different scenarios
        print("不同目标切片数的spacing需求:")
        print("-" * 50)
        target_options = [128, 160, 192, 224, 256]
        
        for target in target_options:
            required_spacings = valid_df['physical_thickness'] / target
            avg_spacing = required_spacings.mean()
            min_spacing = required_spacings.min()
            max_spacing = required_spacings.max()
            print(f"目标{target:3d}切片: 平均spacing {avg_spacing:.3f}mm (范围: {min_spacing:.3f}-{max_spacing:.3f}mm)")
        
        print()
        
        # Find files that would need extreme adjustments
        extreme_threshold = 3.0  # Files needing >3x or <1/3x spacing adjustment
        extreme_files = valid_df[(valid_df['spacing_ratio'] > extreme_threshold) | 
                                (valid_df['spacing_ratio'] < 1/extreme_threshold)]
        
        if len(extreme_files) > 0:
            print(f"需要极端调整的文件 (调整比例 >{extreme_threshold:.1f}x 或 <{1/extreme_threshold:.1f}x):")
            print("-" * 60)
            for idx, row in extreme_files.iterrows():
                print(f"{row['filename']:<20} 当前: {int(row['current_slices'])}切片, {row['spacing_z']:.3f}mm -> 需要: {row['required_spacing_z']:.3f}mm (比例: {row['spacing_ratio']:.2f}x)")
        
        print()
        
        # Recommendations
        print("建议:")
        median_required = valid_df['required_spacing_z'].median()
        print(f"1. 统一spacing到 {median_required:.3f}mm 可以使大部分文件接近{target_slices}切片")
        
        # Find a good compromise spacing
        spacing_options = np.arange(0.5, 4.0, 0.1)
        best_spacing = None
        min_variance = float('inf')
        
        for spacing in spacing_options:
            resulting_slices = valid_df['physical_thickness'] / spacing
            variance = np.var(resulting_slices)
            if variance < min_variance:
                min_variance = variance
                best_spacing = spacing
        
        resulting_slices_best = valid_df['physical_thickness'] / best_spacing
        print(f"2. 最优spacing {best_spacing:.1f}mm 可以使切片数方差最小")
        print(f"   结果切片数范围: {resulting_slices_best.min():.0f}-{resulting_slices_best.max():.0f}, 平均: {resulting_slices_best.mean():.0f}")
        
        return valid_df
        
    except Exception as e:
        print(f"分析时出错: {e}")
        return None

if __name__ == "__main__":
    csv_file = "tmp/liver_analysis_results.csv"
    
    # Explore different target slice counts
    print("探索spacing调整以达到目标切片数")
    print("=" * 50)
    
    result_df = explore_spacing_adjustment(csv_file, target_slices=160)
    
    if result_df is not None:
        print("\n" + "="*50)
        print("额外分析: 不同目标切片数的详细对比")
        print("="*50)
        
        targets = [128, 160, 192, 224]
        for target in targets:
            print(f"\n目标 {target} 切片:")
            required_spacings = result_df['physical_thickness'] / target
            print(f"  需要spacing范围: {required_spacings.min():.3f} - {required_spacings.max():.3f} mm")
            print(f"  平均spacing: {required_spacings.mean():.3f} mm")
            print(f"  中位数spacing: {required_spacings.median():.3f} mm")
            
            # Count how many files would need significant resampling
            current_spacings = result_df['spacing_z']
            ratio_up = required_spacings / current_spacings
            ratio_down = current_spacings / required_spacings
            
            need_upsampling = np.sum(ratio_up > 2.0)  # Need to increase resolution significantly
            need_downsampling = np.sum(ratio_down > 2.0)  # Need to decrease resolution significantly
            
            print(f"  需要显著上采样的文件: {need_upsampling} 个")
            print(f"  需要显著下采样的文件: {need_downsampling} 个")
