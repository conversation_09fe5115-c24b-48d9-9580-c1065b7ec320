#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to read and analyze the liver analysis results CSV file
"""

import pandas as pd
import numpy as np
import ast

def read_and_analyze_csv(csv_file):
    """
    Read the CSV file and extract required information
    
    Args:
        csv_file (str): Path to the CSV file
    """
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        print(f"读取到 {len(df)} 个文件的分析结果")
        print()
        
        # Extract shape information (convert string representation to tuple)
        shapes = []
        slices_list = []
        
        for idx, row in df.iterrows():
            try:
                # Parse the shape string to get the actual tuple
                shape_str = row['shape']
                if isinstance(shape_str, str):
                    # Convert string representation of tuple to actual tuple
                    shape = ast.literal_eval(shape_str)
                    shapes.append(shape)
                    
                    # Get the third dimension (slices)
                    if len(shape) >= 3:
                        slices_list.append(shape[2])
                    else:
                        slices_list.append(None)
                else:
                    shapes.append(None)
                    slices_list.append(None)
            except:
                shapes.append(None)
                slices_list.append(None)
        
        # Add parsed data to dataframe
        df['parsed_shape'] = shapes
        df['slices_from_shape'] = slices_list
        
        # Display the required information
        print("文件名, 切片数(shape第三维), Z轴spacing:")
        print("-" * 60)
        
        valid_data = []
        for idx, row in df.iterrows():
            filename = row['filename']
            slices = row['slices_from_shape']
            spacing_z = row['spacing_z']
            
            print(f"{filename:<25} {slices if slices is not None else 'N/A':<8} {spacing_z:.4f}")
            
            if slices is not None and not pd.isna(spacing_z):
                valid_data.append((slices, spacing_z))
        
        print()
        print("=" * 60)
        
        # Calculate averages
        if valid_data:
            slices_values = [item[0] for item in valid_data]
            spacing_values = [item[1] for item in valid_data]
            
            avg_slices = np.mean(slices_values)
            avg_spacing = np.mean(spacing_values)
            
            print(f"统计信息 (基于 {len(valid_data)} 个有效文件):")
            print(f"平均切片数: {avg_slices:.2f}")
            print(f"平均Z轴spacing: {avg_spacing:.4f} mm")
            print()
            
            print(f"切片数范围: {min(slices_values)} - {max(slices_values)}")
            print(f"Z轴spacing范围: {min(spacing_values):.4f} - {max(spacing_values):.4f} mm")
            print()
            
            # Additional statistics
            print("详细统计:")
            print(f"切片数 - 中位数: {np.median(slices_values):.1f}, 标准差: {np.std(slices_values):.2f}")
            print(f"Z轴spacing - 中位数: {np.median(spacing_values):.4f}, 标准差: {np.std(spacing_values):.4f}")
        else:
            print("没有找到有效的数据进行统计")
            
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")

if __name__ == "__main__":
    csv_file = "tmp/liver_analysis_results.csv"
    read_and_analyze_csv(csv_file)
