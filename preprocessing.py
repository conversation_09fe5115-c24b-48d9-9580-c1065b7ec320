import numpy as np
from glob import glob
from tqdm import tqdm
import h5py
import nrrd
import nibabel as nib
import pandas as pd
# import xlrd
import pdb
import SimpleITK as sitk
from skimage import transform, measure
import os
import pydicom
import matplotlib.pyplot as plt


def ImageResample(sitk_image, new_spacing = [1,1,1], is_label = False):
    '''
    sitk_image:
    new_spacing: x,y,z
    is_label: if True, using Interpolator `sitk.sitkNearestNeighbor`
    '''
    size = np.array(sitk_image.GetSize())
    spacing = np.array(sitk_image.GetSpacing())
    new_spacing = np.array(new_spacing)
    new_size = size * spacing / new_spacing
    new_spacing_refine = size * spacing / new_size
    new_spacing_refine = [float(s) for s in new_spacing_refine]
    new_size = [int(s) for s in new_size]
    if not is_label:
        print(f'Original size: {size}')
        print(f'New size: {new_size}')
        print(f'Original spacing: {spacing}')
        print(f'New spacing: {new_spacing_refine}')
    resample = sitk.ResampleImageFilter()
    resample.SetOutputDirection(sitk_image.GetDirection())
    resample.SetOutputOrigin(sitk_image.GetOrigin())
    resample.SetSize(new_size)
    resample.SetOutputSpacing(new_spacing_refine)

    if is_label:
        resample.SetInterpolator(sitk.sitkNearestNeighbor)
    else:
        resample.SetInterpolator(sitk.sitkBSpline)
    newimage = resample.Execute(sitk_image)
    return newimage



def set_window_wl_ww(tensor, sl_window=None):
    assert(sl_window != None)
    # sl_window = [75,400]
    # sl_window = [-125, 275]
    # [wl,ww] = sl_window
    w_min, w_max = sl_window
    print('w_min: ', w_min)
    print('w_max: ', w_max)
    input()
    tensor[tensor < w_min] = w_min
    tensor[tensor > w_max] = w_max
    tensor = (tensor - w_min) / (w_max - w_min)
    ### min max Normalization
    return tensor

# 根据标签图像（label）中的非零区域来裁剪图像（image）和标签本身
def crop_roi(image, label):
    assert(image.shape == label.shape)
    print(f'Before crop: {image.shape}')
    ### crop based on lung segmentation
    w, h, d = label.shape

    tempL = np.nonzero(label)
    minx, maxx = np.min(tempL[0]), np.max(tempL[0])
    miny, maxy = np.min(tempL[1]), np.max(tempL[1])
    minz, maxz = np.min(tempL[2]), np.max(tempL[2])
    # print('minz, maxz: ', minz, maxz)
    # input()

    # px py pz 是需要扩展的边界的长度
    px = max(output_size[0] - (maxx - minx), 0) // 2    # DCB: 扩展边界，防止连预设的96 96 96 都不够？
    py = max(output_size[1] - (maxy - miny), 0) // 2
    pz = max(output_size[2] - (maxz - minz), 0) // 2
    minx = max(minx - px - 25, 0) #np.random.randint(5, 10)
    maxx = min(maxx + px + 25, w) #np.random.randint(5, 10)
    miny = max(miny - py - 25, 0)
    maxy = min(maxy + py + 25, h)
    minz = max(minz - pz - 25, 0)
    maxz = min(maxz + pz + 25, d)
    
    image = image[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 不知道为什么，原始的代码只在xy这两个轴上做crop，不管z轴
    label = label[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 这里进行了修改使得在z轴上也进行裁剪
    return image, label


output_size =[96,96,96]
base_path = '/home/<USER>/data/tumor/Dataset017_Liver'    # 改
list_label = glob(f'{base_path}/imagesTr/*')
dataset_name = 'Liver'                                         # 改

HU_window_dic = {
        'Lung': [-1000, 1000],
        'HepaticVessel': [0, 230],    # 221 和 178 的image和label的spacing不细小差异导致resample的new size不一样，单独处理用tmp.py处理
        'Spleen': [-125, 275],
        'Colon': [-57, 175],
        'Pancreas': [-87, 199],
        'Liver': [-200, 300],           # 41 和 34 和 89 没有肿瘤,在train.list里也手动剔除了， 85 的image和label的spacing不细小差异导致resample的new size不一样，单独处理用tmp.py处理
        'KiTS2023': [-79,304],   # https://blog.csdn.net/h201601060805/article/details/109197644
    }

for item in tqdm(list_label):
    name_image = str(item)
    name_label = name_image.replace('imagesTr', 'labelsTr')   # 注意一下是否需要修改 如果label有多个标签处理后
    if name_image == '/home/<USER>/data/tumor/Dataset017_Liver/imagesTr/liver_85.nii.gz':
        continue
    print('Processing image: ', name_image)
    print('Processing label: ', name_label)
#     pdb.set_trace
    itk_img = sitk.ReadImage(name_image)
    itk_img = ImageResample(itk_img)
    image = sitk.GetArrayFromImage(itk_img)
    image = np.transpose(image, (2,1,0))
    
    itk_label = sitk.ReadImage(name_label)
    itk_label = ImageResample(itk_label, is_label = True)
    label = sitk.GetArrayFromImage(itk_label)
    label = np.transpose(label, (2,1,0))

    # print(np.max(label))
    # try:
    #     if np.max(label) != 1:
    #         with open("max_label_errors.txt", "w") as f:
    #             f.write(f"{name_image}  max(label): {np.max(label)} \n")
    #         continue
    #     assert np.min(label) == 0, "Minimum value of label is not 0"
    # except AssertionError as e:
    #     print(f"Assertion failed: {e}")
    #     # 这里可以添加额外的错误处理逻辑
    # except Exception as e:
    #     print(f"An unexpected error occurred: {e}")
    # assert(np.max(label) == 1 and np.min(label) == 0)
    print(f'Unique values in label: {np.unique(label)}')
    # assert(np.unique(label).shape[0] == 2)
    # print(f'Image shape: {image.shape}, Label shape: {label.shape}')
    assert(np.shape(label)==np.shape(image))

    image = set_window_wl_ww(image, HU_window_dic[dataset_name])
#     print(image.shape)
#     plt.figure(figsize=(10, 10))
#     plt.title('CT Slice_enhanced_100')
#     plt.imshow(image[:,:,100],cmap='gray')
#     plt.show()
    image, label = crop_roi(image, label)
    # image = (image - np.mean(image)) / np.std(image)
    print(f'After crop: {image.shape}')
    
    os.makedirs(f'{base_path}/h5/', exist_ok=True)
    base_name = os.path.basename(name_image)
    f = h5py.File((f'{base_path}/h5/'+base_name[:-7] + '.h5'), 'w')
    f.create_dataset('image', data=image, compression="gzip")
    f.create_dataset('label', data=label, compression="gzip")
    f.close()

