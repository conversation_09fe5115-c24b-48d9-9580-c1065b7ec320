#!/usr/bin/env python3
"""
检查Liver数据集NIfTI文件的spacing信息
"""

import nibabel as nib
import numpy as np
import os

def get_nifti_spacing(file_path):
    """
    获取NIfTI文件的spacing信息
    
    Args:
        file_path (str): NIfTI文件路径
    
    Returns:
        dict: 包含spacing和其他元数据的字典
    """
    try:
        # 加载NIfTI文件
        nii = nib.load(file_path)
        
        # 获取header信息
        header = nii.header
        
        # 获取affine矩阵
        affine = nii.affine
        
        # 获取spacing (pixdim)
        pixdim = header.get_zooms()  # 这是最准确的方法获取spacing
        
        # 获取数据形状
        shape = nii.shape
        
        # 获取数据类型
        dtype = header.get_data_dtype()
        
        # 获取方向信息
        orientation = nib.aff2axcodes(affine)
        
        return {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "shape": shape,
            "spacing": pixdim,
            "dtype": str(dtype),
            "orientation": orientation,
            "affine": affine,
            "header_info": {
                "dim": header['dim'].tolist(),
                "pixdim": header['pixdim'].tolist(),
                "qform_code": int(header['qform_code']),
                "sform_code": int(header['sform_code']),
            }
        }
        
    except Exception as e:
        return {"error": f"Error reading {file_path}: {str(e)}"}

def print_spacing_info(info):
    """
    打印spacing信息
    """
    if "error" in info:
        print(f"❌ {info['error']}")
        return
    
    print(f"📁 文件: {info['file_name']}")
    print(f"📍 路径: {info['file_path']}")
    print(f"📊 形状: {info['shape']}")
    print(f"📏 Spacing: {info['spacing']}")
    print(f"   - X方向 (宽度): {info['spacing'][0]:.6f} mm")
    print(f"   - Y方向 (高度): {info['spacing'][1]:.6f} mm")
    print(f"   - Z方向 (层厚): {info['spacing'][2]:.6f} mm")
    print(f"🧭 方向: {info['orientation']}")
    print(f"🔢 数据类型: {info['dtype']}")
    
    # 计算体素体积
    voxel_volume = np.prod(info['spacing'])
    print(f"📦 体素体积: {voxel_volume:.6f} mm³")
    
    # 计算总体积
    total_volume = np.prod(info['shape']) * voxel_volume
    print(f"📦 总体积: {total_volume:.2f} mm³ = {total_volume/1000:.2f} cm³")
    
    print(f"\n🔍 Header详细信息:")
    print(f"   dim: {info['header_info']['dim']}")
    print(f"   pixdim: {info['header_info']['pixdim']}")
    print(f"   qform_code: {info['header_info']['qform_code']}")
    print(f"   sform_code: {info['header_info']['sform_code']}")
    
    print(f"\n🔍 Affine矩阵:")
    affine = info['affine']
    for i in range(4):
        row_str = "   [" + ", ".join([f"{affine[i,j]:8.3f}" for j in range(4)]) + "]"
        print(row_str)

def main():
    """主函数"""
    print("🏥 Liver数据集Spacing信息检查")
    print("=" * 60)
    
    # 要检查的文件
    image_dir = "/home/<USER>/data/tumor/Dataset017_Liver/imagesTr"
    files_to_check = ["liver_1.nii.gz", "liver_5.nii.gz"]
    
    for file_name in files_to_check:
        file_path = os.path.join(image_dir, file_name)
        
        print(f"\n🔍 检查文件: {file_name}")
        print("-" * 50)
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        info = get_nifti_spacing(file_path)
        print_spacing_info(info)
        
        if len(files_to_check) > 1 and file_name != files_to_check[-1]:
            print("\n" + "=" * 60)
    
    # 比较两个文件的spacing
    print(f"\n📊 Spacing比较")
    print("=" * 60)
    
    all_info = []
    for file_name in files_to_check:
        file_path = os.path.join(image_dir, file_name)
        if os.path.exists(file_path):
            info = get_nifti_spacing(file_path)
            if "error" not in info:
                all_info.append(info)
    
    if len(all_info) >= 2:
        print(f"📋 Spacing对比:")
        for i, info in enumerate(all_info):
            print(f"   {info['file_name']}: {info['spacing']}")
        
        # 检查spacing是否相同
        spacings = [info['spacing'] for info in all_info]
        if all(np.allclose(spacings[0], spacing, rtol=1e-6) for spacing in spacings[1:]):
            print(f"✅ 所有文件的spacing相同")
        else:
            print(f"⚠️  文件间spacing不同")
            
        # 检查形状是否相同
        shapes = [info['shape'] for info in all_info]
        if all(shapes[0] == shape for shape in shapes[1:]):
            print(f"✅ 所有文件的形状相同")
        else:
            print(f"⚠️  文件间形状不同")
            for i, info in enumerate(all_info):
                print(f"     {info['file_name']}: {info['shape']}")

if __name__ == "__main__":
    main()
