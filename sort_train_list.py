#!/usr/bin/env python3
"""
重新排序2d_train.list文件，按照liver_病人号_切片号的顺序
病人号优先级高于切片号
"""

import os
import re

def extract_patient_and_slice_id(file_path):
    """
    从文件路径中提取病人ID和切片ID
    例如: liver_123_045.h5 -> (123, 45)
    """
    filename = os.path.basename(file_path)
    # 使用正则表达式匹配 liver_数字_数字.h5 的模式
    match = re.match(r'liver_(\d+)_(\d+)\.h5', filename)
    if match:
        patient_id = int(match.group(1))
        slice_id = int(match.group(2))
        return (patient_id, slice_id)
    return None

def sort_train_list(input_file, output_file=None):
    """
    排序训练列表文件
    """
    if output_file is None:
        output_file = input_file
    
    print(f"🔍 读取文件: {input_file}")
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    # 读取所有文件路径
    file_paths = []
    with open(input_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                file_paths.append(line)
    
    print(f"📊 读取到 {len(file_paths)} 个文件路径")
    
    # 提取排序键并排序
    valid_paths = []
    invalid_paths = []
    
    for path in file_paths:
        ids = extract_patient_and_slice_id(path)
        if ids:
            patient_id, slice_id = ids
            valid_paths.append((patient_id, slice_id, path))
        else:
            invalid_paths.append(path)
            print(f"⚠️  无法解析的文件路径: {path}")
    
    # 按照病人号优先，切片号次之的顺序排序
    valid_paths.sort(key=lambda x: (x[0], x[1]))
    
    print(f"✅ 成功解析 {len(valid_paths)} 个有效路径")
    if invalid_paths:
        print(f"⚠️  {len(invalid_paths)} 个无效路径")
    
    # 写入排序后的文件
    print(f"💾 写入排序后的文件: {output_file}")
    
    with open(output_file, 'w') as f:
        for patient_id, slice_id, path in valid_paths:
            f.write(path + '\n')
        
        # 如果有无效路径，也写入文件末尾
        for path in invalid_paths:
            f.write(path + '\n')
    
    print(f"✅ 排序完成!")
    
    # 显示排序前后的对比
    print(f"\n📋 排序结果预览 (前10个):")
    for i, (patient_id, slice_id, path) in enumerate(valid_paths[:10]):
        filename = os.path.basename(path)
        print(f"   {i+1:2d}. {filename}")
    
    if len(valid_paths) > 10:
        print(f"   ... (还有 {len(valid_paths)-10} 个文件)")
    
    # 显示病人分布
    patient_counts = {}
    for patient_id, slice_id, path in valid_paths:
        if patient_id not in patient_counts:
            patient_counts[patient_id] = 0
        patient_counts[patient_id] += 1
    
    print(f"\n📊 病人分布 (前10个):")
    sorted_patients = sorted(patient_counts.items())
    for i, (patient_id, count) in enumerate(sorted_patients[:10]):
        print(f"   liver_{patient_id}: {count} 个切片")
    
    if len(sorted_patients) > 10:
        print(f"   ... (还有 {len(sorted_patients)-10} 个病人)")
    
    return {
        "total_files": len(valid_paths),
        "unique_patients": len(patient_counts),
        "invalid_files": len(invalid_paths)
    }

def main():
    """主函数"""
    input_file = "/home/<USER>/data/tumor/split/2d_train.list"
    
    print("🔄 2D训练列表排序")
    print("=" * 50)
    print("排序规则: liver_病人号_切片号")
    print("优先级: 病人号 > 切片号")
    print("=" * 50)
    
    # 创建备份
    backup_file = input_file + ".backup"
    if os.path.exists(input_file):
        print(f"💾 创建备份: {backup_file}")
        with open(input_file, 'r') as src, open(backup_file, 'w') as dst:
            dst.write(src.read())
    
    # 排序文件
    result = sort_train_list(input_file)
    
    if result:
        print(f"\n🎉 排序完成!")
        print(f"📊 处理了 {result['total_files']} 个有效文件")
        print(f"📊 涉及 {result['unique_patients']} 个不同病人")
        if result['invalid_files'] > 0:
            print(f"⚠️  {result['invalid_files']} 个无效文件")
        print(f"💾 备份文件: {backup_file}")

if __name__ == "__main__":
    main()
