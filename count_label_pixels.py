#!/usr/bin/env python3
"""
Script to count pixels with specific label values in H5 files
"""

import h5py
import numpy as np
import os
import argparse
from pathlib import Path

def count_label_pixels(h5_file_path, label_value=2, dataset_name='label'):
    """
    Count pixels with specific label value in H5 file
    
    Args:
        h5_file_path (str): Path to H5 file
        label_value (int): Label value to count (default: 2)
        dataset_name (str): Name of the label dataset in H5 file (default: 'label')
    
    Returns:
        int: Number of pixels with the specified label value
    """
    
    if not os.path.exists(h5_file_path):
        raise FileNotFoundError(f"H5 file not found: {h5_file_path}")
    
    try:
        with h5py.File(h5_file_path, 'r') as f:
            # Check if label dataset exists
            if dataset_name not in f.keys():
                available_keys = list(f.keys())
                raise KeyError(f"Dataset '{dataset_name}' not found in H5 file. Available datasets: {available_keys}")
            
            # Load label data
            label_data = f[dataset_name][:]
            
            # Count pixels with specified label value
            pixel_count = np.sum(label_data == label_value)
            
            # Get additional information
            total_pixels = label_data.size
            unique_labels = np.unique(label_data)
            data_shape = label_data.shape
            data_dtype = label_data.dtype
            
            return {
                'pixel_count': int(pixel_count),
                'total_pixels': int(total_pixels),
                'percentage': float(pixel_count / total_pixels * 100),
                'data_shape': data_shape,
                'data_dtype': str(data_dtype),
                'unique_labels': unique_labels.tolist(),
                'label_value': label_value
            }
            
    except Exception as e:
        raise Exception(f"Error reading H5 file: {str(e)}")

def count_all_labels(h5_file_path, dataset_name='label'):
    """
    Count pixels for all label values in H5 file
    
    Args:
        h5_file_path (str): Path to H5 file
        dataset_name (str): Name of the label dataset in H5 file
    
    Returns:
        dict: Dictionary with counts for each label value
    """
    
    if not os.path.exists(h5_file_path):
        raise FileNotFoundError(f"H5 file not found: {h5_file_path}")
    
    try:
        with h5py.File(h5_file_path, 'r') as f:
            if dataset_name not in f.keys():
                available_keys = list(f.keys())
                raise KeyError(f"Dataset '{dataset_name}' not found in H5 file. Available datasets: {available_keys}")
            
            label_data = f[dataset_name][:]
            unique_labels, counts = np.unique(label_data, return_counts=True)
            
            total_pixels = label_data.size
            data_shape = label_data.shape
            
            results = {
                'total_pixels': int(total_pixels),
                'data_shape': data_shape,
                'label_counts': {}
            }
            
            for label, count in zip(unique_labels, counts):
                results['label_counts'][int(label)] = {
                    'count': int(count),
                    'percentage': float(count / total_pixels * 100)
                }
            
            return results
            
    except Exception as e:
        raise Exception(f"Error reading H5 file: {str(e)}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Count pixels with specific label values in H5 files')
    parser.add_argument('h5_file', help='Path to H5 file')
    parser.add_argument('--label', '-l', type=int, default=2, help='Label value to count (default: 2)')
    parser.add_argument('--dataset', '-d', default='label', help='Dataset name in H5 file (default: label)')
    parser.add_argument('--all', '-a', action='store_true', help='Count all label values')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    try:
        if args.all:
            # Count all labels
            results = count_all_labels(args.h5_file, args.dataset)
            
            print(f"H5 File: {args.h5_file}")
            print(f"Dataset: {args.dataset}")
            print(f"Data Shape: {results['data_shape']}")
            print(f"Total Pixels: {results['total_pixels']:,}")
            print("\nLabel Counts:")
            print("-" * 40)
            
            for label_val, info in results['label_counts'].items():
                print(f"Label {label_val}: {info['count']:,} pixels ({info['percentage']:.2f}%)")
                
        else:
            # Count specific label
            results = count_label_pixels(args.h5_file, args.label, args.dataset)
            
            print(f"H5 File: {args.h5_file}")
            print(f"Dataset: {args.dataset}")
            print(f"Target Label: {args.label}")
            print(f"Pixel Count: {results['pixel_count']:,}")
            
            if args.verbose:
                print(f"Total Pixels: {results['total_pixels']:,}")
                print(f"Percentage: {results['percentage']:.2f}%")
                print(f"Data Shape: {results['data_shape']}")
                print(f"Data Type: {results['data_dtype']}")
                print(f"Unique Labels: {results['unique_labels']}")
                
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    
    return 0

# Quick function for the specific file mentioned
def check_liver_h5():
    """Quick check for the specific liver H5 file"""
    h5_file_path = "/home/<USER>/data/tumor/Dataset017_Liver/h5/liver_0.h5"
    
    print("Checking specific file: liver_0.h5")
    print("=" * 50)
    
    try:
        # Check if file exists
        if not os.path.exists(h5_file_path):
            print(f"File not found: {h5_file_path}")
            return
        
        # Count label=2 pixels
        results = count_label_pixels(h5_file_path, label_value=2)
        
        print(f"File: {h5_file_path}")
        print(f"Label=2 pixel count: {results['pixel_count']:,}")
        print(f"Total pixels: {results['total_pixels']:,}")
        print(f"Percentage: {results['percentage']:.2f}%")
        print(f"Data shape: {results['data_shape']}")
        print(f"Available labels: {results['unique_labels']}")
        
        # Also show all label counts
        print("\nAll label counts:")
        all_results = count_all_labels(h5_file_path)
        for label_val, info in all_results['label_counts'].items():
            print(f"  Label {label_val}: {info['count']:,} pixels ({info['percentage']:.2f}%)")
            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    # If no command line arguments, run the quick check for liver_0.h5
    import sys
    if len(sys.argv) == 1:
        check_liver_h5()
    else:
        exit(main())
