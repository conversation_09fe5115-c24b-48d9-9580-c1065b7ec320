#!/usr/bin/env python3
"""
比较两个H5文件的label形状
"""

import h5py
import numpy as np
import os

def check_h5_label_shape(h5_path):
    """检查H5文件的label形状"""
    print(f"🔍 检查文件: {h5_path}")
    
    if not os.path.exists(h5_path):
        print(f"❌ 文件不存在: {h5_path}")
        return None
    
    try:
        with h5py.File(h5_path, 'r') as f:
            print(f"📊 数据集: {list(f.keys())}")
            
            if 'label' in f:
                label = f['label']
                label_data = label[:]
                print(f"   Label形状: {label.shape}")
                print(f"   Label数据类型: {label.dtype}")
                print(f"   Label唯一值: {np.unique(label_data)}")
                print(f"   Label值范围: [{np.min(label_data)}, {np.max(label_data)}]")
                
                return {
                    'shape': label.shape,
                    'dtype': label.dtype,
                    'unique_values': np.unique(label_data),
                    'min_val': np.min(label_data),
                    'max_val': np.max(label_data)
                }
            else:
                print(f"❌ 文件中没有'label'数据集")
                return None
                
    except Exception as e:
        print(f"❌ 读取文件出错: {str(e)}")
        return None

def main():
    """主函数"""
    # 要比较的两个文件
    file1 = "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d/liver_0_002.h5"
    file2 = "/home/<USER>/data/ACDC/data/slices/patient001_frame01_slice_3.h5"
    
    print("🔍 比较两个H5文件的label形状")
    print("=" * 70)
    
    # 检查第一个文件
    print(f"\n📁 文件1: {os.path.basename(file1)}")
    print("-" * 50)
    result1 = check_h5_label_shape(file1)
    
    # 检查第二个文件
    print(f"\n📁 文件2: {os.path.basename(file2)}")
    print("-" * 50)
    result2 = check_h5_label_shape(file2)
    
    # 比较结果
    print(f"\n📊 比较结果")
    print("=" * 70)
    
    if result1 is not None and result2 is not None:
        # 比较形状
        shapes_match = result1['shape'] == result2['shape']
        print(f"🔍 形状比较:")
        print(f"   文件1 label形状: {result1['shape']}")
        print(f"   文件2 label形状: {result2['shape']}")
        print(f"   形状是否相同: {'✅ 是' if shapes_match else '❌ 否'}")
        
        # 比较数据类型
        dtype_match = result1['dtype'] == result2['dtype']
        print(f"\n🔍 数据类型比较:")
        print(f"   文件1 数据类型: {result1['dtype']}")
        print(f"   文件2 数据类型: {result2['dtype']}")
        print(f"   数据类型是否相同: {'✅ 是' if dtype_match else '❌ 否'}")
        
        # 比较唯一值
        unique1_set = set(result1['unique_values'])
        unique2_set = set(result2['unique_values'])
        unique_match = unique1_set == unique2_set
        print(f"\n🔍 标签值比较:")
        print(f"   文件1 唯一值: {result1['unique_values']}")
        print(f"   文件2 唯一值: {result2['unique_values']}")
        print(f"   标签值是否相同: {'✅ 是' if unique_match else '❌ 否'}")
        
        # 总结
        print(f"\n🎯 总结:")
        if shapes_match and dtype_match:
            print(f"✅ 两个文件的label格式完全兼容")
        elif shapes_match:
            print(f"⚠️  两个文件的label形状相同，但数据类型不同")
        else:
            print(f"❌ 两个文件的label格式不兼容")
            
    else:
        print(f"❌ 无法完成比较，因为有文件读取失败")

if __name__ == "__main__":
    main()
