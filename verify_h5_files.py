#!/usr/bin/env python3
"""
验证生成的H5文件内容
"""

import h5py
import numpy as np
import os

def verify_h5_file(h5_path):
    """验证H5文件内容"""
    print(f"🔍 验证文件: {os.path.basename(h5_path)}")
    
    try:
        with h5py.File(h5_path, 'r') as f:
            # 检查数据集
            print(f"📊 数据集: {list(f.keys())}")
            
            # 检查图像数据
            if 'image' in f:
                image = f['image'][:]
                print(f"   图像形状: {image.shape}")
                print(f"   图像数据类型: {image.dtype}")
                print(f"   图像值范围: [{np.min(image):.6f}, {np.max(image):.6f}]")
                print(f"   图像均值: {np.mean(image):.6f}")
            
            # 检查标签数据
            if 'label' in f:
                label = f['label'][:]
                print(f"   标签形状: {label.shape}")
                print(f"   标签数据类型: {label.dtype}")
                print(f"   标签唯一值: {np.unique(label)}")
                
                # 计算ROI百分比
                roi_pixels = np.sum(label > 0)
                total_pixels = label.size
                roi_percentage = roi_pixels / total_pixels
                print(f"   ROI百分比: {roi_percentage*100:.2f}%")
            
            # 检查属性
            print(f"🏷️  属性:")
            for key, value in f.attrs.items():
                print(f"   {key}: {value}")
                
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def main():
    """主函数"""
    h5_dir = "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d"
    
    # 选择几个文件进行验证
    test_files = [
        "liver_1_021.h5",  # liver_1的第21个有效切片
        "liver_1_001.h5",  # liver_1的第1个有效切片
        "liver_0_019.h5"   # liver_0的最后一个有效切片
    ]
    
    print("🔍 验证生成的H5文件")
    print("=" * 50)
    
    for filename in test_files:
        filepath = os.path.join(h5_dir, filename)
        if os.path.exists(filepath):
            verify_h5_file(filepath)
            print("-" * 50)
        else:
            print(f"❌ 文件不存在: {filename}")
            # 查找类似的文件
            similar_files = [f for f in os.listdir(h5_dir) if f.startswith(filename.split('_')[0] + '_' + filename.split('_')[1])]
            if similar_files:
                print(f"   类似文件: {similar_files[:3]}")  # 显示前3个
            print("-" * 50)

if __name__ == "__main__":
    main()
