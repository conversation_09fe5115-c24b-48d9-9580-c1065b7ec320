#!/usr/bin/env python3
"""
统计训练列表中不同病人的数量
"""

import os
import re
from collections import defaultdict

def extract_patient_id(file_path):
    """
    从文件路径中提取病人ID
    例如: liver_123_045.h5 -> liver_123
    """
    filename = os.path.basename(file_path)
    # 使用正则表达式匹配 liver_数字 的模式
    match = re.match(r'liver_(\d+)_\d+\.h5', filename)
    if match:
        return f"liver_{match.group(1)}"
    return None

def analyze_train_list(list_file):
    """
    分析训练列表文件
    """
    print(f"🔍 分析文件: {list_file}")
    
    if not os.path.exists(list_file):
        print(f"❌ 文件不存在: {list_file}")
        return
    
    patient_files = defaultdict(list)
    total_files = 0
    
    with open(list_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            total_files += 1
            patient_id = extract_patient_id(line)
            
            if patient_id:
                patient_files[patient_id].append(line)
            else:
                print(f"⚠️  第{line_num}行无法解析病人ID: {line}")
    
    # 统计结果
    unique_patients = len(patient_files)
    
    print(f"\n📊 统计结果:")
    print(f"   总文件数: {total_files:,}")
    print(f"   不同病人数: {unique_patients}")
    print(f"   平均每个病人的切片数: {total_files/unique_patients:.1f}")
    
    # 显示每个病人的切片数量分布
    slice_counts = [len(files) for files in patient_files.values()]
    slice_counts.sort()
    
    print(f"\n📈 切片数量分布:")
    print(f"   最少切片数: {min(slice_counts)}")
    print(f"   最多切片数: {max(slice_counts)}")
    print(f"   中位数切片数: {slice_counts[len(slice_counts)//2]}")
    
    # 显示前10个病人的详细信息
    print(f"\n📋 前10个病人的切片数量:")
    sorted_patients = sorted(patient_files.items(), key=lambda x: int(x[0].split('_')[1]))
    for i, (patient_id, files) in enumerate(sorted_patients[:10]):
        print(f"   {patient_id}: {len(files)} 个切片")
    
    if len(sorted_patients) > 10:
        print(f"   ... (还有 {len(sorted_patients)-10} 个病人)")
    
    # 显示切片数最多和最少的病人
    max_patient = max(patient_files.items(), key=lambda x: len(x[1]))
    min_patient = min(patient_files.items(), key=lambda x: len(x[1]))
    
    print(f"\n🔝 切片数最多的病人: {max_patient[0]} ({len(max_patient[1])} 个切片)")
    print(f"🔻 切片数最少的病人: {min_patient[0]} ({len(min_patient[1])} 个切片)")
    
    # 按切片数量分组统计
    slice_ranges = {
        "1-50": 0,
        "51-100": 0, 
        "101-150": 0,
        "151-200": 0,
        "200+": 0
    }
    
    for count in slice_counts:
        if count <= 50:
            slice_ranges["1-50"] += 1
        elif count <= 100:
            slice_ranges["51-100"] += 1
        elif count <= 150:
            slice_ranges["101-150"] += 1
        elif count <= 200:
            slice_ranges["151-200"] += 1
        else:
            slice_ranges["200+"] += 1
    
    print(f"\n📊 病人按切片数量分组:")
    for range_name, count in slice_ranges.items():
        percentage = count / unique_patients * 100
        print(f"   {range_name} 切片: {count} 个病人 ({percentage:.1f}%)")
    
    return {
        "total_files": total_files,
        "unique_patients": unique_patients,
        "patient_files": dict(patient_files),
        "slice_counts": slice_counts
    }

def main():
    """主函数"""
    list_file = "/home/<USER>/data/tumor/split/2d_train.list"
    
    print("🏥 训练列表病人统计")
    print("=" * 50)
    
    result = analyze_train_list(list_file)
    
    if result:
        print(f"\n✅ 分析完成!")
        print(f"📊 该训练列表包含 {result['unique_patients']} 个不同的病人")
        print(f"📊 总共 {result['total_files']:,} 个2D切片文件")

if __name__ == "__main__":
    main()
