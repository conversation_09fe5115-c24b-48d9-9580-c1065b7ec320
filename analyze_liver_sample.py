#!/usr/bin/env python3
"""
分析Liver数据集样本，计算按要求处理后能切出多少片
"""

import nibabel as nib
import numpy as np
import os
from pathlib import Path

def analyze_liver_sample(image_path, label_path, roi_threshold=0.05):
    """
    分析单个Liver样本，计算符合要求的切片数量
    
    Args:
        image_path (str): 图像文件路径
        label_path (str): 标签文件路径  
        roi_threshold (float): ROI区域阈值，默认5%
    
    Returns:
        dict: 分析结果
    """
    print(f"🔍 分析样本: {os.path.basename(image_path)}")
    
    # 加载图像和标签
    try:
        img_nii = nib.load(image_path)
        label_nii = nib.load(label_path)
        
        img_data = img_nii.get_fdata()
        label_data = label_nii.get_fdata()
        
        print(f"📊 原始图像形状: {img_data.shape}")
        print(f"📊 标签形状: {label_data.shape}")
        print(f"📊 图像数据类型: {img_data.dtype}")
        print(f"📊 标签数据类型: {label_data.dtype}")
        
        # 检查图像和标签形状是否匹配
        if img_data.shape != label_data.shape:
            print(f"⚠️  警告: 图像和标签形状不匹配!")
            return {"error": "Shape mismatch between image and label"}
        
        # 获取图像的基本统计信息
        print(f"📈 原始图像值范围: [{np.min(img_data):.2f}, {np.max(img_data):.2f}]")
        print(f"📈 原始图像均值: {np.mean(img_data):.2f}")
        print(f"📈 原始图像标准差: {np.std(img_data):.2f}")
        
        # 应用CT阈值处理 [-200, 200]
        img_clipped = np.clip(img_data, -200, 200)
        print(f"📈 阈值处理后值范围: [{np.min(img_clipped):.2f}, {np.max(img_clipped):.2f}]")
        
        # 归一化到[0, 1]
        img_normalized = (img_clipped - (-200)) / (200 - (-200))
        print(f"📈 归一化后值范围: [{np.min(img_normalized):.2f}, {np.max(img_normalized):.2f}]")
        
        # 分析标签
        unique_labels = np.unique(label_data)
        print(f"🏷️  标签中的唯一值: {unique_labels}")
        
        # 计算每个标签的像素数量
        for label_val in unique_labels:
            count = np.sum(label_data == label_val)
            percentage = count / label_data.size * 100
            print(f"   标签 {int(label_val)}: {count:,} 像素 ({percentage:.2f}%)")
        
        # 分析每个轴向切片
        total_slices = img_data.shape[2]  # 假设z轴是第3维
        valid_slices = 0
        slice_info = []
        
        print(f"\n🔍 分析 {total_slices} 个轴向切片...")
        
        for z in range(total_slices):
            img_slice = img_normalized[:, :, z]
            label_slice = label_data[:, :, z]
            
            # 计算ROI区域（非背景区域）
            # 假设标签0是背景，其他都是ROI
            roi_mask = label_slice > 0
            roi_pixels = np.sum(roi_mask)
            total_pixels = label_slice.size
            roi_percentage = roi_pixels / total_pixels
            
            slice_info.append({
                'slice_idx': z,
                'roi_pixels': roi_pixels,
                'total_pixels': total_pixels,
                'roi_percentage': roi_percentage,
                'valid': roi_percentage >= roi_threshold
            })
            
            if roi_percentage >= roi_threshold:
                valid_slices += 1
        
        print(f"\n📊 切片分析结果:")
        print(f"   总切片数: {total_slices}")
        print(f"   有效切片数 (ROI >= {roi_threshold*100}%): {valid_slices}")
        print(f"   有效切片比例: {valid_slices/total_slices*100:.1f}%")
        
        # 显示ROI百分比分布
        roi_percentages = [info['roi_percentage'] for info in slice_info]
        print(f"\n📈 ROI百分比统计:")
        print(f"   最小ROI%: {np.min(roi_percentages)*100:.2f}%")
        print(f"   最大ROI%: {np.max(roi_percentages)*100:.2f}%")
        print(f"   平均ROI%: {np.mean(roi_percentages)*100:.2f}%")
        print(f"   ROI%标准差: {np.std(roi_percentages)*100:.2f}%")
        
        # 显示一些具体的切片信息
        print(f"\n📋 前10个切片的ROI信息:")
        for i in range(min(10, len(slice_info))):
            info = slice_info[i]
            status = "✅" if info['valid'] else "❌"
            print(f"   切片 {info['slice_idx']:3d}: ROI={info['roi_percentage']*100:5.1f}% {status}")
        
        if len(slice_info) > 10:
            print(f"   ... (还有 {len(slice_info)-10} 个切片)")
        
        # 找出ROI最多和最少的切片
        max_roi_slice = max(slice_info, key=lambda x: x['roi_percentage'])
        min_roi_slice = min(slice_info, key=lambda x: x['roi_percentage'])
        
        print(f"\n🔝 ROI最多的切片: 第{max_roi_slice['slice_idx']}片, ROI={max_roi_slice['roi_percentage']*100:.1f}%")
        print(f"🔻 ROI最少的切片: 第{min_roi_slice['slice_idx']}片, ROI={min_roi_slice['roi_percentage']*100:.1f}%")
        
        return {
            "image_path": image_path,
            "label_path": label_path,
            "image_shape": img_data.shape,
            "total_slices": total_slices,
            "valid_slices": valid_slices,
            "valid_ratio": valid_slices / total_slices,
            "roi_threshold": roi_threshold,
            "unique_labels": unique_labels.tolist(),
            "slice_info": slice_info,
            "roi_stats": {
                "min": np.min(roi_percentages),
                "max": np.max(roi_percentages),
                "mean": np.mean(roi_percentages),
                "std": np.std(roi_percentages)
            }
        }
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return {"error": str(e)}

def main():
    """主函数"""
    print("🚀 开始运行Liver数据集分析脚本...")

    try:
        import nibabel as nib
        print("✅ nibabel导入成功")
    except ImportError as e:
        print(f"❌ nibabel导入失败: {e}")
        print("请安装nibabel: pip install nibabel")
        return

    try:
        # 数据路径
        image_dir = "/home/<USER>/data/tumor/Dataset017_Liver/imagesTr"
        label_dir = "/home/<USER>/data/tumor/Dataset017_Liver/labelsTr"

        print(f"📁 检查图像目录: {image_dir}")
        if not os.path.exists(image_dir):
            print(f"❌ 图像目录不存在: {image_dir}")
            return
        print("✅ 图像目录存在")

        print(f"📁 检查标签目录: {label_dir}")
        if not os.path.exists(label_dir):
            print(f"❌ 标签目录不存在: {label_dir}")
            return
        print("✅ 标签目录存在")

        # 分析多个样本
        sample_names = ["liver_0.nii.gz", "liver_1.nii.gz", "liver_2.nii.gz", "liver_5.nii.gz", "liver_10.nii.gz"]

        all_results = []

        for sample_name in sample_names:
            image_path = os.path.join(image_dir, sample_name)
            label_path = os.path.join(label_dir, sample_name)

            # 检查文件是否存在
            print(f"\n📄 检查样本: {sample_name}")
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                continue

            if not os.path.exists(label_path):
                print(f"❌ 标签文件不存在: {label_path}")
                continue

            print("✅ 文件存在，开始分析...")

            print("\n🏥 Liver数据集样本分析")
            print("=" * 60)
            print(f"📄 分析样本: {sample_name}")
            print("=" * 60)

            # 分析样本
            result = analyze_liver_sample(image_path, label_path, roi_threshold=0.05)

            if "error" not in result:
                print(f"\n✅ 分析完成!")
                print(f"📊 该样本按要求处理后可得到 {result['valid_slices']} 个有效的2D切片")
                print(f"📊 有效切片占比: {result['valid_ratio']*100:.1f}%")
                all_results.append(result)
            else:
                print(f"\n❌ 分析失败: {result['error']}")

        # 汇总统计
        if all_results:
            print("\n" + "="*80)
            print("📊 多样本汇总统计")
            print("="*80)

            total_slices = sum(r['total_slices'] for r in all_results)
            total_valid = sum(r['valid_slices'] for r in all_results)

            print(f"📈 分析样本数: {len(all_results)}")
            print(f"📈 总切片数: {total_slices}")
            print(f"📈 总有效切片数: {total_valid}")
            print(f"📈 整体有效切片比例: {total_valid/total_slices*100:.1f}%")

            # 每个样本的详细信息
            print(f"\n📋 各样本详细信息:")
            for i, result in enumerate(all_results):
                sample_name = os.path.basename(result['image_path'])
                print(f"   {sample_name}: {result['valid_slices']}/{result['total_slices']} ({result['valid_ratio']*100:.1f}%)")
        else:
            print("\n❌ 没有成功分析的样本")

    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
