{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'xlrd'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mxlrd\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpdb\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01mSimple<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'xlrd'"]}], "source": ["import numpy as np\n", "from glob import glob\n", "from tqdm import tqdm\n", "import h5py\n", "import nrrd\n", "import nibabel as nib\n", "import pandas as pd\n", "import xlrd\n", "import pdb\n", "import SimpleITK as sitk\n", "from skimage import transform, measure\n", "import os\n", "import pydicom\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "def load_scan(path):\n", "    temp = [pydicom.dcmread(path + f) for f in os.listdir(path)]\n", "    slices = [t for t in temp if t.Modality == 'CT']\n", "    slices.sort(key = lambda x: int(x.InstanceNumber))\n", "    try:\n", "        slice_thickness = np.abs(slices[0].ImagePositionPatient[2] - slices[1].ImagePositionPatient[2])\n", "    except:\n", "        slice_thickness = np.abs(slices[0].SliceLocation - slices[1].SliceLocation)      \n", "    for s in slices:\n", "        s.SliceThickness = slice_thickness        \n", "    return slices\n", "\n", "def get_pixels_hu(scans):\n", "    image = np.stack([s.pixel_array for s in scans]) \n", "    image = image.astype(np.float32)\n", "    # Convert to Hounsfield units (HU)\n", "    intercept = scans[0].RescaleIntercept\n", "    slope = scans[0].RescaleSlope\n", "    \n", "    if slope != 1:\n", "        image = slope * image.astype(np.float32)\n", "        image = image.astype(np.float32)\n", "        \n", "    image += np.float32(intercept)\n", "    \n", "    return np.array(image, dtype=np.float32)\n", "\n", "listt = glob('./Pancreas-CT/Pancreas-CT/*/*/Pancreas-*/')\n", "base_dir = \"./TCIA_pancreas_labels-02-05-2017/TCIA_pancreas_labels-02-05-2017/\"\n", "\n", "for item in tqdm(listt):\n", "    name = str(item)\n", "    name_id = name[35:39]\n", "    patient_ct = load_scan(name)\n", "    imgs_ct = get_pixels_hu(patient_ct)\n", "\n", "    itk_img = sitk.ReadImage(base_dir +'label'+ name_id + '.nii.gz')\n", "    origin = itk_img.GetO<PERSON>in()\n", "    direction = itk_img.GetDirection()\n", "    space = itk_img.GetSpacing()\n", "    label = sitk.GetArrayFromImage(itk_img)\n", "    \n", "\n", "    image_gz = sitk.GetImageFromArray(imgs_ct)\n", "    image_gz.<PERSON><PERSON><PERSON>(origin)\n", "    image_gz.SetDirection(direction)\n", "    image_gz.SetSpacing(space)\n", "    sitk.WriteImage(image_gz, \"./image\"+name_id+\".nii.gz\")\n", "#     plt.figure(figsize=(10, 10))\n", "#     plt.title('CT Slice_10')\n", "#     plt.imshow(imgs_ct[9],cmap='gray')\n", "#     plt.axis('off')\n", "#     plt.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"start_time": "2021-07-08T07:22:32.650Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/82 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n", "(179, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  1%|          | 1/82 [00:08<11:39,  8.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 201]\n", "[400, 400, 201]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 201)\n", "(170, 145, 201)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  2%|▏         | 2/82 [00:13<08:50,  6.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 205]\n", "[419, 419, 205]\n", "[0.81999999 0.81999999 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(419, 419, 205)\n", "(216, 145, 205)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  4%|▎         | 3/82 [00:19<08:01,  6.09s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 205]\n", "[420, 420, 205]\n", "[0.8203125 0.8203125 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(420, 420, 205)\n", "(200, 145, 205)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  5%|▍         | 4/82 [00:24<07:35,  5.84s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 223]\n", "[420, 420, 223]\n", "[0.8203125 0.8203125 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(420, 420, 223)\n", "(174, 145, 223)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  6%|▌         | 5/82 [00:30<07:25,  5.79s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 210]\n", "[439, 439, 210]\n", "[0.85799998 0.85799998 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(439, 439, 210)\n", "(181, 145, 210)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  7%|▋         | 6/82 [00:36<07:31,  5.94s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 213]\n", "[460, 460, 213]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 213)\n", "(156, 145, 213)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  9%|▊         | 7/82 [00:42<07:29,  6.00s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n", "(210, 146, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 10%|▉         | 8/82 [00:50<08:11,  6.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 233]\n", "[500, 500, 233]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 233)\n", "(187, 146, 233)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 11%|█         | 9/82 [00:58<08:21,  6.87s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 209]\n", "[400, 400, 209]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 209)\n", "(184, 146, 209)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 12%|█▏        | 10/82 [01:04<07:56,  6.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 205]\n", "[400, 400, 205]\n", "[0.78200001 0.78200001 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 205)\n", "(188, 145, 205)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 13%|█▎        | 11/82 [01:10<07:33,  6.38s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 216]\n", "[340, 340, 216]\n", "[0.6640625 0.6640625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(340, 340, 216)\n", "(146, 146, 216)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 15%|█▍        | 12/82 [01:15<06:59,  5.99s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 251]\n", "[500, 500, 251]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 251)\n", "(213, 145, 251)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 16%|█▌        | 13/82 [01:22<07:19,  6.37s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 209]\n", "[440, 440, 209]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 209)\n", "(191, 146, 209)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 17%|█▋        | 14/82 [01:27<06:56,  6.12s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 215]\n", "[500, 500, 215]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 215)\n", "(191, 146, 215)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 18%|█▊        | 15/82 [01:34<06:50,  6.13s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[380, 380, 310]\n", "[0.7421875 0.7421875 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 310)\n", "(146, 151, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|█▉        | 16/82 [01:41<07:12,  6.55s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 210]\n", "[460, 460, 210]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 210)\n", "(188, 146, 210)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 21%|██        | 17/82 [01:48<07:03,  6.51s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[380, 380, 310]\n", "[0.7421875 0.7421875 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 310)\n", "(155, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 22%|██▏       | 18/82 [01:56<07:23,  6.93s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 247]\n", "[400, 400, 247]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 247)\n", "(192, 147, 247)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 23%|██▎       | 19/82 [02:02<07:07,  6.78s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 201]\n", "[420, 420, 201]\n", "[0.8203125 0.8203125 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(420, 420, 201)\n", "(175, 146, 201)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 24%|██▍       | 20/82 [02:07<06:29,  6.29s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 201]\n", "[480, 480, 201]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 201)\n", "(199, 146, 201)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 26%|██▌       | 21/82 [02:13<06:14,  6.15s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 198]\n", "[380, 380, 198]\n", "[0.7421875 0.7421875 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 198)\n", "(194, 146, 198)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 27%|██▋       | 22/82 [02:18<05:45,  5.75s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 223]\n", "[480, 480, 223]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 223)\n", "(216, 150, 223)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 28%|██▊       | 23/82 [02:25<06:02,  6.15s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 223]\n", "[500, 500, 223]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 223)\n", "(202, 146, 223)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 29%|██▉       | 24/82 [02:32<06:15,  6.47s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 231]\n", "[480, 480, 231]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 231)\n", "(153, 146, 231)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|███       | 25/82 [02:39<06:21,  6.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 223]\n", "[480, 480, 223]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 223)\n", "(175, 146, 223)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 26/82 [02:46<06:13,  6.67s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n", "(179, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 33%|███▎      | 27/82 [02:54<06:38,  7.24s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[360, 360, 310]\n", "[0.703125 0.703125 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(360, 360, 310)\n", "(190, 152, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 34%|███▍      | 28/82 [03:02<06:31,  7.26s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[470, 470, 310]\n", "[0.91796875 0.91796875 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(470, 470, 310)\n", "(205, 146, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 35%|███▌      | 29/82 [03:10<06:44,  7.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 201]\n", "[460, 460, 201]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 201)\n", "(201, 146, 201)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 37%|███▋      | 30/82 [03:16<06:02,  6.98s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 206]\n", "[460, 460, 206]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 206)\n", "(168, 146, 206)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 38%|███▊      | 31/82 [03:21<05:31,  6.51s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 218]\n", "[439, 439, 218]\n", "[0.85799998 0.85799998 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(439, 439, 218)\n", "(201, 145, 218)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 39%|███▉      | 32/82 [03:27<05:13,  6.27s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 218]\n", "[440, 440, 218]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 218)\n", "(183, 146, 218)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 33/82 [03:33<04:59,  6.11s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 206]\n", "[500, 500, 206]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 206)\n", "(210, 145, 206)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 41%|████▏     | 34/82 [03:39<04:58,  6.22s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 466]\n", "[485, 485, 233]\n", "[0.94726562 0.94726562 0.5       ]\n", "[1.0, 1.0, 1.0]\n", "(485, 485, 233)\n", "(204, 146, 233)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 43%|████▎     | 35/82 [03:50<05:55,  7.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 205]\n", "[400, 400, 205]\n", "[0.78200001 0.78200001 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 205)\n", "(198, 145, 205)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 44%|████▍     | 36/82 [03:55<05:13,  6.82s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 195]\n", "[460, 460, 195]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 195)\n", "(225, 146, 195)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 45%|████▌     | 37/82 [04:00<04:47,  6.38s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 222]\n", "[460, 460, 222]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 222)\n", "(163, 146, 222)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 46%|████▋     | 38/82 [04:06<04:34,  6.24s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 185]\n", "[360, 360, 185]\n", "[0.703125 0.703125 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(360, 360, 185)\n", "(179, 145, 185)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 48%|████▊     | 39/82 [04:10<04:04,  5.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 210]\n", "[460, 460, 210]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 210)\n", "(170, 146, 210)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 49%|████▉     | 40/82 [04:16<04:01,  5.74s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[360, 360, 310]\n", "[0.703125 0.703125 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(360, 360, 310)\n", "(175, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 41/82 [04:24<04:15,  6.24s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 216]\n", "[480, 480, 216]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 216)\n", "(215, 146, 216)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 51%|█████     | 42/82 [04:30<04:11,  6.30s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[360, 360, 310]\n", "[0.703125 0.703125 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(360, 360, 310)\n", "(168, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 52%|█████▏    | 43/82 [04:38<04:22,  6.74s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[340, 340, 310]\n", "[0.6640625 0.6640625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(340, 340, 310)\n", "(184, 146, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 54%|█████▎    | 44/82 [04:46<04:33,  7.21s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 221]\n", "[460, 460, 221]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 221)\n", "(212, 146, 221)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 55%|█████▍    | 45/82 [04:54<04:31,  7.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 216]\n", "[0.78125    0.78125    0.69999999]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 216)\n", "(176, 145, 216)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 56%|█████▌    | 46/82 [05:01<04:25,  7.38s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[380, 380, 310]\n", "[0.74218798 0.74218798 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 310)\n", "(182, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 57%|█████▋    | 47/82 [05:09<04:23,  7.53s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 240]\n", "[440, 440, 240]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 240)\n", "(188, 145, 240)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 59%|█████▊    | 48/82 [05:16<04:09,  7.35s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 223]\n", "[490, 490, 223]\n", "[0.95703125 0.95703125 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(490, 490, 223)\n", "(217, 146, 223)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|█████▉    | 49/82 [05:24<04:04,  7.40s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[350, 350, 310]\n", "[0.68359375 0.68359375 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(350, 350, 310)\n", "(166, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 61%|██████    | 50/82 [05:32<04:01,  7.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 237]\n", "[480, 480, 237]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 237)\n", "(224, 145, 237)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 62%|██████▏   | 51/82 [05:39<03:54,  7.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 204]\n", "[400, 400, 204]\n", "[0.78200001 0.78200001 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 204)\n", "(192, 146, 204)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 63%|██████▎   | 52/82 [05:45<03:30,  7.03s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n", "(210, 146, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 65%|██████▍   | 53/82 [05:53<03:33,  7.37s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 211]\n", "[481, 481, 211]\n", "[0.93945312 0.93945312 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(481, 481, 211)\n", "(175, 159, 211)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 66%|██████▌   | 54/82 [05:59<03:14,  6.96s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 194]\n", "[456, 456, 194]\n", "[0.890625 0.890625 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(456, 456, 194)\n", "(167, 146, 194)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 67%|██████▋   | 55/82 [06:04<02:53,  6.42s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 181]\n", "[500, 500, 181]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 181)\n", "(169, 146, 181)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 56/82 [06:09<02:35,  6.00s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[360, 360, 310]\n", "[0.703125 0.703125 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(360, 360, 310)\n", "(183, 146, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|██████▉   | 57/82 [06:17<02:40,  6.41s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 233]\n", "[440, 440, 233]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 233)\n", "(196, 146, 233)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 71%|███████   | 58/82 [06:23<02:31,  6.33s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 226]\n", "[480, 480, 226]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 226)\n", "(210, 145, 226)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 72%|███████▏  | 59/82 [06:29<02:27,  6.41s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 209]\n", "[500, 500, 209]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 209)\n", "(145, 158, 209)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 73%|███████▎  | 60/82 [06:35<02:17,  6.25s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 224]\n", "[460, 460, 224]\n", "[0.8984375 0.8984375 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(460, 460, 224)\n", "(198, 146, 224)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 74%|███████▍  | 61/82 [06:41<02:10,  6.22s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 186]\n", "[380, 380, 186]\n", "[0.7421875 0.7421875 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 186)\n", "(163, 146, 186)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 76%|███████▌  | 62/82 [06:46<01:54,  5.72s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 216]\n", "[500, 500, 216]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 216)\n", "(177, 145, 216)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 77%|███████▋  | 63/82 [06:52<01:50,  5.83s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 198]\n", "[400, 400, 198]\n", "[0.78200001 0.78200001 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 198)\n", "(171, 145, 198)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 78%|███████▊  | 64/82 [06:57<01:40,  5.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 187]\n", "[380, 380, 187]\n", "[0.7421875 0.7421875 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(380, 380, 187)\n", "(192, 145, 187)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 79%|███████▉  | 65/82 [07:02<01:29,  5.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 193]\n", "[350, 350, 193]\n", "[0.68359375 0.68359375 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(350, 350, 193)\n", "(166, 145, 193)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 66/82 [07:06<01:21,  5.08s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 199]\n", "[439, 439, 199]\n", "[0.85799998 0.85799998 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(439, 439, 199)\n", "(211, 146, 199)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 82%|████████▏ | 67/82 [07:12<01:17,  5.17s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n", "(178, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 83%|████████▎ | 68/82 [07:19<01:23,  5.94s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 221]\n", "[462, 462, 221]\n", "[0.90234375 0.90234375 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(462, 462, 221)\n", "(164, 146, 221)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 84%|████████▍ | 69/82 [07:25<01:17,  5.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 201]\n", "[480, 480, 201]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 201)\n", "(174, 146, 201)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 85%|████████▌ | 70/82 [07:31<01:10,  5.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 206]\n", "[475, 475, 206]\n", "[0.92773438 0.92773438 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(475, 475, 206)\n", "(202, 146, 206)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 87%|████████▋ | 71/82 [07:37<01:04,  5.88s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 227]\n", "[440, 440, 227]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 227)\n", "(188, 146, 227)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 88%|████████▊ | 72/82 [07:43<00:59,  5.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 199]\n", "[400, 400, 199]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 199)\n", "(160, 145, 199)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 89%|████████▉ | 73/82 [07:48<00:50,  5.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 196]\n", "[439, 439, 196]\n", "[0.85799998 0.85799998 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(439, 439, 196)\n", "(194, 146, 196)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 90%|█████████ | 74/82 [07:53<00:43,  5.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[440, 440, 310]\n", "[0.859375 0.859375 1.      ]\n", "[1.0, 1.0, 1.0]\n", "(440, 440, 310)\n", "(193, 145, 310)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 91%|█████████▏| 75/82 [08:01<00:43,  6.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 251]\n", "[400, 400, 251]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 251)\n", "(145, 145, 251)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 93%|█████████▎| 76/82 [08:07<00:37,  6.27s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 203]\n", "[481, 481, 203]\n", "[0.93945312 0.93945312 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(481, 481, 203)\n", "(190, 146, 203)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 94%|█████████▍| 77/82 [08:13<00:30,  6.09s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 214]\n", "[500, 500, 214]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 214)\n", "(188, 157, 214)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 95%|█████████▌| 78/82 [08:19<00:24,  6.06s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 206]\n", "[480, 480, 206]\n", "[0.9375 0.9375 1.    ]\n", "[1.0, 1.0, 1.0]\n", "(480, 480, 206)\n", "(184, 146, 206)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 96%|█████████▋| 79/82 [08:26<00:18,  6.27s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 221]\n", "[500, 500, 221]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 221)\n", "(151, 145, 221)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 98%|█████████▊| 80/82 [08:33<00:12,  6.41s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 228]\n", "[500, 500, 228]\n", "[0.9765625 0.9765625 1.       ]\n", "[1.0, 1.0, 1.0]\n", "(500, 500, 228)\n", "(201, 145, 228)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 99%|█████████▉| 81/82 [08:40<00:06,  6.62s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 203]\n", "[400, 400, 203]\n", "[0.78200001 0.78200001 1.        ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 203)\n", "(164, 145, 203)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 82/82 [08:45<00:00,  6.40s/it]\n"]}], "source": ["import numpy as np\n", "from glob import glob\n", "from tqdm import tqdm\n", "import h5py\n", "import nrrd\n", "import nibabel as nib\n", "import pandas as pd\n", "# import xlrd\n", "import pdb\n", "import SimpleITK as sitk\n", "from skimage import transform, measure\n", "import os\n", "import pydicom\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "output_size =[96,96,96]\n", "def ImageResample(sitk_image, new_spacing = [1,1,1], is_label = False):\n", "    '''\n", "    sitk_image:\n", "    new_spacing: x,y,z\n", "    is_label: if True, using Interpolator `sitk.sitkNearestNeighbor`\n", "    '''\n", "    size = np.array(sitk_image.GetSize())\n", "    spacing = np.array(sitk_image.GetSpacing())\n", "    new_spacing = np.array(new_spacing)\n", "    new_size = size * spacing / new_spacing\n", "    new_spacing_refine = size * spacing / new_size\n", "    new_spacing_refine = [float(s) for s in new_spacing_refine]\n", "    new_size = [int(s) for s in new_size]\n", "    if not is_label:\n", "        print(size)\n", "        print(new_size)\n", "        print(spacing)\n", "        print(new_spacing_refine)\n", "    resample = sitk.ResampleImageFilter()\n", "    resample.SetOutputDirection(sitk_image.GetDirection())\n", "    resample.SetOutputOrigin(sitk_image.GetOrigin())\n", "    resample.SetSize(new_size)\n", "    resample.SetOutputSpacing(new_spacing_refine)\n", "\n", "    if is_label:\n", "        resample.SetInterpolator(sitk.sitkNearestNeighbor)\n", "    else:\n", "        resample.SetInterpolator(sitk.sitkBSpline)\n", "    newimage = resample.Execute(sitk_image)\n", "    return newimage\n", "\n", "def set_window_wl_ww(tensor):\n", "    # sl_window = [75,400]\n", "    sl_window = [-125, 275]\n", "    [wl,ww] = sl_window\n", "    w_min, w_max = wl - ww//2, wl + ww//2\n", "    tensor[tensor < w_min] = w_min\n", "    tensor[tensor > w_max] = w_max\n", "    tensor = (tensor - w_min) / (w_max - w_min)\n", "    ### min max Normalization\n", "    return tensor\n", "\n", "# 根据标签图像（label）中的非零区域来裁剪图像（image）和标签本身\n", "def crop_roi(image, label):\n", "    assert(image.shape == label.shape)\n", "    print (image.shape)\n", "    ### crop based on lung segmentation\n", "    w, h, d = label.shape\n", "\n", "    tempL = np.nonzero(label)\n", "    minx, maxx = np.min(tempL[0]), np.max(tempL[0])\n", "    miny, maxy = np.min(tempL[1]), np.max(tempL[1])\n", "\n", "    px = max(output_size[0] - (maxx - minx), 0) // 2    # DCB: 扩展边界，防止连预设的96 96 96 都不够？\n", "    py = max(output_size[1] - (maxy - miny), 0) // 2\n", "    minx = max(minx - px - 25, 0) #np.random.randint(5, 10)\n", "    maxx = min(maxx + px + 25, w) #np.random.randint(5, 10)\n", "    miny = max(miny - py - 25, 0)\n", "    maxy = min(maxy + py + 25, h)\n", "    \n", "    image = image[minx:maxx, miny:maxy,:].astype(np.float32)\n", "    label = label[minx:maxx, miny:maxy,:].astype(np.float32)\n", "    return image, label\n", "\n", "listt = glob('Dataset090_Pancreas-CT/imagesTr/*')\n", "\n", "for item in tqdm(listt):\n", "    name_image = str(item)\n", "    name_label = name_image.replace('imagesTr', 'labelsTr_flipped')\n", "#     pdb.set_trace\n", "    itk_img = sitk.ReadImage(name_image)\n", "#     origin =itk_img.GetOrigin()\n", "#     direction = itk_img.GetDirection()\n", "#     space = itk_img.GetSpacing()\n", "    itk_img = ImageResample(itk_img)\n", "    image = sitk.GetArrayFromImage(itk_img)\n", "    image = np.transpose(image, (2,1,0))\n", "    \n", "    itk_label = sitk.ReadImage(name_label)\n", "    itk_label = ImageResample(itk_label, is_label = True)\n", "    label = sitk.GetArrayFromImage(itk_label)\n", "    label = np.transpose(label, (2,1,0))\n", "\n", "    assert(np.max(label) == 1 and np.min(label) == 0)\n", "    assert(np.unique(label).shape[0] == 2)\n", "    assert(np.shape(label)==np.shape(image))\n", "    image = set_window_wl_ww(image)\n", "#     print(image.shape)\n", "#     plt.figure(figsize=(10, 10))\n", "#     plt.title('CT Slice_enhanced_100')\n", "#     plt.imshow(image[:,:,100],cmap='gray')\n", "#     plt.show()\n", "    image, label = crop_roi(image, label)\n", "    image = (image - np.mean(image)) / np.std(image)\n", "    print(image.shape)\n", "    # os.makedirs('Pancreas_processed', exist_ok=True)\n", "    f = h5py.File(('Pancreas_h5/image_'+name_image[-11:-7] + '_norm.h5'), 'w')\n", "    f.create_dataset('image', data=image, compression=\"gzip\")\n", "    f.create_dataset('label', data=label, compression=\"gzip\")\n", "    f.close()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/82 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[512 512 310]\n", "[400, 400, 310]\n", "[0.78125 0.78125 1.     ]\n", "[1.0, 1.0, 1.0]\n", "(400, 400, 310)\n"]}, {"data": {"image/png": "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**********************************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********************************/4Mgn2KIATtlibF91HQaTabeuWVV5TL5TxUUQzEg+PY63nj+ChOsH+sbxyLJM8aSJ8Q26SB365tFuEgAbJ9PqBikhxlj/WR9TeXy3mQfFAPe035Pnqd7GgA//Xn/SRV0kR1jaGdVgUTdI/ExE+ps+s0TAN7rROJhLu+ew2uZn8sjY7EFz/A9Tl58qR6vd4d6xMCuzOs3W5rdXVVoVBIs7OztyzpC+7CwAL7BwuHd6Zn+2lfVgCAABlHCZqCQ4PuZCuC0o5TtnQI1Gsk7UIVqMZZJAFKQa/XU7fbdRVO64BwnJaOYekPvO5PrECL1tfX9d3vftfNA8JsomWbXzudjqsKW6WnO83YN/oSOIbrTXT8fVmBBXarjUCZQIAknXuY/4Me88O9vbm5qe3tbfdsEjCT0LAOIDtfq9VcH6AN7m2ju10/LG0JSq1dA1i/WCNbrZZefvllzc3NaWZmZtc6AaWUIHsvpcNQKOTQbSuwQCBvm/TtftLXyLoMEk3gz7Y5XlAtel0svQzkqt/vq91uu3WcdZd94vW91kOOw/YO+cVuQN4sssNnKaRZ4QkSX6uuBjOA7WSzWVew8suTc9yWvsjcHa4P/Z/+RIde0Gg06qiFp0+fVq/Xu641NrCDa61WSysrK1pYWHBx162wINEJLLB/sEgkojNnzujw4cOOcw432j+LgAWexlMrO+rvnSFowIn4URVbvbSOlmoYk88leSqtVkUHZaFisegCc1ttTSaTHrUk9gvHvbGx4UQOMMuFJ3HDCYfDYdVqNae6NjU1pbm5uTvSsXFuLcpFRTSwwO4kI6gdDAZuoKXtLyMh4P+2YGJ7O1iTCPz9ksk04PMsFwoFxWIxpdNpTx8b6BCzcWwfkLSzZhYKBRf8SpNGepIyf78hAim2wMP7rIrbteiido2cmZlx6l+INRDMs76xn6y9rFcMErXrKNLVUI1tkz4JBAkf6wfrJkUvaWftpVhmFcxAk0kqoNSC3luxB2nSu2OTDI6TXhp+OA4SWwpUltZn7xe2bftALTpnf5Dkxn9wH3DtuP94bWZmRuVy+Y7v39xvgzHAYNy9Evx72S5duqTPfe5zeutb3+rEn26FBYlOYIH9g9EgeOzYMaeIYydT4zSptFLFg2Ji+eU4fgv34yQtvx2zdI1oNOqU1KSdgIGEA6dkaWqRyM6E6mw2q3w+7wbhEeiMx2NPP5E1nGa1WnXVXkmuSplOpx1FhYCL4MhKvcbjcZXL5Tsy0ZEmYg9URK0aXmCB3SlGUYV5JgyztbNO7LNt1xjbL8PrdhAwCYIkT8ISiUSUzWYVj8eVyWTcoEc7QJIeoL0SHdAR1kh6QFD9Qm56OBwqnU677fsTHWhU4/H4NRu0STIKhYKbFWMTKrYpTSTlMVB5BjZSBOKzW1tbDtVm3y0CzDrK7/QmWTWzZDLpQUhY21F5Ay0BHZLkBpqiisdxknzgI8bjsWeeDgZCZZNOjt2KDnCOOScW5YEGh/lFFTBLmfQnOuFwWMViUcViMUh0XsMoPvBc2LlEB8HW19f11a9+VUtLS7f0e4JEJ7DA/sGouEGPsIkCDovKG3xmgvxsNusCFFuRw4lAJbCyrCQfOBAqoFT/pqen3TYIgGw1jmQoGo2qUChoZmbGSbiSnCQSCQ9Fw9pgMFC1WtX6+rpeeOEFtdttN/tC2oGVQa724pEXCgUVCgWlUil3Lu5E81dISQ7v1P0N7GAbSEu1WnUCBBiJTzQaVSaTUSqVUjwed4G5v5gBomP77KQJFcn2EkJnarfbrpcHI4Am2RmNRkqlUrvoVX66FL0e7G84HNbq6qry+byH6gUaAYq1sbHhAuZryVJHo1GVy2Ulk0knjc0+SRNFsW6366GukSzYZIeCFbL57XZbm5ub7pzaPhkrGgAyRL8Lr3M+rTomyRm+xdKNSUZAzkj6oKN1Oh3Ptjmn4XDYg/hzbe2+5vN5T08pSRL7xv3GWAN8ne3LQniAa8zxgkLUajWFw2HPMNhsNqsnnnhCL7zwgr72ta95WA8H1ba3t/XNb35TiURCDz30kBKJhJ544gktLS3pL//yLw+czPTq6qqefPJJPfbYY5K86rM304JEJ7DANOmboRLHom6VaiTv8DXrCBKJhBMJsJVU+14U0KwsKcEDjoRKm+0NITnCqRBU+FV7CoWCJykaDofKZDLXDOi3t7edWtDVq1c9dBg7iwOqB+cIS6VSyuVyrqJ7p5qVbSWxvJP3N7CDa5bq1Wq11Gw2Va1WXeDJc09hIZFIKJFIuICY9Ym1ARoSxQrQFp5tWwRg3aAP0Aa7FGAsCoAha0wAb4s8rG924G6lUnFJjKU7RaNRp3gGOoTi216JDlSvaDTqhpLaJnySLNZNjsv244BUQNtDMW1qasqtx/bckNxYyhc0wE6n40kq+Qznn3WY/eP8kOhBnbOJDuptDDOVJsiPRWhA2dmG7eHBp1m0z9KwuUc47mQyuUvZjfuLa815QUm03W4rkUh4EIlUKqVz584pGo3qm9/8ZpDoaOdcnz9/XrlcTqdOnVKhUNC5c+cUi8X0qU99ar9377ZbrVZzkvmS3H1nB3ffDAu8fWAH3iKRiN71rnfp8OHDOnz4sNLptKrVqqOjSfI4CUszozLWarXce0h0cM44SIIBqqNU1kgebOBB4mMrb8iZshD0+31X0bUNx9dbDWGgIIo5GN8PWmU54ZIc1aLVaimRSCidTt+0a3EzzVZhJe/MiMACu9OMpv1nn33WNdBDI/LTr5LJpBsgjAS+7ckhUKZKz5rFewjK/cpdIEaSXCIlTXqHLKWL5Inv5nN8hqo/2yQgrlQqGgwGmp2ddYkQlslkFI/HHUoxHA7VbDaVSqV2FSds0kGjfa1Wc8cuTYpAsVhM1WrVIenxeNzJXpfLZZfkUNSJRCKq1WrqdDquyGNRLY4TGhlrO4mnpTFLckg/wzhB90l+0um0E4tg/0mGSMRisZhb76G4cZ3YZ5IsEDe7ZuNPSKSROU6n04pEIsrlcpIm8tMMnSb4ZPtWmY+kp9FouGIe5zGdTuud73ynUqmU/vRP/9RDiTuoNhwO9elPf1rPPPOM3vGOd2h2dlaPPvqo8vl8ME9H0iOPPKKf/dmf1Re/+EW98MILN227QaIT2IG3cDisEydO6OzZs8rn857GTNvATqJj1cUIAGxFjc8SUOOQCLpJSAhKLIWAJAUaiVVww9Hyvf5GY8xy9q+lgsZ+t9ttz75DywCRssPucHaWd297l+40s+eI8xQkOYHdicbz3+/3tb6+7pIEq7bFPQzqy3wWi/Ta7UlylVGeUdt3g+oXgTEokl1rbFBLEYfA3qpD2n4RK3fNZ0BUOKZiseiSLmskAtKEnkuP4l6qjhQvksnkLjln9oFzBQ0X+mq323XCCJLc2k/SABXZ39zPfpA40qPD/tBP6e+dsus1iY+fHmb9jZ9um0wmHQ3NT1f0o230OVmp63a77fYBlMiqZqLiaYt1kjx+h+/AJ3GcJMm9Xk+JRMLt49GjR3X16tVrCkscNBuPx3rxxRddgTESiWhxcdEl1AfdlpaW9Mgjj+i5554LEp3AAruZFgrtaLgvLi46JAflHZyNFQqwcqZU5WwVjx8+YwNsnBCVOiuLSrDC9vP5vCfJsNKvltdtHRdGAON3MFQg19fXVa1WVa1W1Ww2Pcdq6Qv+JFd6kwABAABJREFUJmZ7Lmq1msbjsWZnZ+/IahTXEWTKBlGBBXYn2Hg8dlQj24RupZwJ4C3VyX4OZCMSiXjQFmhLJEe2AZ3gFdVIEBRLi7O9JryGpHUymfTQzAisWY+s1DPDMG3PTiwW89DcrNlgv16vO2EGSy+DBiftBN/FYtEda71e16VLlxwNj31k3zmuSqXiZJ1brZbC4bByuZyWlpY0NzenSqXiRA5AvLa3tx2V0CpyhkIhh4xA8eK7t7e3HboCvY6EE9pytVp11yUajboEAhQftTZ6ciw6w35zPmAK1Ot1R+EDdQF1knYSO5LdcHgyTNbS6rj+mKVIWj/EPYJcOQhcqVRSPp+/ZsEtsMCs3XfffSqXy/r85z9/U7cbJDqBHWizMHs6ndbW1pZarZajY1gKhK2aWbNODwdvKWp+5R1/c6qdr0MSBJqCI4W2YJWSrHSov/p7LZU1KreNRsMNItyLO832CaJo1sUJUsWz1cw7zayCENcnsMDuNLOKav7iin3OMRIJmyhYFSw/ukDfCVQqizSwdlC0sciutFv2GfU1+1181qpJ2h8q1QTlJBuvRbMFHWcwKn0n7BfBM0WjUCjkikP+JM0mhew3xSGQDYJ4EgF6HumttLQvi+jbxNL2aNrPcC04v9ZPSBN6czKZdNv1C9pw7UCRMBJHi+JZSh1rH+g7CRO9iqD2fM4qh7I9jOPCuJbcHxalww+xf1zDwALzG895Npt1AiE304JEJ7ADa6FQSG9+85u1vLysQqHgWeyZZ2CbSiORiDKZjCdZ4TU7pdw6uL0SHqqSOEbocDhY2/hvZ9ggYkDfjP2dwMEmIn6Dd91sNrW5uen6bqjqNZtNbWxsOCoeFchUKqVUKuXECprNpqvqQf+ArnAnVO4IBgh4kM0NaGuB3Wk2Go1cL0i1WlW73XaoDkExFXcUxKCXEVTbZ7/Vaqnb7apUKjllSN4HWmMTGb6Lv9s1z59MYSQrMzMzkuSEWyyli+2AFtlAZmZmRrFYzA0+3stI0Obm5jw0YL+SmbVoNKp8Pi9Jmp+fdwIu7DPnAKofKMx4vCPzvLq6qlqt5hIn+ibo5UGYBnSLf22Qb/s3LQJP74+lsNkEloIM74cyVygUHGpCgQp/k0wmlUqlnGQ1CZndRrfbdYIB+CKLurE/lm5tRROsH0P0AJlwkivbT9rpdNxxY7lcTm9961v1yiuv6Omnn75jC2OB7Z+9/PLL+uxnP6tTp05peXn5pvvqINEJ7MBaKBRSuVzW8vKyq2rxdyqWtvKIA+M90sQx2V4QzAoLWDobC71NliR5lJHs9vnXJk1sx6r5WJ78XoGA5VK3221HccCxg1bttc9UEzECFzsdnWBpP832OtlKaMARD+xOMZ4jAmT7TNpZGgTOe60rmA1WbWDqf/5tn44Nxnm+/WIme/UV2sAcZAC6HOsk7+XZs2uJv4cHKthexvcwGJR98ReQ7Hmx1DgKG6xxnDeblIBCcf5Ap0mOKCCRKLCOWCqgvSYWSfKjcKytnAeLqvF3jtG+bgtiJFf+uTr++8OPyFn/5UcI/T7N9hTZY/QfL9uwaJ8VpbDJTDwe19LSktrt9r77hzvFKBLSfxYO76isHtTBoY1GQxcuXNDCwsIt6aUNEp3ADqyFQiGdO3dOP/ADP+AqqkDxDLtj0Qeup4EQPjooBo4Q5x6NRh1nmypYu912vGySDovC0AyKw/XLUNuggNk9xWJRhUJBw+HQqeigpONvbhwMBlpZWVG1WtWFCxec2EEsFlMmk9H29ray2awnYEJ5h/PFftKI22w2df78eVWrVZ09e/aaFdrbYewvtIxkMnlNCl9gge2X0Q9DsQE1MLueWGEAkABLaUN9sdvtembe0DcBLZV1ydKKQImkCb2oVCo5xIACBtujEJJOpx3C0Wg03Bpj5ehtIE/Ayz7RE8KgYdYeO3sFI9jxJ3V8ttFoaGpqyiHs1lKplI4cOaJWq6XNzU1tbm561Mw6nY46nY7nnHLeh8OhqtWqtre3VSqVHErUbrcdGk4/jx0gSqBv/852of3St7NXUYxeHQI86GXdbtf5FHwQSA7XD4oa6x6vcV9QgOJesD1T+DLQGvwRqJEdbmqRchCgbrfrSXrpX5qennZIVrFY1I/92I+pWCzqC1/4QkBf004Rg9lCb3vb25TL5fSRj3xEzz77rP7sz/7M0297EOz8+fP6zGc+o/n5eT3wwAM3fftBohPYgTQW8Fwup1wu5xm65kdTJHnUh2zFi/fbHhyMChpynbZKRjJh34vZRINtWw44lUgoDRYl2qsCLE0oH51Ox9FbLKKDE+U47f7g4KygAscEhx0qCgpB+2X+a7KXUlNgge2XEURCq0K50K4FtgfO/xxyT/M+fwWedchSkrj/beJgK/42GbLb9Rvf7adq8dpeSBLbs6gRNC3WDEv79X/Ovx+28GSpdraXErrqaLQzh6fVarmGeb+Spj1PrP/Mk2HbBPqJRML1vEDLs/tEL47fB1hqoR/N4b1WxtmuYfZz/A2Ux6Lu/N1/vdimH2mya7n/3O7l22zflzRR8/ObFYDgfojFYlpYWND09HSwFv+DbW9v69KlS8rn83r44YcVi8V0/Phx50MPmjUaDV25ckWtVkvSRE3xWmvKjdrBO6OBBSbp1KlTOnr0qEqlkicB8FPVcBTpdNpx4KXd1ATruGyiYrdhHax1rCAklnKFQyFxmJqaUjqdds6aQXc4fQbrgWDYoIFKaL1e1+XLl536jk2WON695szQDwRtIplMuongONxWq6WLFy8qk8loeXl5X5IdKs82GAwssDvFSA5qtZra7bbW1tacEIkk59i5d5vNpktA7HNq6ULM07GCBjYI7fV6GgwGbv0hMLZ9QJK0sbHhZq1Y6hlN+iif0cRv1zd6Vazwhw2oUTpkDWm1WkqlUi55iMfjKpfLmp6eftXzR98LSJZNVFi70+m0W2cTiYSb1cMa3263XXM80vrShIrLOWEuTDqd1vz8vCKRiObn513vJkIs3W7XzTuCcgTKZemBbBe0hSQJdUwQJVTwQKD28iHj8dhJRfM6yF8kElGr1fIcDzLTXHO+HyoZ0ta20Geph1Cs6Kmampry9HxZhLHVarl5Oqj+pVIpnTlzRpcuXQr6JP/Ber2e/uIv/kJf//rX9a53vUvHjx/Xu9/9bpXLZf3u7/7ufu/ebbcrV65ofX1dq6urknYUAefn57W5uekR3ni9FiQ6gR1Im56edr05dtIzZhtq+V2SW/wtMmOrqpYjz+s4A4uC8C/m7y2RJj07BD84F7ttKBPpdNo5nL0kpXFUdmgpx2OPkd9t4kUyZako0qQayDaRYi0UCm569+2s4Pn7CAIL7E4wnhk7w4aJ99eaR2Or/hQf/IIj0mRYZLfb9fTW2DWAIgZrCQE027aVfL7T3+PBs+9fEyR5XpMmfSZ+xILiB9+JKmKz2XTDKl/LbK8gdGDoW3sNFEVVDDEVP2LhLwhJchS0ZrOp8XjsenSQxIaSbBNLP0rjX/fs+WHfOL8WjeEaW9TOMgX2UkCzrAF7jblv/CgTqD3nwX9P2OvPPtukEv8FRZvqO6ie7dXh3mPQaYDo7Nh4PFalUlEqlXJsiVwup2w2eyD9F/cLa0qxWNShQ4cczfSNWpDoBHbgLBQK6ezZs3rPe96jTqejSqXiIHa/4wOC73a7zmnwOws/8tTwmm2igKNg9kQikXCvU+kkUICqAsKDLGytVnPzIahG4tDo6SGx2Avm3d7e1tramur1utsXAgucFg4zk8m435FlhVtuq8q2IRZJ3Hq9rlgsps3NTeXzeT344IO3dW4N1cnAAruTrN1uu2o5BQGUC7e3t90zhXIVATmzU7LZrNsWogV2jYBSZSlgzJSh15B1jb48UB4rMUyPEPvAD0lZPB73yB8zg4bnP5fLOaVIG6AjEEDfBsgwQibr6+sqFAqveR5DoR2VtOFw6JTQGEaZz+cdWuK3RCKh6elp1et1bW1tqdFouHOIjC0JDWv7YDBw6yr0tWQyqUwmo3g87pIgi6rk83m3RrJNjr/VaqnT6bhEAmQK5AMkimsPYm8pZp1ORxsbGw4lscqbNnEikaTQZBOPaDTq1vh2u+0p1Fk2g6Xl2aSa/i3+zvkgEcdnNJtNhcNhFYtF1weE8pyfqnkQjefH9gLDyDjoyWAoFNIP/uAPKpvN6vd///e1ubn5hrcZJDqBHTjDcaVSKSfJ6a+4YZYCwmJu1dP89ASL7Nikwzogy5O/Fs+cgAWHB53NKuewXfu5vRZJEB0788ZWh6nOEbRQ3bPKPva7OS/+SiHH3Wg0FArtDBQlOAgssINmPFsoq/E3EgiLTNjkwkqjS5Mqvu2x8aO69nm129xrTbO0NCrxvJ/n3zadSxPkw6K6/rWI9Yd10j/Xx/YcWoTC0sCgX13LCLBBxprNphM6uJYxw8YmCBSorNCDnwrGd9TrdSecQBK5vb0zBBSJfoumIwXN+bT+wPZyWmSdc0RCbOcRWbMURH8fDe/lc1buG3/i/z7/Zyyac63zbv2aRZVIYjl+/3y2RCKh+fl5bW1taWNj45rX66CYRRaxg57kYNPT0zp+/Pg1ixc3akGiE9iBMtuXAjqDEg7caxwQC7dV82Lis61sSnKfSSQSTqGHaikBCSpFBBMsaqitSXKIDg4TqVQCJFtV5f3SpJdnLyc1Go1UqVRUq9U8ARbVS7bTbrdVr9ed04zFYs65JxIJ93ckcFOplOc76RmqVquO618qlXTu3LmgZyawA2dUv+v1ulP5QqmQogNFiOFwqFar5ZIikpHt7W2nBmmllilGdDod9z2WBler1VwPD1QlyduszzrW7/dVq9XU6/Xc829liKF+0etHs3mxWHQFo1AopM3NTW1vbzs0ibWPNRVkGhUyUBQUI1dXV1UqlV6TxkZPYK1W08bGhpMvvlYTNyj44uKipqamtLGxoWq16l5nPWQgsy36DIdDvfLKK25eTSaTUbFYdOg5vT6SPIlCrVZzPVYkCJKUyWQcBU6aqL0xa6jT6Whqaso17vO+8XjsUccjcSbBAomzFET2CX/H/ll/x3vstkAVKOZZOraV+AaV4B4Jh8Oufwc0zAby5XJZ733ve/X000/rM5/5zE1pMg/s3rT7779fx48f13/9r//1pmwvSHQCO1BWLpdVLpcd1cxyQy3v3Ab7tirqpwlQNSXRwaGQ5FhkRppUeS0iYnnZtvLmV+vB7P+pVu4lIiBNqC4cKw2qOFhLV7BJm60++ukMODreRzXPIkPb29tqNpuKRqPa2NhQIpFwfUQHkYMc2MExnm96cGjm5xmECmafob2myvtlpSmO0JdjxQmo4Nt+C1BkS+e0M2B4nyT3XRQ0WOdsoAyaY0VL7A+N9Kx7BN9+4QAbkLO/0J2up1cnHA4rmUy6z6Gg+WoDi0Hxc7mc618hkWEdRnkTCqEtSFk1NBKJqakpV3iya6u9jphfjMbeK9DLrC/BD4EIca2smplFzawP8yM8dm22xTv20b++W9SIc8fxW9/E3+nNsTRM9pfrA3V7YWFBq6urHmbDQbbRaCJ3zrNXKBTUbrfVaDQO3DlqNBq6evWqcrnca6K7N2JBohPYgbJz587p7W9/u+bn59VoNNy8CZyxVTuDO20TDiqzLOwELe122zm+TCajfD7vHIVVqoFXbRMJAn8WOis1CudZmvTT2GBmenraTUH3Lwqj0cj15qC+RFW1VCq5BAynTmWOZmnoNOyPpdVQjbXOlsotgdvW1pZarZYGg4GKxaJOnjypWCymRCJxuy53YIHddgNhobeN/gzQkFarpX6/r1arpfF47BTImGNCIpPJZDyy7iQZNHYnEgk1m0239iQSCW1sbLhZX8xMQcYZYRT6AS3FKZ/PazgcOpqpXeNs8cVP27VFHpIU6F/JZNKtORaJgt4EQgXaMzU1pUKhoFKp9KrnNxKJaG5uTsPh0M26KZfLrxkUZbNZZTIZlUol9Xo9VSoVtVott3Y2Gg11Oh2HwFWrVUdZswUfxFdAuorFouu7IqG1YgAgHQgYdLtddz5JHglqSShRTgP5s76D5ApaM4pydm2WdpSrSDZJnlib/Wg+18GifyBWFNIikYjzI4lEwh3TaDRy5yoej7vrzIwozlEul9ODDz6oSqXyeh6re9JGo5GuXr2qcrmsQ4cOKZlM6v7771cikdBTTz114HqZLly4oK985St68MEHtbCwcNO2GyQ6gR0oS6fTzikSzPtRGSgD1lkRwNsqmTSZeE3iYukLVMZoMoSiQlV1LweEQ7WVMv5uHSffkUwmlc1m92zCx9lwnLbxFLPVXb6DvxOUWCqflU21ogT2nGAkSkiObmxsOMcZ9O4Edq+ZfaZpLIeSZPvhKA7YvkCLrtKnYfthbJWez5FQsL7Qc7MXMsxcGYJc24tBII7Aid0/u+bxN9YamyghQmB7/qDTsb5xrLY/iGNk8ClJoV8Yxm92/bO9J69loBC2PwYamEV4LFUQahgJIiMGuM4gOogN+M8v5zgSibjvsCicpZDZQdRsk/3hvvDPT7O9Wv45QSRkHJ9d0/1MBeiGHCuFP9t/g58jGbc9O+wPanjhcNgVw3gOstnsbVfjvJNtMBjoxRdfdINzk8mkHnjgAUWjUT399NMHLtFZXV3Vd7/7XR0+fDhIdAIL7PVaPp/X0tKSms2mq8D5K2GSnLKMNEFScJLSxMlRWc1kMo47jeOFToEyDZUsAhpUiwha2u22otGoCoWCqyKyTRvIEJSEwzuqNntVM9lmq9Vy3H+buPkbcAlomOTNBHTmUxBUWEoeSNBeTay2clipVFwvAfu/sLCgkydP3urLHVhgt81slRwqVKVScc8HzyR9bQSFPDvxeNwVBgaDgQtQoQSBxtiAlmeV/j/68Kx4CD120kQBziqjRaNRpVIp9Xo99Xo9h1Twup+qynMO6sCaYIcus75ZuhXHlE6nXS8I/T4kOMViUel0WqVS6TUbkcPhsDKZjIemdT1GIsE1aTQaHvEGepf8Q0MJ9FnjURvL5/PufFerVXdupQmNjSQCRMf20nCeY7GY8vm8Sza63a5arZampqaUzWbV6/XUaDQcKm6RfVvUAmlhXc9kMk75zY4JsPeepUDTq8Rw1Fgspk6n4wpm+LZ4PO56u7gnOPZKpaLBYKBcLucSq+3tbZXL5euWEj8I1u129dnPflYrKyt6+OGHVSgU9IEPfECLi4v6q7/6K8+crYNgTz/9tOr1uh544AGdPXv2pm03SHQCOxA2MzOj2dlZzc7OetTPbKWU30kacAD+v9lKmK1w4cRZ9C0qAtXLznKQJkkUKA6JCJVBqCwEH7yGo7FzfayzJ2gh0fGrJ7GA2oQNrrhN+KgW2yoiCZKtCEsTJ0cTql2kqYYigbu+vu6cOwiPHfZ3PUaVkYDvdplt7A4sMIygnp4cBlLyfNvfbQ8IVC6L8tjnmUKD7fcjqbI8doJb1jSoUbbfws59sWgQCQ8oRqPRcN9pg3VbJOE7rWIb64Kt8icSCbcfrGOcL7bBPjebTa2trXn2a681gSTE9r5cr/FdzCwJhULu2kC/snQuaSdh2dzcdGtgPB5XsVh0VEKLhCDbbNcJ1j9J7r1cH/5mJaX9hSmSGesb2DbrMfcC14kEVJLH31Cw41rbbXAv4fMsikiCDKWOZMgeK/c274vFYm5tpwcMSe/Adu6r8+fPO5p6NBrV7OysE6M4aNZoNLSysuLQ0XQ6rVwup3a7vUvF70YsSHQCOxB25MgRveUtb9GRI0d2Dcm01AcCDlCTvWSVMetgEomE+6E6aVGVcDjsqGs2kbCJDokLFV+C+Hg8rlQqpXw+74IpfqcB2I+mdDodp0yEAhPHR68RRvBB34A9N8lkUrlcziFWIE/D4dDRLKA2sM1SqaRQKKRGo+ESKZwj56xWq+ny5cvK5XIqFotaWFhw27veBR56DI77djgGkivbWxVYYNJEKrnRaKjZbKpWq3mEQOiVI8EB/aG4gPqiTSqkSdBpqUXValWNRsOtGSh/QRXih2fW7qNFZqj22+en0Whoc3PTFRAIZukNYpugSFT6SeIstY01hHWD9QRpaMkrpUzfDP00e80VYU18vca5KZVKKhaLbv1gzhgS1FYIoNfraWVlxe0vFCzWYUvNI2kCwWa9tdSzUqmkqakph3pxHSkS8XfOJfcMfsgW6UDPQaE4l/TRSF45afwL6zK9YZZWzTmxYgf4Lys0Y5kQtgBG0phKpZTJZFQoFNx1tP8/6DYYDPTtb39bzWbTSZUfPnxYFy5cOJD+ZWtrS5VKRY1GQ9IOA2d2dlZXrlwJEp3AAnsty2azOnLkiGKxmEtEcNCSHE0ER0HVksDBNlnivPmdSiyOzD8IkNfs5F/bw2PRCNsDxGvZbFb5fF7z8/OOypZOp528s7/vhn2gwmoTNtsDxL5Z2pt1oDhHGplt8ERTKpVDi4hBZWD/oUIQCFE15fgRLrAVapqo4eBbs+gYMtbD4VBTU1M3jAq9HrtdSVVgd7ZxL1sEB3EABAdIKiS5AJP1gmGU/sGOqVRKyWRS09PTu4oSNqC2v/NsEijbCj5BLL8zC8YO/7V9NHbODFRWaYK8UMBhDbD0W7t/rANQ31gvGJba6/U8SAj7TBKIdPEbSWpey0KhkFN4skpnrC3QtyS54+Pc+FXLSPIymYw77+122/Uq2WSD801BjGPknpEmPoLv2Ku4QiHJsggw9g+/xPvZF/yU7Uuy1GyumSTXJ0riyfHZZNxS8fwKcday2azuu+8+bWxs6OrVq6//4gV2T1soFNL999+vWq2mT3/6066Y83osSHQCOxBWKBR07Ngxp6rTarXU7XZdQG6nO4dCIecMcNa2MkZFzyY3cKRRxrGVU+tQqHjhFAnqLV2BSiKqN9PT0yqXyzp8+LDjbROEXGt2hJ21IE1oVnD12Sc/OuGnwUHFaLVabgYEqlLsL1Vhzh3nDC58Pp9Xr9fzcPYJcHD66+vrTlFoampKMzMzymaznqoiBpJD9Xw8Hjv1pBtFhW7ULI0xsINttjm9Vqup0+mo2Wy6AZjWLKWT+8dKOTNfB6Qkl8tpbm5O/X5fW1tbnt4KSz2ziU4oFHJrCUUOXrey8qis+dccSa6QwTZZFy1FzfYJ2cDbUtEsVQvEwE9xIqmj8d4mW5zTZDJ5SxMdScrlcspms45yVqvV3GyhwWDgKG6s5xRqOBeSPIkOEtb4ApAraSJeQwEN5T36VuyaDapi12QSRysCw3vZB95n1dWgjUkTiiV/t7RC9s8WprjnLEoEYsN9x/ezL5bm5m+oLxQKOnfunJ5//vkg0QnsmhYOh/Xggw8qk8nom9/8pi5fvvy6txUkOoHd05ZIJJTNZlUoFDzzDuCU20Daj05QUbOJDkiNreaymCcSCSWTSedQCCrgztNMDEJikQ9Lj7OTtnO5nCfot3Qxy6G2gT2JQLvddsNLpYkDkuRpAgYJgcqCY7OzE2hWRUIbB2mrsDhK0DEQs62tLUk7inc4WElOfAEJVCur2u/3nSBCPB53jp0gjKAE5zs/P69kMnnDjcmBBXajRlW/2+2qWq26Z43AD5SVYopVYrOWSCQ8TeIgO1BVeR4LhYL7Xht4Woob9z2B6urqqntGxuOxo49Jk1k6JDOSPEIH0WhUmUzGJRlI3NvP8H62x3fT32PFB7LZrFuTpL1nw9gEKBwOO+oK6/atfq5DoZCKxaJD0+ht7HQ6TpiB/YUu22g0XALIa5xLxAKQ2CZZseiPpXwh1sK1xO/4k0a7vxZ1pwBj2QEkr3yPFYqw94rtz7SolfWB3HegNfb7oKpxzbkfxuOxu44kSFAXz5w5414LLLC9LBQKaWlpyRWD34gFiU5g97RBAcnlckokEh4JUBTDMKpatvnTTw1jEbeNx/xr+3uszCvbswESn2e7KP3wedAQ5j7YBOd6KpwEX1D0cJwkJ9BtGo2GU4eDdkFFGmfKaxwT32/Ve6DD0VBLVRYloEQioUKh4D4DJSedTisWizkEiuSs0Wg4DjvS3LyHamMkElGxWFShUFA6nXbV58ACu5VGkt/tdl3136qt0fMC75773QaANLFHIhHV63VHZYtGo+5ejkR25njNz8+7oJekIxaLubXK3vM2IK9Wqy6oZsCmRVds74/t5yHBQigEGqz9DhTEeO7ZB5Apkj6CFJvU2G1Z1UdbNGEbIMcUT26lkVCC7qyurqrZbLriF/tPosDa5JfPZu2m+GUpjHYwqPU9sAv8BSzbI2VV4dg+14NrgkgDKB0FK1vksj2YViyC+T42AeNe5vrYni+O0947/p4d0CwSSMYhnDhxQq+88sotvqJ3tx3kgh330Pz8vNLp9BsWsAgSncDuaVtaWtI73/lOnThxwjPxmd4RnApoAcP2WNz93GrbyIkTpz+EypalGFjnhlPAGfmb6yyXPx6POyQKVZ/rMapqqK5JXqQKg+ZlpT5xhlSVoR/4VX34F1TIKkuNx2P3u3WK/ItDRWmt1WrpwoULLhi0wdVoNHLyvFwrUCFUfxiW2Gq1VKlUtLKyomKxqNOnTx9oRxHYzTcShW63q83NTTfFm7XAVupBbq06FgkBf2+3264PjV4R1LxQcERK2KK+PH97obnSTgB66NAhzczMSNp59hqNhhvg6J/j0u/3Va/XXR8hBQ2SKp5jSwkFASHhsQUcUOBSqeRRMLP9kLYHBIW3UCjkmvwzmYwrwLza2mfpUjfL2L+5uTmlUilVKhVHvWW9C4V25MNBnxgzYJO18XhH2plenX6/7ylSgbSRONlEgjXbijHY622pcVxz+yPJDRG14hSWegv1keR3rzk63HOIK7DmW5Ee/mXb9jvYbjabdclqOp3W8ePH9Z3vfOemXbO73fr9vp5//nmNx2MdOXJEmUxGDzzwgC5duqRXXnllV5/TvW6bm5t65ZVXnGjHGy1gBolOYPe0lctlvfnNb1a5XHYPCws4wT9OBpSHiibOjEqaTYAsrG+Vx2zzKoEDzsvOv4HOZo3FjBkTuVxO6XTaUeGux6g62h4dS6chkaFSB6Wl1Wp56AkEMLZaSYUPdIkEkEoen7c9SZZmw3tBY6LRqKP/ELyR2DFFnr4FUCUCH9sQzRT6arWqp59+WsvLyzp58mTQSxPYTTWKFq1WS+vr62o0Gm4IbiqVcmsIP1C0LBrMc2P746CEMjtmdnbWFTv8dr339PT0tPv/aDTS5uamS0Ts2sA6ZJEG1jYGnvrFDbBUKuWeVUtHk3bWHJr87bpLsciiwTZwpsgCDdj2/u1lFkW5WcYaVygUFIlEtLW15Xpt/IIyyWRS7XZboVDIJTX2uEB8rLS07Y2CBmwLSnzeJpX8y/+twAM+iGSGc04xza+CZxNHrr3dNz/yaAuBJDvQsm1izJpu95P7n0KVJEc1zuVynt6eg2yDwUCXLl1SKpXSoUOHlEgkdOzYMY3HY124cOHAnaNarabV1VVls1lX6HkjFiQ6gd3Tls1mdejQIQ0GA62urjrJVyuRTOCMoyHQZ5GHQ83/rTNgobb8eUuhogqMQ7I0MGki90oVDyebTqc1MzOzi5vq/y5LG5F2hvg1m01VKhVXvWUfoF/YRdMKBNjZDVAjrKw2322TltFo5II8tm97gqDYwM9mICJBHIkL55KkhQqobZL1V4YJEFqtli5evKgXX3xRf/iHf6gzZ84omUxqYWFBJ06cuBW3VWAH0OgNszK7/mcxHo87KWGSGN5PdZ7KdqlUUiaT0ezsrENuIpGIEym4WUbSAQWE/UXiuVqtanNzU9IkmWOyva3aswbwrCKj3Gg0POhtuVyWtEPHQlUsHA47RUQq/nabFF6gvjFHR7o2WmMb6W92USMSiWhubk65XM7tE8kfSQnMgFarJWkymsCuyZxLi6BZVM+qfFpRCdZZS+FjXbbUyL0KU1ZgBtEaEiDuL+YCkTBZiXGbtJLgcJ5TqZRH2ZJraOl5FArpy+p0Og695LpOT08rk8nc1Gt2N1u329VXv/pVNRoNNzj0Xe96l3K5nL7yla94hCfudRuPx3r22WeVz+eVz+d1+PDhN7zNINEJ7J40qldU5jY2NlySQ+Jhp4/7VYH8ijIWymdx99NGCMJtFReHZVWK+E6cs/0MjjIejzuevN9sI6+fo2/7XOgP4DvYR3/l0CYw8OFtFdHfRIvT5pzYiiGOEVoGqBGJjq0CShOpZssBx7H3+30PvZBE0yZqfF+tVtPKyoq+/e1va3t7WxcuXFA8Hg8SncBumhHQWQES+xpohV+O3d/cTSKTyWScutqt7kEhabCFE6uKBrITCoUcNRRkhWo9hRDWPZAAK8QQiURckEsRBTXFbDbrzpWkXUkiaLltin8t868HN8tAaOLxuOsXJKGzg16hddGvQtO9n2JmETG7dlr0xL7OeaIn0fojCmJsB79hk2/bt8P7SXS4F9gu3wsrwfZM2X4baSKww994r793h9c5XxStGCuA2l5gOzYYDHT+/HlHhU8mkzp16pTW19cPJAV7ZWVFzz333E0TrAgSncDuSVtaWtJDDz2k++67zzUNr62teWhWkjxOhB4bi1pYRSHJO+dGmvCcea9Fa2i+nJqacpC/DQqsSg+O3k9B8FcqcXS2OojBt+50Ok7Fx/YU8R5LV7DIk/2xvQV8p5+WAa8/k8k4Wpk9pwRO9NvgOP10Frv/NrBiXhCIG8IPBFzh8M5081QqpYWFBcViMf3SL/2Sjh07pne+850usLrXzIpFhEIhd48FdvONoJv+vfX1dbXbbVUqFVfl5tmies19SlDIvZ7JZJTP57W4uOjmYFHY2A+bmppy6EuhUHBDhm1gjPQzlF7brI4hdGDXDEmuYIKwQiKRcAkCZmnAbAu1NysAYPuDMNsTcqssGo06mhU0WYbAQmteX193FLbp6Wklk0l3fUGyuC84HtQtSYoYvom6HUkz77N04OFw6IRmLGUYuiP3n+0X4l61yRO9YTYZgtZoKYHWB7COc94tijQej9215vdKpSJJjt5nfWUgHDOxbrerb37zm26tKRaLetOb3qQrV64cyPP03HPPaXV1VR/60If0pje96Q1vL0h0ArsnLZPJ6OTJkyqVSi7JsAgHBv0Bx2qRC4sgsNjs1fxr+cy20oYDsXLLe1EtbHKz14/f9toHzCoDUe3zoyEcM/vrTzb44Thshc6+16ox2X3i/ewHAVC/3/c4Tlst5LPQM3DGNnHEobJdkkFEFSKRiN7ylrdoYWFBhw4dumcrYfQm2aqtTcRfrcJ9r56Tm208AwSnrB8UEQhIbRHBBqKSPM8wgX46ndbc3NwdkYSDLhGUS3IoBc8W/TQULfZajyz11957HDfy0PazFhW3n7FiCxbZuBY17dX6d26GQdeiqEJCAqIFNY11kcSEtT+dTrvEgPdKE8ED27xPsmiTiXg87nwRax/BsJWt5lxYUQxr1h+wflJwsgi+9RmWps31s31ntvhlfYalSENFtogP5xX/YwuPB9VImNfX1x2VsFQquT6mg2aVSsWpVkqTdcEm8DdiQaIT2D1phUJBb3rTmxSNRrW5uanRaKRCoeAoUX7z981YqplNFqhKgvzgMOxEchZ2P6pCxc1PWcOZ0J8zPz/vGcJ5I0Z1DzEFHAiOtdlsOpWlaDSqQqHgqRhiNunzD6dLpVKeaiOUND8tjs/gqO3UbeucLUJl+d72vFSr1V2T5iU5tCiXy6lUKmlhYcGjVHQvmu2xoAILbSYajbqKM/ed5fGjXHcvn583auPx2IlhXL161aEzdpYTSmEWMYXq5ZeWLxQKCoVCOn78uJvVcidZNpvVuXPn1Gq1tLm5qVarpXq9LmmixNhsNl3ig1m66Wg0mQ9GcEIg71drA832z5fh9V6v5+SI6e+71v16uwRHYrGYjhw54gZ81ut113tSrVbdNWXIqb0npqenlUgkPL2HtpDEOeHZtPRg+rs6nY6bb8R5tvNtrFqbn43APcq5wv/Zv3ON6evhuuEL8Zvc0yB+9GOybf6t1Wpu2zAqwuGwyuWyu5YzMzN68MEHtbq6qkuXLt2W6xjY3WeRSERvfetbJUl///d/71DCG7Eg0QnsnjKcRjKZVLFYdJPLgfapiGH+ipVVT7MQPf/6+0RsJctW0kiGOp2Oc9jXopvZyi/Jjl8O+nrM9tkQhBF02Oqb5WZbPjb7xz5aoQSbYNgGYmmCSBEI+qt8tonZIjxcL1shtNu3/QCWsmbNNsAizXsQzPZDSXL3K7OHBoOBRx3PIpKBXdu4P3l2aQZnFoxNsv3oJ8kkAhw24YlEIsrn83cEkuO3aDSqfD6vqakpF2D7JePts2dpsP5ERZLn+eZ8cr+yxtm1wVLe/LPFXguxsQnSrbRwOOySGVTy7HprkZZOp+PWRAZI+/tXMM6NRcStn9lLQdP6KlsM8/eNce782+c7bQJJ4cQOubZoi1UJ9G8PP8m+W8YAiZjf70o7NMW5uTk3ayewwPaycDisubk5HT58WN/85jdf1zaCRCewe8oSiYRmZmY0NzenTCbj+Mw2OGcBH4/Hrr/BOgsqWKPRyCnWWKfGQo7Zv0tyzi2Xy7nGXGnCXeezUCJ4DYUcgqQbceJQGba2trS2tqatrS0numAbTTkPIFCNRsPJ48LJx4kx2d1S8ghYQFekSUDSbDZdsE01ECdnK4Qknaj/2EZmHGa1WlWr1VI2m3UzdyKRiLuWBAOxWEzj8VgrKyvK5/OOd34vm5Ud3t7edjA/16jRaGg4HGpubs71CzCX5dWq4wfdxuOxarWaer2eKpWKQ8xAaXl2ksmkjh49qk6no62tLU9SbkUJFhYWVCqVXHB4pzdfJxIJzc3NaTwea21tzZPsUbm3VX0bxEpSLpfzFEhYe1BwBMlgMGU0GnXzvqDPseYhyCLpmj06o9HIUcEsan6rDBRleXlZ8XhcFy9edGI3rHndbldbW1vKZDJuNAD0I+TzI5GIms2mer2eU8a09FyYA1iv13NIkSRHBywUCk7Vj/d0Oh0niIAfabfbGo/Hbj+4ltyPfpEJEl1QKytMwbZB7vB7rD34SgQ3+CE5tFYoFHT27Fk1Gg09++yzt+y6BXZ3Wzgc1n333adIJKLPfOYzr2sbQaIT2D1l8Xhc5XJZ2WzWUzG0vTa2EmWpU7Yyey2zzZQWwaFaxWctJ30vpIP38h4cP9vlvdfLQec4CU7gj+PArBQulWeLrpBk4IzsubEJiP2+16puW9qfPecWheG9fBb6BcdD0EQgAE2GwIEki+n09BjcLkrLfhgBHUm7bTAOh8OeQa6oQQWIzvWZ7QOz1CqrTsj5t2IEFvEliU+lUneVhC6FA4Jai+LYYo99Xu0zbxMNW+H3owm20d1u3w6qfC3KmiTPdZF0WwQ5SEKSyeSefU2WLmwpXhwbhSyOi3WapAO6n6W2+f2WXftisZhLKDkn1qfxN/vD+on5BQb4G8Uz259le3D8im2W1WCZErzmv5Yg8CRwgU3OO8kvzwV/O0hmafCFQkGzs7OvW7glSHQCu6dsbm5O/+gf/SMVi0VtbW052prkpTkQ6NNQj1O18xtAHvw9OmyDKqV1zlSuoDZQ8bQBONUvZvOApuRyOSUSCbVaLU8j/vVUgqEWMBeD2Q1U1iwVBUqFNdtAjUOMx+Me1KnT6bjeJPsZzicVWb4jnU675Ilzzfmm8stnbV8JFUP+PhqNXCNwPp+XNKHL2UGozWZTFy9eVCaT0dzc3Bu6j+5k49iRH6dpk99jsZiTc+33+9rc3NTU1JRSqZQSiYSy2WyQ8FzD7HkhsNve3nZqZNxvOGDWBPpwotGoFhYWND8/v29qam/UyuWycrmcLl26pPPnzztUxs7ZstV61kC/aEGhUJAkj5LYeDxWu912qopTU1PKZrPKZDJKpVJu/Zybm9P8/LynYCR5qcLb29tqNBoOQfAnETfbbOGnVCrpoYce0urqqr773e8qHo8rnU67BIiZRfwtEokolUqpVqtpOBw61T56dqQdhAXEB7loEBqCPNAxEBWolfRKwiaIRCJuhhEJYLVadT4L2X9pgtLl83lP8S4S2ZEKz2QynmcBdbjt7W13zkHW6Ye1Pam2eGaLd/l8XidOnNBTTz11S67X3WiDwUAXL16UtKMcm06nderUKV29etX9/SAZ8czJkyc1OzurXC73urZzd67EgQV2DYvH45qdnVU8HnfULX9PiHWEVhDAX5G0CY6lbfm3YREMUBlbjbOvS5MAyo/yWHnpvWgacKT3qpD5e2nsvluhALZt1eE4B36Ot92Gv6Lo5+TvdU6gc1jZUr6D5M/uPxQV6yhJvGzVMBQKeagRtvrYbrddovRa6NzdbP6qLkmx7bHyiz7474/Adswiv71ez6k0gh7yd1ul5nMWsYDaQ8B+txrUqHQ6rWw2u+vc2OQDBMKuZ3Yds+uSH/2y38e9DE0VUYfXMrsm3a57mrUtl8up1Wo5CXwr/mH7ljhmSQ4ts+uav++G1zCrROdP+kgu90L/WWv5DL7Qoub+NdvfU+n3W7ZPy4/W2f2yz4itzNvjisViyufzd5w4x37acDjU+vq60um0FhcXFY/HNTc3p263q0uXLnnO30GwVqularXqkj57/9+IBYlOYPeUpVIpHTlyRN1uV1evXlW73Var1XILtqVnSfLQU2ySYYdg8i+zY0B+cOJ2YrWlWxB88zqLFFVH0ApmolB1n56edtUwttVsNrW2tqZUKuX6VizSMxgMXD8N4ge2ikpSkUqldjk3aUIpAUni89aJ+h0gqBgzEmx/D8fD93e7Xa2srLjP2SFy0oS6h8NHGYzgwQYG0WjUVTjpp7p69arG47E6nY7G47FKpZKrNN6rRmLNPQGFzaqxSVI+n79r0YVbbQSK9KGsrKyo1WppY2NDo9GOmt9wONTa2poikYgWFxddII+Bps7OzurYsWP3DG1yenpaqVRKly5d0urqqqrVqkajkVPlYoBmNptVJBJRq9WSJPfsMjOMnhBL+/LTd1nvisWi8vm8p0fFmg2sUY2U5FE6ux02NTWlfD7vEjxmpNVqNW1sbGh6elqj0UjT09NuXyORiMrlspLJpC5fvqxQKKRsNuuQdyhrljbJGovAgWUY8C/PO2s1CA0UVgRp/Kh9r9dz4jd8h6UWWhob/k2SQ/slOWTH9urY9brVarmeJFBBzkcul9OJEydUKpVuyzW7G6zRaOhv//Zv9aY3vUn33XefZmZm9J73vEdf+9rX9PTTTx+oRGc8Huv555/XzMyMMpnMNdeE67HA+wV2TxgUCBo07RBQy1XmX39/ieSlrdjkR5pI+oJeWL4sVTB+bDWLRAgHjSOTJn09JBZU5v18bwb29ft9JRKJPatoBFvsA2ZRI8nLnff3G9njgA5hkS0SD95jzxtVS+uAbZXRJkb2u9hHf0XTXzEkIMXB2qSR/bTHSpB/Lyc6NkhMJpOOx+2/Bv4+tcAmz3O73XaN1zTGcx79iJhVUCPJZA4Ng2vvpcGtHB8Iix1gbGm/tqBifwjA2+22K8AQrEOh8iOPFkF4LbMFqf1ILumfyOfzTrYf5JSeQX7ok2O94pyiyGYLOtbPcD4sDdqeH9ZdK4ZxLVaAv6jkR972oghalB/aor0H7Huv1bfJ+WCQKb6AxDcowkys3+/r0qVLLlGOx+NaWFg4MGqi1sbjsarVqq5evaperxckOoEFVigU9I53vEOnT5921UEqi1S57CBNmwwQtNhFHN45qAqBtu1PsU7bDo0jYUFRzFa5QqGQR2kH8YR8Pu9QCJtI9Ho9bW5uugBM0p5zULrdrhqNhprNpprNpjsOv0w2KArHZREUghKqsP7ADu45Tp3jzWQyLti2wTXqSigqzc/Pu/3kezlHBFIgVSj6sN/1el3j8Vjlclmj0Ui1Wk2JRMLNzYGrzvFubW0pn8/fVc3gN2qcQ9TmrAiFNJGgRjFsMBjc04nfjRgB6MWLF1WtVlWr1TQYDBw9wk9dnZ6ednNMePZAIMrlso4cOXLPIDkYz342m1Wn0/HMy0G2v9/vO1SF5BAEBxWwzc1N96wyf6vVanlmrZBc8nM9CTnr2H4afW8bGxtqt9uuqEXPIaIOs7OzHiW2paUlpVIpNxyZtZ3eHJAQW9iyiQ4KliSQIPU2UWR9topuJIcWkbLbxBC1abVajhERjUY1PT3t5qdZ5Ad/Zf2NRahqtZqi0aharZaSyaTr3UH1NLAdazab+vznP++eg2w2q4cfftghgAfJxuOxXnjhBW1vb+vxxx93/bmvx4JEJ7B7wuLxuJaWllQoFJwsMtA9nGGbcFiVGGlCLWDRRc0KwwnYxko/39yvNMT3+CU4+UwsFlMymXQNnyz+Fvrv9/uu4dYKGfgNqhIBg90X+y9OzV/557hI3BhCKU16imySaHnYIFQ2iLbn2Kq/2cTJvk+a9CHxHThXEh/7/SBYjUbD/W5nPRwk9MIm3JI8QQ/VVEm7EvyDbEyph9ra7/c9vSSS9zmmIs89yU8ymVQ2m72ngzXWB4JWzgcIDesFiQ73IcUUEmwCX4ZP2kCZIgU/FJZuJR2N58HfM3SjxvlIpVLqdDqeWUl2TZMm9xTCNf5+S9ZCfBX34179nax3frVOu+5x7SyrwVKrpYlPwOfZhJN99jMN/OfQ+gRLYeM9+GMEZyga+vuODrqBMiMJTi/YQe1j2traUjKZdAwN7nX/TKbXsiDRCeyesFwup8cee0zRaFSrq6vqdDouCGbBAPqEY2ypVDhiq7hmJR3pyYEW1+12dymisWjjNAiOQFmA6WleBelYWFhwyY6lBQyHQzUaDV24cEH5fF6Li4vXhG9p2kPFh+PDcJr0sECpS6VSDlmyUs2o1dnp5FZEYDgcKh6PKxwOu2nho9HIVemoRqIwFA5PJm5bxTebZHW7XbetTCajZDKpfD7vZkVYdSdoMbVazW1rOByq2Wwql8upWCweGAdKhZZziRzp5uamU2YimIRqdNCt2Wxqc3NTlUpFtVptF91SmqgJgkrybE9NTalYLCqdTmtmZuaer7SSSPf7fdXrdZVKJaVSKaVSKXef2ZldnU5HsVhMMzMzLpBHYYxePWlCnyUZB0FgbbRrya0w1hS+640Y6zg0RoLVUGgyfFWa0LmgmlrKH8psVrCl1+t5Cj7JZNLR4qBa4rtIfiz1l/lZrNP4Q9Zlzn8ikdD09LRD3Ek4OTZQGNZ8jodky1KJoaRJE/nsWq2mWCzmlEbxm/5+t4Nuo9HIM7cKcSXQt4NkIDorKytujYZKaxk012NBohPYPWHhcNjRlFjQbV8KakgYjsRfeWJx9yvnUEGwCJBfHce+l5kutnLFe9g3nIkfGeJ9ODyCejvXwhrID1UgnJ6/z4CEjvNFtdXfo2Q51Jb6x7Yshc9+zj9rBBqePbd2/oINKPnXr87D9ngP9Cx7/nHcXCNL/YBOBxp2LztVe2zRaFTZbNZRAqmySnJSyVa57qCYvV9AuizSK3nvYxq7WStAXpENPgjnDroZ95JFd21QTpANaitN1jHEUkBcec2ufaA9JJPQ4giyb/a5tgIob9RY71BV4zhZXxGxQXQAX5TP5xUOh53QA4UIzqMtpPkp2Jwnf88Oa66lB0sTaqF/3SVxscgm54bngOOy18ovTsO27D5wfSnAQN2zieVBeIZu1Pz+9aAlOZjtLw2Hw5qfn9ehQ4d0/vx5h/JcjwWJTmD3hDGDYDAYaG1tzdPoDx+ZKn84HNbKyoqazaajneA8qL75kwQeKlspIxCy1axoNOpJckA57EA4aB44FWn3Ys/7ut2um0zdbrd3Qdg4nGazqUql4kGd/CIEkUjE8VyReiY5oqrH9GyQnkgk4qqtLDpUENl3O7SSINI6dQQMmA9EouKnAeKUOW97Nd9ubm46KgVUP+B+rkO329X6+ro6nY6Gw6FisZhrGr+Xe3agU4FKosSGMhJ9A1evXlU0GtXMzIxrOD8oxj1FDwLJMEkg5wrnSn/CcDh0c0ry+bxmZmYOTIAG2nzlyhVtbW255xREwRZieN5BgElwer2eqtWqQ5GhRlklMXrvUC6bmprSsWPHVCqVNDMzc9P7cezMtDdqdngvCUmr1XII+2g0UqVS0eHDh5XNZjU9Pa18Pq9Op6NqtepmYdG/CFUsmUy6NRMmAckEayDnkqQCH8Yazkw1ingUOPzsgdXVVecfM5mMZ64OiBLrs92ev1BnfQDXN5PJqFAoaG5ublexKRAjCOx6LBqN6sEHH1QkEtHa2lqQ6AR2cCyZTOrEiRM6ceKEJO/kbYzKv1Wesipglu9pZZZtVcVyqW0CweLOZ6Aa4KhIPKLR6C7lK2BY+m786A/bgcblp6PxHlvxocLHccAPtwmBNSuTzTFaVR5+JHn6Z6xqEkmUbUSFqmaDQRwyw1OtVCpGAMp+4vRJzDh/iCbk83lFIhEVCgU3QJABj5wPqHJWBvygBKk2aILCaZuF72WES5ogOMzGgVJUqVRcg719/m1juKWyQX0tFAq7JNoPipEU234MiiGoRvJ/G3BbtHUvFMLSn1gLWZur1ap7jcZ1i0ZadJjPYazttxO1ZERALpfTeDxWpVLxFIp6vZ4HZWZA6NrammvW30tQAF8iySWYoCwWxbcIi0W6OU+wG1jrra+z1w9/JXlHEFCks1RFu5ZTYLOz2ayQArRGi1SlUinNzc2p2Wy6dTuwwPwWiUR0+PBhtdvtGy5QBIlOYHe1FQoFvec979HS0pJbTG0yYbn3vV7Pk7DYBlvLJbdojuXlWyoAXGPrgJBbhdPMPuCgSRTg32YyGeVyOedgSACs4lMikVCpVHJTtvdSzbKiAiQuJAbMRiAwQAkN+kAqlfIEBNZx2cSA2TeDwUDNZtNTTWFOCwEiPT3ZbNbRUaxEdr/fdygL38U1q1QqqtfrjvYBNzedTnsabmu1muLxuHK5nJLJpGZmZlwPQKfTcXLBw+HQqQRxju72gY43aiBn8XjcE/jdyinyd4qNxztKUPV6XdVqVWtra9ra2vIE37bIgHofyTH3TSKRUDab1dzc3D0tPPBqlkgknGKaXUuTyaRDThFoAJ2xAS+JEWsUz7jthUTFjPuS+ViNRsMlEPF43CFqiJSwXtlEBwoxyMftMFAiClSIyfCzsLDgZMgjkYjm5uaUz+dVqVRUrVbdzBvWRNZUEj9mua2tralQKHj6dyx9UJokSTAHuD7ZbNb5MYphrP3QyxC34ZySLHHt7dpK4mKfI3ydpYqC6vn9WKlU0qlTp/TKK68EiU5g17RoNKpz584pk8ncsDhDkOgEdlfb1NSU5ubmVCgUHEVC8lYOoe8Ap0ty9DKctVVdA/3AbHMlC7dFg2xzpSSnNGbpWHyWfQ6HwyoWi27YHgkBTotETJLTkN8LibACAdDoLGLln31Bkgfqwt/YJ78qmlXV4e84TVuFJXi2CBkJHZQ2HDwJGefZniuCqfF4Z4AdAQz0PxsE4WDj8biy2ayrlCJQkEgklM/nXYMwA0YPsvKYv+p9rxo0H54PkByeRRAe7keaqAn4KH5AV1teXnbP6kE1khQGHFvkFiEXkg9bMGKNZW21Co+gHVZxza4zBPq2L481jfvYj65jrCmvhebYnpabZRyjf9AmQ0VBCK2yn5Vkxm/5Vc24JwuFgksYQEz885/wN2yX9dcyEjDrw+hx9SM6dntcA4palrIHisN1hP6M2AwUP1twpJAV2I4NBgOtr69L2ikkMgi9Uqloa2trn/dufywUCml6etrdQzdi977HC+yetlgspqWlJWUyGceHZpGljwaaFdUm6whwnvF43NEjbLOsJA9SQgMzfT00K+O0cPRUEi2ly1bNUCUiAIdWFovFlMlkPBX3drvtlNr8zoBKGUmedYg2WLC0OPYTB0kAYxMz25htHZLlojebTfd5zrntb6I6NxwOHcID3Q1EiX0n0EylUopEIqpWq2q1Wu78xONxF3QSiEKlSaVSjsI2Ozvr+nMICFKplDKZjAukblSa8l4y7j/JKzV7r9lgMFC1WnX9BCgfQrfp9XpqNBpOccvKR1sKDvftqVOnDhQKuJfxvBFksBZBZ2MN3dzcdEE353Mvihv9I1T8kUqmYMJ6wvVi7SG4pgACgu1PVKyoyqsZQbqVYX6jRkJHQYe1FlXJXC7njkGaCAdwzCQ6JIz9ft/T0M//QdIpdNnzwHagPOdyObddzo8kj7ABfUKWPuc/J2xTklPF49kCYeN5IvkloW00Gq5/yybAiHwEtmP9fl8rKysKh8PK5XLK5XI6ffq0XnnllQOb6ITDYc3Ozrp44kYsSHQCu6uNZAQpZhZ8An6cmK2a2aZ3298Clx8I3hpoCUG8Vb6xn8M5WOqBX/YzFou5PpJQKKR8Pu+pahFoNRoNz7A22zSNNZtN1Wo1NxTSr0ZGUOBXW/M3j+L8SCDK5bJarZaj8kjeZneaiv1JIwEKwg6S3O9UZ/k858UibQRIBAf0JkHFI7HkOvK6dcbpdFpHjx51zhsuOypwB9mhcu2hwJBcUhDwS6bfbcZsJfpxJDm0lHvZSqPb3rZYLKZiseioqdJuKtVBNp47m2Cw9tn10qLJtgDCGgotytJZbXAMIi7JM2tGkhuOaZMX9gFUye6HRaf3w6A7QksDvarVahqNRsrlcopEIiqXy5qamtLa2pqnp4c1PZFIuKIVa7WlBeKL8C3W31gkzVLWbE+P3Z69fqDo+EsSK8QRbOHAn7zYgiE+hHXYXlMoyAd5XfZbo9HQ17/+dTWbTR05ckTT09P6wR/8QUWjUT399NP7vXu31cbjnQHgV69eValUel3bCBKdwO4Jo2prB53ZRMcu3AygJFnBgP8leWSXJblKFdvFsRDEg1TwN9CKvRIdFMuazaaniRbnEAqFnDMkWLOokbVWq6XNzU03H8ev/kNVL5fLef7uT9g4V9vb204hZzgcumZgBr1Z2gKJDao+Fi2yM27S6bSnF4p+IRwyCackj0Ml+bPIj51rghMG5cGA+W0l9KD2VexlNtEheCUQJRC5W63X67keHIRBwuGwOp2Oms2mex8IIRRWqET0l2xtbWk8Hu95fx1Us8UievZIXKwCF+sZz7kk198nTWiFzWbTrTu2L5D1j0GjFHcofKTTac/1YN22tF3ef710KP9nb5ZZyizFrdFopHq9LklufSPRiUajbq2zVDFm89geFtZOP7XYT7u26A9FMEm7inVsi2ts11cSUtZqm6zaQhl+xsqQs6/SpHBgk89YLOZ6VQPbsWazqW984xsKhUJ64oknNDMzox/8wR/U5ubmfu/abTeb6KTT6de1jSDRCeyutEQiodOnT+vw4cPOKUILkCZOjsUdxRgCOhpjqWqxEPsdo+1rseiPJLcNUAlbXbNiBn7Hyw9ORPL2FOBA+J2hnXs5Yf6Gc8FZ4fRwfCBDfIb/Ww67X4krmUxqenraw59H4MDOICKRsMhPOBxWoVBw14o+KZykTZSo8BHs2H1h3ziPnFv2nz4LG6T0ej3V63WX6JDcWvQIJM0OLz1IFo1GVSqVNBwOXcAp6a5LCJHLJuiT5Ol3sH0DthBAoMYQRhBI7rnp6WnFYjEdOnTI0SkPuhH8ck6t5PFgMHCINAWTRqMhyTsYFAoXyZDtUbE9NTQbUzTxK4pJE6EJ+j/oG2Rdfi2zBSuLxt9MQ3Blfn5e+XxeL774ohPGGAwGThiF85lOpzUajVStViXJrVvQsiVv36RF8bl36Se1VEwKUpK3iGd7eSz7wc68gZJoC1IkMrZAxTWCwobxLPZ6PTWbzV0JTTweV6FQOFAy969ljUZD3/jGN5yQDvTZubk5TwJ5EGw0GumZZ55RIpHQ3Nzc69pGkOgEdldaLBbTqVOnND8/7wJYHIENkAmocb4s5AQvOF4Wart42wVlr2THOlQrfWyDces8rTOiqRS4ngSi1+upUqm4fYdq9VqOm21SNfVT06xcs22+tip0NkCU5Og9BBPMegA1o0rIubRSqFQhCWhwcpxPEjOLZHE+bfLDNbBVRwIekDQ/+kZ/hq0K22CC77KJ5kGzSCSiXC7n5nhIk3lId5PRtMt9wIR3izpAh7T0Hu6FZDLpBjySFJMEI3sbVJp3zBYnbPGDRJLn1wbWIAR2fbG9j2zTX4SBjmqT1L2CO9YiKzJhe19ezWyicz1JjvUF12uWNjYYDPTKK684euVwOFSpVHJJOmp/qJpBAaMHyTIVeL+dV8O5xF+wn7aABvpl0S6eeXt8UKVZO+1wa0meIlc4HPZQi+132bV2MBg4H2ctHo8rk8ncdUWWW2mdTkfPPfecjh075oR8UqmUisXifu/abbfRaKRXXnlFiURCjz/++OuiOAaJTmB3pcViMZ04cUIzMzNu0S6VSh5qmJVrZhHF4frVYkh4bBWM75HkqllU2FAEowcknU57aFhwoWnst7Q2+iCy2axHdY2KGCiR7fu5liNmJgjypDgenBb9SjhNgkBoJ7ZhFfUy/k7zP0Ehw+larZZzepa2gOO0EqOSd54F14VzifgC14bjZrgoyc5eFA0SGtsLFIvF3DnhXqCHyVaOCfRx5Ae1Yk+fE+f3Tq8UgrxABbL0NJJthi6SYFerVVcRpxLNNUcEhGSZdaFcLiuTyRwIhbrrNatg12w2Xe/b9PS0Q2zpbZImaAT9M3a9hIJq0QSb0LDmEdxLE4QCo9DRbDZ19epVhcNhtVotJZNJlUqlXbLSdq2/Fkr/avZGaG3cn9PT05Lk1i2G1ubzeXe/QWlmX9vttqMs2cIQ545EHmEHBCP8anjc781m0zNbR/IWBEHFxuOdgbmg41xLEjIrjkMhAcVM6My2D9YigYhLUBA7dOiQcrnc6z6/95rhM3kmDrJtb2/rqaee0tbWlj72sY8FiU5gB8ei0ajm5uZUKpVcsA53m0DeBsVUIm2g64f2/WgO3yNNEh2rLGb7a1KplHM4OGkrdc2Prb5Z/r9FgywdgADhWk623+876VzU2aCRkWyQPIXDOxOvbeLAOYFTn81mHfXAUv9IDi3Nj3/9Kj5sm2ojaBmJDrQ3zq9duEjo6G+y4hE20aFfiaox+5pMJt254JhIfEhquM4o1e2lZndQjKos5+NW9SrcTOM56/V6DsVlf0ejnTlV3LeDwUDtdtvTTyBNKtL83SbloVBImUzGzRsJbMcoakD7QzmRgtHm5qZDeqXJ2ulHcCzabNchkh1b4PBTh/37A6LEvK3hcKhsNuu4/HZtsYUdSXtu81YZ634mk9FwONTm5qYL+kejkaO4sYZ1u12XEHDOKeRAv2T/UTyz84hY0/BPFNcsCm6l9nmfZT1QVGi328538bqVx7bJjGUoWJYF26P4Z1ElZsUdVHT9WnbQExxsPB7r8uXLuwopN2JBohPYXWlTU1Oan59XsVhUpVJxi6pdcKlk0QxKHw8Pi6Xp2IouDpOFfi/KmpUN7fV6arVaHplaaDNQz5CptTMkWPwZ5ChNRBWodNlkyprlaEuTYX5WKCAcDiufz3sSqbW1Nec4o9Gd4XHsH5QPgl/bJ4NTAonp9/taX19Xt9tVvV53x4Hjss2tJHxWZlXSrmQqFPI2Idu+pHw+r3A4rHa77QJRGufH4x1J22g06ob0QaGgYs+gO5rSSZBarZZmZmbcQNaDVsFHdrZer6ter6vdbqvRaCiRSCidTrvn6E6xUGgyoBIaJYgMvRxW9IL7nAAQGg+BmA3cbEB4PbLEB83i8bij0NiGcpJFkAYCYySDKS5QJAJtZZ1JpVIe9TUCYnoTUOXyJ57b29uq1Wqq1+tqNBru93K5rIWFhV37zxrDuslcpVtFm7IFA76bAaEkEFb9z66xrVZLrVZLtVpNrVbL0SxB3Llvt7e3XcGMXinr18bjsVN4w8dxDpiHRIJjk0qKQDwrJE6JRMIluKFQSPV63SM0wcw0S2XDH4IuwbiwTICDjKoH9tpG/PZ6k7+D5dUDuyeMYCSTySidTrtqnjUCaP4FRUCdzCIOfgqW5FVs83PP2QdLr7ATrSXv8DlQGRo7bcUSh2+bQwnKrGyyPwC3VLfxeOyqfX7UiLlABHUkhTYxsWiTPXccN0Ek7+d81mo1h7xQ1bNqS5wDUC6L4uCkqcyzv3beDyicNJnX4G8qt/0AXG+O1U/t4JozpbxarbrgDR75QUt0SFzD4bCj9/V6PWUyGXdv7GeiY58Le39ilg5DYQGqlF8JzKKLNskZj8cuSOe+8tOkApsEpFDWoJ5Ziq40QWns+ZQm9DQKHLa4ArUV47ogFmHVAG2PHjPLLEqByqPf7FoPhbbX6ymZTN6W/pBQKOTEB5iVxjFb1EuSK9w1m011Oh13fkDrbaJjxwdEIhGPYhuUQiTToSRbarFFa2yvp5UMtz2UFgFHIc7ut0XmLGJke1Rtz5VN8AILbC+z8ZmloF9v4nOwvHpgd70lk0m9853v1JEjR1z1cGtry9Msaec3EMhCCSgUCkomk2o2m57J6ATxNsHxN2lKcgPZ4PLDN8eZ24ZR5DmppuGAMpmMksmkcrmc64/AYQwGAydSQKXx1SR/afAkOWC/a7XaLr62pbBJO86rWq16ztP29rYLZKjcobRk1dG2t7ddHwMNtDTYkvBARSCRoKmV/hscKTx19o2ZEZYeCLLGOcShc77hlYMecazSTvKZz+fVbrfV6XSUz+c1PT2t8XisjY0Nx+2PRCJOaeigVfMJiCS5QISEgPvodjcLQ8nhfiHZ5T5nztPW1panqmxV+XhWQ6GQ0um0ex9BMcdVKpWUTqd17NixYKbHNcz2mcRiMTcbxq6XtlBhiyyWuipNqFLj8VidTmdX3wbPn53/IsmtoxRgJLnvYp3odrtu/aP/z5rt3yRhuxV2rcA9FAo5dIq5OtxzSOovLy9ra2vLoeWsc/7+GpJ3SS7hI6GjZ4ekkj5Szg3IJdfJFuTq9bpbGy39m//Tq8O/loooTRIj/A9UUGTdUeiTJsW9gKq121Dco5AZ2E6h9OjRo+p0Onr++eev7zO3eJ8CC+ymWjQa1fLysg4dOiRJripHAEOygoPFoRIws1hAX6OyZGWopd1Oaq8eGoueIO1sK1o4EksBAFmikkk/Ec6XfhOronMtZ7xXw6Ll0XMs/go1TshWtjlnnBeOieO1yA+BMBxtkhDoIySctmJpERqrsmYVkzB77nHaltpHJdfy0HmNgIq/Ewz5qYLpdNrdN1A4SLj2QuUOgkFpsSibrbrfykRnryAH6gsUI/uckpgRIBNw2e1BSfMjC3v1p5HcW4GQwLxGMkMw7K/C2yq95JWu91dfWR+kST8HlEO2aRFv/s/a6n827ev+9W+v47AI9O1GEvBD0HEsIobPyGQy6na7DsGxTf82UbNro513xt95Zlh/LY3ZPtcWceGZ4RxZ5IXPWqqoZULwf6496wdUZPYlUFi7PkP4KB6Pu0Ij98617u973cLhsIrFomZmZoJEJ7B706ampnTy5EkdOnTIUcao+Nr3EMg0m001Gg2lUills1mHshQKBWUyGTdFHaTGj9BIk2o3/QE2wZF2J0EkHwT8JDLIddIUTzVUmkx0p5l3PB5rfX3dg4bYfSJA9wcH/iZT5ufwPhwecypwhgQH9XpdnU5HrVbLQ1MhqWFbtiJKda7ZbHqGCDJHw6q+2cQylUq5ai77zO/w+20fFMHLYDDQxsaGotGoFhcXXX/S9vbOQD6SQ1A7Koj0FoBCocTVarWcSlAymdT8/LxTbjooAW8+n9cDDzyg1dVVvfDCCy556Ha7ajQamp2d9RQNbmYSSLLCPcz9SLXbKjXR9E4DNzRQAieoa1Y9ivkq9B0wP4e5HQSalroT2G5rtVqqVCq6evWqGo2GS3ikCYfersOtVsslpdJkrQExtXQU7jWrKGZRC+45rhuoniS3brFGUMy4Fg0VFJx14HYXNEKhkAqFgusJpLAwHo8d+wDEGZSdZMP6HL9cPs9LKpVyPaGRSETtdlvdblfr6+uS5Ohz9D1aJoMk1weJ0A/XLBaLuSHWJDkkS1wrez/Ynh9povDoRyaCHp1rW6vV0ve//30tLS3pvvvu0/z8vN75znfq5Zdf1gsvvLDfu7cvNjU1pTe/+c3K5/P60pe+dF2fCRKdwO4qo8k1m83uqZbEezArS0ywbznGfjTAignY7eJM/IuxpbpZZ8H/bWWNfbN0MBwzCYpVMoP7TEO1NQJ++z024QE9sZKt/oqqDSxtpY7X/Q2smK2G234gEhm/Ghu/+6u9tneC93NNbZVwr4qr/Q57zqlC2mvOe0maoB2CwhGgkWgikZrJZFwV7V43hCkqlYo7J7biTpLAe+11eyNGQAvlhr/xdzuvhWcJhIfrLHkRAirPdnYOx2ErzpYi5VcDC8xrBNadTschafac+QNaSZ51z64/e6Eo/vXRosJ7fQfv5/m2aK59j6W/Yrb30j7bflTxVt4L+BnECCyVl6KQRf39yBbHYRUs/eea62MLWRQrbMHCHrf1GfRScq5A4Ck4gArZZ8+iTPY62GfPb/jbg4agX4+RoCJCkclktLS05GbtHUQLh8Mql8ueobSvZUGiE9hdZeFwWKVSSblcTufPn3eN09Afpqam3DwC+mNY6OGDN5tNj8OIRqMe3X+qYXCRQ6GQ+w4+iwOygRqfxyGxeCP5TD/A9PS0G9bYarWc1Ci0NeZPMGATjq61fr+vRqPh1MTYPo29vN9St6RJDxHbBNmxAgE4SNTJcHLlclm5XM59j7+ptFAoOJU1m/TR00MlsNPpOOW2qakp5XI5D+1iPB676iznjH2SdoIEkC6rumavM0FsPp93nweZwqgk0htVrVbVaDRcn1S321U6ndbCwsI9n+zYAIj7mkZvXu90Oq7H7WYoVZFQNZtN1et1T/U4Eok4dJHhr4VCwdF66N2xErb01cTjcadYRcUeig73F/cH1e1Dhw4pn88H09n3MBLOarWq9fV1t0by91qt5pIgaSIcwjoIqjMajRySa6m69AVGo1EVCgXl83nFYjH1ej3lcjlXcAiHww4FB8HY2NhwKC73LipuoPX0hWCIAli0SZKjGLPfoNismf5E440Y56dYLGowGLg+M3+yReLH/pIU2YQ9kUgolUp5pNJ5LjhP29vbKpVKikajTqwBZBSaNeswVF7WBM49149rxb6AoLGv/IsvgH4qSVtbW7sSmgDRubZVq1V96UtfUr/f16OPPqrFxUU9/vjj6nQ6+vrXv77fu7cvFovFdP/996tUKl33Z4JEJ7C7xqx8szShadnKH68R3NjqMBVekB37GVul4l9rBPX+RdpSNuznbHXT33NjlWeotJGQYBZl8aMx0qR/gc/iJNgujsr+nf3CuVmzyJbdtqVG0OgraZeaEn+zKIo9BxZZshV3W1m054pzwL5aoQI/asQ5lLyKXLZSaLfFce51nRGGkHb6uMLhHcEL7r2bFejcqcY58ysmIaFOQGIRH8mLfPr7NtiGNdBD0AE7J4kAy1bd90L4+JztI5Mm/WD+YIzjYxt2n6GTBlXl3UZSYiWgJS+avVefoLS7T8feH/Y9Vt6b60/Si1qlRYL4DtYa25tJkYiCkSSHoPvRa/5v1wIKTv61ge/lWN6ocZx2e/413/YpWeTf0oFtIYhzwTknqSdx4zWLnPtZCXv5Ps6LLfLZQpft4dzrOLhH7P5f674IbGK9Xk9XrlxxCE4qldLCwoKy2ew+79ntNwqg/X5fuVxuV2/vq1mQ6AR2V1gkEtHS0pLm5+ddldfODbBOA0Rhr4AZugCLK9VHBn5SMSTAZ/ElOZqdnXXbQ93H7iOOB+djaRzpdNpVzZiNQDWN/YYmBI2Oap2f19zpdLS1teU40+Px2KEkoFvXoorZZl348pFIxM0kaTabrnpn5V2Hw6EqlYqbRULwkUqlPBQJZEwJJqEakdhMT0+7CrANaEEI7LYIuAl2qOKsrq66iq7dV97HYFQqkQTq/l4lW73lfBEA0cOztbWlVCqlY8eOuR6re9Xi8biKxaJDG+Hm06vDuQT1ssFROBx2fQE2cbZFB96/vb3t+j0IcHltc3NT3W5Xy8vLmp2dVS6XU6/Xcz1XnU7HqfyNRiMnD4zyFolxMplUv99XvV53x2fpOuPxzuR3v2BFYF5jnWPwKteJpJfiE/dDo9FwzxIIO88b26M3jt5Jgmc+DwrDrDQ7H4y+R4sacU8xB2p1dVWlUknLy8vufgChZG20CmKj0UgLCwtOdZE1yCYK0iRBJ8l4o2ZRauiBoEjIcHO8zN6RJsptuVzOrZEUp+hFxTeWy2UPytJoNDziPblczpMoplIpDy1cmhSGOP/8DoLDmohSokVZ8bkWBez3+55ihFXWC2xiW1tb+tKXvqSZmRmNx2OVy2U98sgj+uIXv7jfu3bbbTQa6cKFC5qZmdHy8rKmp6ev+7NBohPYXWFQp0g0bDXK8rf9PwQ2/l4O22diHShODOdpP4P5K9a2ynct+N2iTjgQHCxBoHXYbBuH6u/RgWJARZMEx79flj7i35+9quwEAdapSfIkHThJ9pfjBw2yFD6SKlsxtOeD77SvWyTO3yNk+frW4OLbSij7vdfvthJs7zFLVWG/SQq3tracMhdJ7b0WGJP0k8hb9IQkw/Y3cf5A8CzdRZpU7S1NiWtEwOu/f0BqCKJtQGoDbavi50d9LK2RoEvyztThfUGA9epm+wF5pu3aaQsTJLWW7mVReJs82MGsdp22CJ2fKmXRWbbDtbfCKnadslQu1kyLJnN/WISJtU3aG725mc89x8q6RDFKkqdHx88S4Dm0RTw79NmeC9Y1+3zxvdA1rS/1+wLbB7UXy4D93YvV4L+W/nWTNXYvBO2gG72jjFWgsHgQVetGo5Gq1aq2trZ0+PDhGyo4BolOYHeFTU1N6Qd+4Ad05MgRhUI7k6NxtnaQHJVcKxlLdZ9KlH/xGA6Hbs4MiQb/tlotD2KEsyXxIHFByclPyyLos5LSVDxxToVCQdvbO/MNLMWO+Qqo42Dj8dhVLWmgB0UplUquN2k0GjlFNIuu4CT9DdhsBxoP6AXVdAJO2zTLuYhEIh4FO84ZCJUNZjlvzBBivk2321U+n3cIViQScRV39tHOSqH62O/3Va1WNRwOd83uYKI4M1TgrENPswlxLpdzFL3xeOyG+sFxv3TpklKplI4ePap8Pq/5+XkPancvWDqd1uLiotbX19091e12XaU8Eomo1+upWq0qGo2qVCopFou5AJJK++zsrIc6hpoedMBoNOoquwTS0BJsbxfva7Vabp9AEmZmZjyUHZBNAkFQOtu0CgWRzxF4+SlugU0M9AXkE1SEQBsaI895Op2WJIdKT09PexJnElAbPINCW6RVklvDrXFNe72e6/dZW1vzJFqs0/TvpFIpNRoNVSoVh0whSkIgvlfhw2/s/800zutwOPScZ/rRUOJkrWJ9Br1m7aSXiXMP1RBfyblmPU0mk0qlUioWi47uZ8+fJIfyW5q4TVJIqFgzpclAb76Tfc7lclpaWnL7aYsoqOYF5rXhcOiGxkqTxPcgJjrb29t65plnFIlEdObMmRt6Dm/YQ3/uc5/TT/zET2hxcVGhUEif/OQnPa//s3/2z3ZV1d/73vd63rO1taWPfvSjyuVyKhQK+vjHP65ms3mjuxLYATISgmKx6JIAfljQqbxz39kA34+8sBjbxMT2+/CzF3cabrjtD7DOwXKO/ZVPadLXw7/NZlPNZtNVuC36xLHbQB/aG8mEpXD4nSELoz8gt/tjedf8bvuJ/D+cG6qD/PAZEjo/ekPCJ02qeLaCi9MkGbLXAVohAQ7bZtFPJpNu0Co9JrYfx94ffI7r7p/FYiudUBtDoZ1mfAL+Wq2mRqPhnJC/x+puNWgmBIE2EZYm1X2SH65Jp9NxCSSJMSIDjUbD8xq9Xtzv9sf2WvlVvmxAaosRBMp2uxaBsMdin1GLsvqf9/0wjsOiZxaN2i+zlFerhMdrkrdfB/NX/bkeljJqEQR7XWOxmJNf3stsb54NwP3X0U9rZt1mLeD77HHsh/mV0FhH0+m0S+wtIgKKRVFJmswj4lmyM9Lsd/j9Ae+3aDzXmvNnnxNYBta/+K8h38v6bIt911pT/H2jge2Y/znai9FwEAxEZ2Nj44b6c6TXgei0Wi09+OCD+oVf+AV96EMf2vM9733ve/X7v//77nf/lOmPfvSjWllZ0d/8zd9oMBjo53/+5/WLv/iL+uM//uMb3Z3ADoiFw2EtLi5qeXlZr7zyijqdjmq1mqepMRLZmWyfzWbd4irJJdF2gYjH40qlUqrVai5Io2fFnwDYhEWaTCvGsRBoW8oXFS2oNaiHTU1NuUQFx3DlyhVHRaNKboN8a81mU2traw5hAYmCSsT+5nI5D4WE/bY9Kfa40um0q8yi8oNjtNWjUCjkKrpU/Knclkolp3jX6/Vc/xLnI5vNepIj5mZwfuLxuEPRrHMfj8fuWiO1bZtxqRZa5SDU8SKRiLrdrus7Go/HTjUMtSibNNIbkEwmtbS05M5Bo9HQ2tqaBoOBLl++rHQ6rW636xTgCoWCyuXyrbj1b6txPdrtthuqyowpEDqKC7a3iqoxNCUq7PQZ+KvyVJztPcC1QP2sUqmoVqupXq9rMBgonU4rmUw65S8QOj5brVZd0IeaWiQSUaFQcIiRDYClyTNOcr5fAQRVcSq4BKw0H++nsc6gZEbPHQqMrAG2uGApg/wLvZTn10pIW+GTSCSiYrGo+fl5pwppjXWWaw4KvVdixdqYy+VUq9U8Mv4gOCT2lq51O431mAQjFAq5BIcE7fLly56hx9KOciaqmNFo1M2T89N+QYdoYK/X6y4J2d7e1urq6i7aKfPpWF9BPXnuQWW4FtVq1T2TllpaKpUUj8eVzWaVTqf3DNIp3t0rxaLAbo1tb2/rxRdfdIjuXmvDteyGE533ve99et/73veq74nH45qfn9/ztWeeeUZ//dd/ra9+9at67LHHJEm//du/rfe///36zd/8TS0uLt7oLgV2jxvVI6pCONZ4PK7RaLSLbkD1F863rRQRuFKJolmSRR+jwmV527aqBXJk6RLSBA2hakUSkclklMlklEqlXMOm7bHBIRNs2eqkNQIEfqhGIj9qkSXLqbZIj0VPCD44TpypRbfsd1suvK302iCHc0oyyHnj/HA+OU/wzPeqItr3XwuZ4/9UOKnaWv6+7QWx9DeLJhHwQgux/SH2WDudjnPk0WjUoRb1et0lSbYXwVZQ+U4SzWaz6UlAbaWW+5N+http3GckH/besf1aVJT994Q0CTRJSHkmCOJJ9EHXLC2I7UNDtD1fFlm196QfmWm3264KHg6HncSw//4hod7PREea0GAphIBc2pk1t9Ms4oz4i1+x0iLK9rxSNOAZgipFgYmCibR7xg4CKKCpfrPJKZ/DF/jn7xDwk4hDa4a2xfNle4bYJ7sO3Q5jDfKjjfbac49YiqcfyeIe5vhJdEiS8KF8p+3zsYg6/sgOesYooNkeIJ4ji5rbnh3LnLAoWmCBXY+NxztCNcxQvBG7JT06n/3sZzU7O6tisah3v/vd+vVf/3WnkPDlL39ZhULBJTmS9MQTTygcDuvJJ5/UT/3UT+3aHlVFzKroBHZvG9UtqodUb6ko2oWaBbRer3t6YWyzOw4uFospk8kon89Lmjg2uML1et0lO9axUpm2Q+2so7S9PgTL8/PzyuVyWlhYUCKRcN9Rq9XU6/WcI8IJk6zsFdxYig6Tx1Eyy2QyHrSF45YmyQdVPWhj/qqr7WuwFUaoW8zssRQIAgwqq/l83vVlWKlpv1MExYH3T2Bnz7m040xtk629NzjfJDj0aYxGIydNi4KYTWhx+pbimMlkXFLKvll6DUFGtVpVJLIz5wVUj/N86NAhHT582FExqMwyd4Y+hoWFBYXDYV28eNGhGpyTfr+vjY0NxeNxzc3NKZ1O35DCzM2wwWDg+ti63a6rCHM9CVjogQAZ9AdrBK6lUskFxKgmghBxn/EccP8SCHFf7JXccK+Gw2GHxtJXtr6+rmQyqVwu52jSVsp6PN5RDrP9aPtlJHm9Xk9ra2uOGjg9Pa3p6WlHo7ydxnW6dOmSLl686KiFrJ82CedZsvQ01msSCJ4p0BWoeaxF0s76Q19iLpfbc78o7NiAmXuTPhM76LfVajnUolQqubWE5xyEKpvNup4f7m/26XYE5RZpt3+zPZXxeFz1et2tz9ZP/P/svXmMZdt13vfdW8Odxxq73+v3+g2kSYoSJQqKRDmDHBmUDCNIIAqIhSBWBhiwQwmIBTiGjfxh2YhlJIADGJJsxLAtjzFgC4ECTbYGUyJFyhKfOJjDa76xX7+earzzUFX33vxR+e37ndO3eq6hq84CCt1VdesM++yz91rr+9a3mB8EiARDHJMEDUHhcDjU/Px8eC9AelBjczU81gGCFkfIGSNPRJIoi9cGUSNKEsMDuiTwSex+NhqN9Pbbb0f6uz2sPfVA54d/+If1Iz/yI3rppZf01ltv6a/+1b+qP/Wn/pS+8IUvaG5uTnfu3NHq6mr0Iv5/iPPOnTszj/kzP/Mz+umf/umnfamJPQOWSqW0urqqS5cuhWw6zpY7wnHlFjYBaYoeSNPGmQQzbNaewWORdxUfkA8vjPeCZ0ckMC8apCifDYbNBEcfPraksKHMyiTGefLQG5AjJdCJ9yVhwyEQ4AtHM17fxLhxTr73zKkjUa6kQ4AI3cEzel5gTnDimzZfBwfTDtwebPgxnd/v6IEjVjjJTrPgXnjWzDPul5qbzc1NSYeJFaeIsJlD6YI2yL1412qcCyhdPGsCTxoF4iRw39vb2yGrDTUQ5/I4jTGKo5mzggyf77yLFFPzPdnd7e3tUHfB33BM/sVBhdLG8+C95tn7XADZjSOpjjRIisxZ7oHs9llqVriwsKCVlZXgEDIeR9WqHKdBRZIUnGh/5+K1csxVnHJP3LgSGOsg0vbStM9NtVoNNNSjjPnl9SEEsCChjvSynhIs8i+y5KxDrDfcl9OYT8JIyklTNIZ9YW1tLVBwqd+KI2jONvBEAAg2e0JcdMMTNY4ek4hgv4mvszw7v24Pfjh2KpWKqN/xr+8F7OuJHW3OQLioFkcWH9ae+ur5Z/7Mnwn///Zv/3Z9x3d8h1555RV95jOf0Q/+4A8+1jH/yl/5K/qpn/qp8H2r1dKVK1ee+FoTO/uWTqf1wQ9+UC+99JIODg60s7MTHFGCCBbvXq8XcbI82+U8bJCbVquler0eNmh3ftbW1oJTtb+/r62trVCE7RnDTCYTiq89AOLcHHswGAQkAJ47zm+5XFYmk9Hy8rKGw6HefvvtsHHFX2o2ZIIa7suLl6ljidOIDg4OdOfOneB4c/1ebxOnpUhT2V8y7tBYcILIvPI33W5XkiL9iuI1FSgf0TXbC8YlRWSEHXWJO6Vs4oyTUzhGo0N1PZwF6rCYA34+adpEcmdnR5PJJDxTlPe4dlCijY2NgNbwXHq9nm7fvh0ynJubm6HGZDKZ6OWXX1alUgn1Irdv39ZwOAxOV6lUUr/f182bN4OMKBnnSqWilZWVp/VqzTQPxH3s44XKBMfQyXgXNzc3A3KK09Tr9bSxsREU3QheCZRAhEA1G41GyMzz/klStVqNoHr0zYkXxvOsXMqWOe7IEO8ByoRnwbLZrD7wgQ+o0+novffeCwgYyZeTNFdhrNVqmp+fD+uEq3Qxd6vVqqSp6EChUIjQcZlHrVYroDAE+aw5KysrqlarD+Tfx+uqWPtoFEoQRIKp2WxqeXk51HDW6/WwPkJr47qoLbxy5cqJBpgEgZLCmNDXamFhQbdu3dLm5qY6nY6azWbY21j7GI847ZoAggCO76GkeWsCEHnocaxpHuBKiuwPk8m0VxoG8wGKKms9AQ971/z8fEgOPWqW/iKZ03PPylr1LNmxv8Uvv/yylpeX9eabb+oHf/AHtb6+ro2NjchncGCPquuBXpDYxbN0Oto/B2fZM1e8+DhG7gyx2OOkOXdbOsxA43TiSDuvH2cO2WKaelK3QJbY0RfPOnhNAfKqHM+RE6felEqlQM8gk0pGmoBGUnAGyFiSvXNagGf9cEBBVpw64BlBpx44yuI0QJxEzuHndTRJmmZrcUihosazl67CxjEYVw/aeJ4cnzGk9oNj4ozzvdcw+f9x6j17yVhJChQZHAkCPv4eBxB6bfxecAKz2axeeOEFVSqV8BxwxBDMYNxAL6BwMdac/7hoTEhH47h4YOj/9+SC12o4wun0NeY2wSYOEPQYUDIojHt7e5GMulNgHMFlXt8vMYAq3GQyCZl86Fflcjlcw3EZ88AVxR6UlSXo9ff3pKzX66nVaqnT6UTU8Fh/WGs8mcN6LCmMbbVaDT8nSUC9jKRIXZdLJj8skuKoAsE5Yx2vbZEO51C/3w/Ud0eDxuNxqCkEbQdxOKmxZxykaH8nX5t97XIKsTTtZcVe54G/r0ms7Zj7VjwfZw148oh3j/OCxHBsb/zpzwHBnlqtFmq1uG5ELs6CwuBZNSjTpVJJxWJRpVJJzz//fFD/TOz+duyBzvvvv6/t7e2gHPOJT3xCjUZDr732mr77u79bkvTbv/3bGo/H+t7v/d7jvpzEnjFLpVK6dOmSrl69GuRmcWjiBfMsvq4S5g6sIzFs1q1WK9ITYnV1NRTDsmnMzc2F7t7NZjMcj2NzzjiNyjPgZL2gIeEsSwooEfzwarUaqRkhoOr1eup2u8EpLhaLGg6HwXHwTLWjGk4NKhaL4bOMb3wDRUY5XqTLxscxcUi8uJ+6HxAOpz0RJHIMUDmnfnFdXlTsjj4btPPzuV8QHGkaPHlhLc8fB417hkrDHCEIo/8S1wJKA9LiaB5qcX6v3Esmk9ELL7ygpaUlvfzyyyqXy+Fz1WpVnU5H165dixQYE8i4WAFZ1qWlpWMLdAaDgTY2NsJ74UGNZ2z5uQfZmUwmZMpRYWKscaZciIPMup+j2+0GFUTeEQ+yPWBwx4h33+mJOHfUs3U6Ha2vr2tlZSXUviwtLR07UsK9gVI6teooo3bCUeSTsna7HVBlAgGXdmf8WR/o84VTTK3L2tpaOB7JHRICBNEEqSCjOHIPawQr8ZpEryHkXQelJeBxqiN7ATRBEl4nieiwxkiKBCpex8LccSprvGaNsXXaNAkVH/M4hY2126WmOS/jyTvHnkAdH3sASK4nH1gri8WiLl26dE8QOxwOtbu7GxJ4id1r1BxKh/t+vV7XBz7wAb399ttJoPMQ9shvcafT0Ztvvhm+f+edd/TlL39Z9Xpd9XpdP/3TP61PfepTWl9f11tvvaX/5X/5X/Tqq6/qh37ohyRJH/7wh/XDP/zD+nN/7s/p7/29v6f9/X39xE/8hP7Mn/kzieJaYvdYKpVSpVJRvV7X1taWRqNR6Nw+yxHzBdopE3GUAOeMn5GR2tnZCU4ZTi1BCpk+z2rzd74pOaTPhsWG6hlpz4RPJpOwYBFAQL2RplkvKHceRHkwguE04+BzTK/X4f7jRaGeCcVZxMkl6Ig7EDipc3NzES4414Kj4/xyr6EgkHEHiAy8qyuxGcYDHVAZ5gY1XK6cxv2Rmcbh8qyopDBvqDeJOxjVajXMDWgdOACMBcEez7Ber2t9fT3MLbLk9ImBjkndQj6fD8EtReEEBjw7hBOehvX7fW1vb6vRaGhzc1PdbjeIeuC4cH88Oy/gZ243m80wl6DdMH6cg4wwzzsuNhOXmXX1PpxBjs9YStGAAiqc1/N5FtmTE0/b6K0EWgwts1QqRepS7mcHBwdBDrnb7YYeYidhrlI2Ho/V7XaDdLE0fT9IUhQKhdCYeW5uLrwfjC3OO7WDHMfHgWx/XIb8KHNlRX+2vhaC0rBXQPOF4ubUVadprqysBHn90zIfGxI0pVJJa2trSqVSESQ13vDWUS4PSEEIWUt93fM9gfdoNBqFseSZSVOKr+897GFeO+T0UYLfWe9bs9nUu+++q0ajcVzD+cxbq9XS66+/roODA126dElLS0v6tm/7NrVaLV2/fv20L+/M2yO/yV/84hf1J/7EnwjfUzvz4z/+4/q7f/fv6qtf/ar+8T/+x2o0Grp8+bI++clP6m/8jb8RgUf/+T//5/qJn/gJ/eAP/qDS6bQ+9alP6e/8nb/zFG4nsfNmBDpLS0va2dlRKpUKRfs4i2S9HRFg8Xdn3Asq/fjStMifYtaFhQUVi0XVarWQreSYbCo4Ve4wOc2LgIagyylsnvFis2k0GpEeNo767O/vq9VqqdFohFofNjQcYw9mtra2gpOFYpYjWY7MEJx5MODX67QEnH2Mz+AQcQyn68DDJwjxfj0e6GQymUgTSacakUlut9szN12CUPoB9ft9ZTKZoADnqB3cc7qkw93HGHNqdCqVSsjuZrPZQEMrlUoRNAdnn2eMmhrd4aHmOmcd9AIRCZwPgqjNzc1QxwR3nmd36dKlpxbo9Ho93bhxQ61WS5ubmyEgdUEEn9PMWxx3+mAwDktLS/cEOqlUSrdu3YpQdHhWjLXX3GCgADyDpaWlQIXx6+I5E1gzh3GI/X3iHT8OpISapI2NDTWbzaCIiMrXw5ir3uGUn5ShXkgw0Ww21el0AqLpFE7eay/uJ+CRDt/PXC4XFPocdXGKUz6fV71ef6jrA83zdcRFWbz2g2Cz3+8rn8+rUCiEgJlAh/2gVquFwv+TroeKmwcEBAzlclnr6+va39/X9vZ2+D3PhGQJazmJHNb+YrEY1mZPyLmwSDxp6AkgGBSsc9I06GVPBBXzfS2fz4c+OrOs0Wjo7bffDrVRid1rzWZTX//615XP5/Xxj39cKysr+vZv//YI6JDY0fbIgc4P/MAP3MODdvs3/+bfPPAY9Xo9aQ6a2ANtZWVFtVpN4/FY7XY7FOHj3FAYK+keBxyHXZpmq3Dsce6l6IbiP4cu0ul0Ah+aLxxVaBseJDgyks1mlc/nlc/nVSwWIxQC7kGSSqVShJNN/U+8jwT8/mKxGMmse+aMz3nvBOnQWfQx8SCMIAp6ATQk53p7HZJnDOOOi9foePDB50ejUUS5TZrW8BwcHARaH5s294XaEMIASKay+XptlQdkLj0OMkEwyfl4pjhFBCcExqAzjvpJ047eBCoEJ1AJvSC30Wgol8sFp2JnZycEBjhkTrEkI10ul9XpdNRut9XtdnX79u0QHOHg42B6IP6wSMXBwUE4tgf9oIAE8cwld4qcPkbwyfxnbjPfms1mQAk4R7fbVbPZVKFQ0MrKSqgLAY3BWeI9gMbGNfD+41i5IwXlC8SBZ5XL5UJ2HOThaRvv6OLiolZXV4M0/qPQDZEWJ8g9Cccb1HgwGCibzQYa4fz8fJDyx4H29YQgl4QGwYQLojCHfG6CVEAVfljj/QGtmZ+fV61WC/OC4AcRBfoAgZByH/T0IeBBmOYkg8pHMSSxofix7khTqtvi4mIYBxIB/N4b5oKw8zvGTJoq1SEYMx6PI81LJUWaRUvTxGKxWIwk8XjPqNuaZa1WS++9917SNuQ+1m639dZbb+nq1auSDoPyV1999cRQ3mfdTg+bTSyx+1gqlQrdsaEgUHAqTRVqcPbJMLKpOtLgvT/ixb1OM/PaFupLUATzbCaOPE6UL/iSIhttNptVoVBQPp+P9OXxv0H1yesUyFLGrw9lKkcpPBDjHnH44/1y4psNjmOz2QybUZzW47Uy/gW6wHHI2sfRLg/gCNZAVeJ0Onpt8Cy4Py8qp86Ja8DJZcwlBecFR5ux8t/xDDk/zlu9Xo+oF3ltyHg8Dk4TVAvGh54cICGg2AcHByGQBI0BcYPf7k472eb5+fkwb3gHoFB65rpSqYR5GK9lepBxbVACvZ6LIDrupDpdlGCQwN3fP8Ya1BVpc0nhfjqdTqAt9Xq9UCPhKJDTkvhb3hPOi7CAO3bdbjdQZnj+zC+SD8dhPDfQLEeSHtagO45Gh/2oTsL5JiAfDofKZDKhcSnjxXuASAEBJEmJg4ODIEbhwTGBbzwI99qcRxEbIvHF8wcB5XfMR54B76mr+TGfSUZBSUUS+ywaa3+hUFAul4ug8k6TxbLZbFhTPLHEsUCzeEaMydzcXCTBRZKIdVlSQKT9OOn0odKeU+PK5XJ4viSUPKEoTfscxZHcxKYG4k6SrVQq6cqVK6EPYGL3t7P5Rid24S2VSmllZUXPPfdcKBhnQcZhjqMTDq1LUwoSHGMPcuK1B56xBikgOFhYWNDS0lKgSxCIcF4oSJ65xMEhA4dTcHBwoI2NjZBd9Htxupg0dTpxFLe3t9VsNiMF4FwvmUruBznY3d3diFIbQZWr+0DxQvWIY3idjQc7TmUjuHBHlI2S63FKQjzwJHjAAWVDhcq1u7sbaqTG47EqlUpwbLyuCYebcfPrJkB0AQGfHxQysxlDHXTE0K+fYObu3bshc5nL5VQqlZTL5bS8vByCYlCs9957T3fu3InUpuAkeqE/Y9ZutzU3NxeCe/o6QU1BKrzRaGh5eTkUhDsSyPceTM7NzalcLoexGAwGunnzpvr9fpA9x3GFromDhRIhGV+a3XKdkrS8vBxxmJrNZqiJQOSC3xOoQSfkHWVOkIFGdZMEA3MR5xtKFNltHFfobQS2UDiZ68dlLqLgKOrjGM74cdQSxW1/fz+IraBMx3tG01hQKijEBJiZTCbQFYvF4j3oDf964AeSQ1PdRzHWWEeUobAiY57P54OqXqVSUafTUavVCnO6Wq0G5I3ridfrnSVj7KCwEdS4YIcjqbwPjsqCwvIOgeZzHEmhpg2qmye2CCRZ+/kda4xTrZ2aCC2coN2fN+tafK1NbGrtdlvf+ta3dPfu3UD1XF1dPfEmwmfBeM8fJTBOAp3EzqyVy2UtLy9Hir1x8lhsvW7ARQGcI85i7ZkkHB+cK5x1sl9O24LfDHedLDVUKpws5ypDI8CBJ4Dg5fSCda4n7tBwbwRSZDHZqHwsHCFKp9NaWlqKyDoT6Dili3ujFsYLT/24cTSHYAUHksDA6XAcgyCNDdKDEN/YCL68bokAjmAQ54rgyumLHtg4tQpqC046x3b+OdfE7xkHrzdiXOPPgzGVDtVwcKL4Yh7t7OwEBxLnXlJQQnI6MAGQz6VsNhsJhkDhdnZ2wjN2hJBnAzrEePvYEWDQm4q5hTgAPVTcofH3BIqTX2epVAqInT8/AiMXHfA6NBf14F/mDwFRr9cLz1iaClhwb6B7Tl/0eQ5dh+d0XPY01brceTxuY31zoQmnkXJfBDi8IwT4KFYedR/8yxykrgdlyYc1pwo7ssgcQJWKZ805er1e6EmDI8/cOo5aradtzIVcLqdKpRIQbp4LySHee09ugI7jJHI8qNQgOlKUuso+STKIwPIoUQ0PljgH33vykEDH6+sSO9oGg4Hu3LkTglKSPw9b83fejHf+YS0JdBI7k5ZKpfTyyy/rYx/7WFDKIuubz+cjGx1ZbhZyFlWy6SzObAIO2eMA44DyN55VJjMvKWSWcZTcoea6PXOGk+7BFrQD6fCFvXXrltLptNbX1yO89u3t7eD07u7uhjoVl/nlWj2jBm8efjVUGknh2rz4GyNgc8fB65uc8oX578kGS9NMNHQhMu/8jp97ZgZqU7vdDo5/oVCI0CFwWlngcYy5bjZtR9yc0oaz7dLEzz//vDKZTAjIoLdAV3TVL2lav+UqkQR6PHOeQy6XC1nlxcXF4DRCQZu10TsNzO/D0Q16l6TTaVUqleDQgUjxznBNBJl7e3uhgJV5Al0JNT9qWZaWlkKwQS2PoxVstAT+Pie8PslRS+o3mLPcH+IRy8vLYYygP4JAMY/icxSRi2azqWw2q+Xl5UjgCBWrUChoaWlJly5durAOwixjDe12u9rZ2Qm1MzThJPhlzJhLvFcEOu74ukMNDTSuFsna9Khola+3pVIpOOY465VKJdIbifWP2in/rNdGPivG+kYAR28gD0gdNfZaSNa1dDodkfoGHeO4nkCAHihNE4gkPDzJ6BRpjkWwDKvB1exI9iWUtcQexUajke7cufNIyGsS6CR2Jo0anbW1NTUajUgBf1wWlI0OZ5MMOYGR1/V4hl5SCHzI8nEszoUjDi2ODBefJWuMI+1BjRRFLfh7Ps+GTyE4cqDQ81A+ovgcBMipGl6A62gIjqI0Lfrl/wRwfO9jx9/Fe6ZwPj+G/x50xZWQcLgJIKEikckvFAqh6JVz9nq9gBSAxBCY8mzJ6HKvnmlEuQznxetNeJ44ABwHHjm0NDZrrt/RLEnBSfd+OI4C4oC707W6uqpisajt7e3wXOPjyv/jvPn4M46LLYAaEuwwZowrX9KhA4JMO88b9AZKJNdeLpdD8Mncc2QU+grXEqeGTiaTEGzz87hgCJQbnK2FhYWII+xUnPg8YI4uLi6GbLHXDIDcHRwcBMQSBajEpoaD6iIDJJacXhqnFvlaRsLIzdcfF2HBXGr6UY15Qd0I84N1xesbfY07D+b7EEItqDyCgHrNpz8HxohnzO+c9ubjFF+PnJ7tz9vpzv7uMkdI0DgKS6CTUNYSexSD2r27u/vQf3M+3vzEzqVBb6A+gzoXnHRHcGZllQhIjnKuyGaR/Yo7yKPRSHfv3lWz2QwOJb0IgPLd8eJ85XJZ9Xo96N3HebT0EyAgoYkidRnZbDYU5vf7fe3s7Kjb7YbgyFEVNjVkmb2Q2x18KF4EBUDgOKvUO5AR9DFj4yN773USKO3gVOIgQYvgHE5JiqMMzuVmI2VjZPPlenC83Xn2WiWcWN+4mTM48/6cstlsKMJm0/UeIu7Ys8Fzv1C4yEoirgCysri4qCtXrmh1dVWlUimMA8eniSXHdSqhF3QzXjxPAlWetc9ZAgYPSnE0USKjbgsOP3/P2HFvSGenUqlI0MdnfZ4wR8rlcqS2CBU1nqU7P16/Aj3VEwucm7Hd2dkJNDjmBFQ1KFU+Z/x6CXLOi7P7NK3X6+ntt99Wq9UKiQOohq5cx7wiWASldAotxjuMPPWshp7x3l8PYzjzBM3QEJl3BOAnUdN0WoZSHAIf7XZbjUYjvMckHxCN4H3mHZYOnw/UPQ9wkK72hCG/4zhekxevAwRtZfxBfUHHPWGEimTSPyexR7H9/X298cYbEYn1B1my6id25szrJJxegDNI5tkdZHdq3RGXFAIb/p9OpyMIAOekVsedZ7Lz0K7K5bKkaeYqLnJApg052bhzxQYNHcBpWFCivNYHRIfNnS9QFc90x6/FHWDGyTntjoz47xkPfsaYef2J19xwDOh8rgTE+bkuzoFjxHNFVtkDCpd4jtPsPEuJOTLliBTXQNbfgzTqVPya4ugR48k5XEmOueJoEkgUUrvVajUEpNRq5XK5MN+ggbnDwLFxIjkfzojXaTGuzBeOw7vAvwSyOLAIWHBu7svrrTzgAm1x2hLvFu8p7y31AqgXxgPnWRl3FySIPzeORQBF3ZIHek6nYUz8mT2OY30RbH9/X7u7u6HOkHfXEx+sa6xfuVwuJD6Oon7xc6iivmawdj0KbcyvizkOgonM/0UwDzj8vXZ5edZv0Fnea9YFr/3yerU4EyK+52C+pvs+DILEsVnTObc/I4QQSFwkltjD2Hg8DrT+h7Vk1U/szNmHP/xhXblyRSsrK5IUir9x5LyxGYuwZ7mROCXgwRyCx/HCCWTj8J9LCv1N9vb2gmMFL937M1DDwqbhMsHxDZjgxxd56mbG47FarZaq1apWVlYCGoNildM04Fnzu729vXB+gicyqd73YDKZBK66F+mTqSXIiNebuKOzv7+vW7duRZo9Qr3jfkAhqA/CmaUxqtNMOD73RYM5spLQsDh2u92OIA1LS0sReow751wTstzQ4xqNhlqtltrttvb390MB8wsvvBCK30GDcLKgfOAQSgpyr94PKZ/PB2eeuQv6QCb64OBAq6ur4Xf8rTsWUNC2trbC+Qh0CFhwXpwC6YIPzBuyt1BX4iqFzFnQU3p1cP3MB94Zgvm5ucN+UiCDHHMyOVTLc2fJkT6kfQlCQIDIAIPqdLvdSM0ZATfNfHlOjMPW1lY4FzUClUpFL7/88rH0zXnWbTgcBjUn3nHvqcJn6vV6+CqVSgFJg2bqyRHWX+aM0x4xT1A9ijl1EnGBixLkuKG8RQKA94p6wlKpFNBnT9AwXiQPeH8IalF4ZB11SjTjzPygzsd7ZDm91ltCxK3T6YQmxYkl9rB2cHCgt95668gGtLMsCXQSO3NWqVS0vr4eGkuSJYorjHm2mZ8TCMS5xf55dybj/GA+49/Huc7SVI46jjh4hn3WJu50JI6LgR5BH5GmhcKc31EjMudxRMupfNCA/P4d0fCiXBR9OD73TgAXvx+/Nufwk+WHNoEalhehuiCE07a4JigPUCF4/jhTjAXHoE4DBMHRIsbHnxkZZgJkvidIAenhWXMe3+yZa9yXj2s+n4/0T/HaF0lhbjM3Hcnzf0E//BlC7wJldJGI8XgcaIycl0DH53UcIfO5H0c/Ce6cVkYtFqgOwQp/H0fFcHR9XvHc+OLZOkWPjLXPX89oMw/92p3mxzOiF0xiU2MuETAzthhj6DViBOrMOUcK4sZzia+ds1DnRzHmkaPMF9FA1kAqfT8DdcVYE9lf4vuai9rwXOPMiFnrhz9fkn3xhA3njz/rwWAQGr8mltjD2ng81u7ublCSfRi7mCtEYmfWUqnD/jlXr14NE5paGhZm6jvivGGcJWpcyCxlMpmQAWczYNHFgYY2Ft/w6QkBakE9DdfjziAbC4s/9QTuxEkKyBCBgm84qdQhd5rakcFgEOkWT+GvF6IPBoOgukN2j/EAySGAgX5Esz2cQvrT0A/IqXM4Nbu7u+p2u2q322EsXb0qTiVk00PFx1Ebl5fl2jz4qdVqKhQKIZgDmfKMczqdjtCXeLYcYzweRzq441CT7cThKhaLGo1GoR8IGU+CLK4DhIaxAUHk+ZINpUklvHSCo1lZ57j6V/wzjDGN4TyI4F/unaLkra2tkKUdj8chUHIqJJlcSSEQKJfLYd62Wi21Wq3wvCh2xry5p183ylooBDInaDxJvRrXtbq6GhkLUKfxeBx674CAuorWeDzWrVu3QrAUnz8obSE1/CjZv4ti3W5Xb7zxRqRGwmmCmK9xzLc4DcnN6W9k+1nbeCcR6JgVIB1lILiTySRIUp+XIIf3Ok4ru5/RaHd7e1uNRiOS8PLEBnsV7ztrIEInPE/WRAIcD4iYFxjrAokuWiN4nWs6fdh8uVarBZqj2+3bt/WZz3xGb7311lMZw8Quhu3v7+v1119/pCTJ+VglEjtXBg1oMpkErr8v4rPQE/4fzzrFF3S3WYiLq3HhRLkD78c8imPOtR6F6jiSAJrg2TQydGwu1C3EESi/FgIYaFaeTfcsXdxR5v9ct4+T13h4wzcKlV1pyYNDjs3YOQ2LAIgvHCaCMEc+COrIysfRKH/+ruDkP4/TL+LoB/fuylzxmhUoOn6/OHxQdwh0isWiSqVScNr9OmfZwyzWnOdhbH5+PqKc5YgKwW38HWH+eL8dnwt+3/zr0rUIVHgNhYsJ+Hz0WizGjGcQHydHPeP0N6fegSR5QMv98P/HQQ7Ou41Go9Cbi7Ug/uyYH8yRuNjJUcigI6U42rzDjiQ/KuWM5/goAdKzYJ64eJQxie9NjsjyL8+TfcTXTw9qvOYnvoYyL+Lrrl+HP1NHUZEej99Xt9vVrVu3EuraI5gzVaSp8iF79UWw8XiqVPuwlgQ6iZ05q1areu6559RoNNRutwOCw0Zbr9dDZtD/xSHG8XSaE9l/FgTPhjtlK77JE3TQCwVlKuhNOzs7AXXxRoQeFLlNJpPQE+fGjRvB0UilUqrX66GAXTrMjpOF4xzUL+BI4wx68zAQnbm5OS0vLyudTocah0ajEblvAolOpxO+JxiAokTGl+smCCEAZeyctkTdBqpLzvHGccJ5d0qa09/m5uYCugXCxWZNwzkU+diEnT4FIlYoFIJiHupn8/PzYUxAbmq1mnK5nJaXl4PD7NfmWVfqtPL5vKrVanAEUIs7CsE5TsOpAMHhvkAOr1+/HmhsyEFns1mtrq4GR6Tdbof6p/n5Q3ls5iV/M5lMdPfu3dBQlAy7O0WcF6VAgvrd3d3Q2Z1ghWOS/admiwAKRIBnvLa2FsZfigb83A8/J9hOAp17jbUTtG4WNVWSarWaVlZWtLy8rGq1qkqlEnFefZ5PJpPQePbWrVsRJJ3kATVtKLodZ/PWZ8GgEPo69jB1R9QZNptNtVqtkJAhsEehDnQ3Tm9zQQF/d2EsEKSCpFPHyfuZSqUiNGRp2srg8uXLKpfLev755yM1s36uu3fv6otf/GLSR+cRjOfJPl0qlXTp0iXt7Oyo0+mc9uWdmOETPqwlgU5iZ848k+81ICASzvv1BZTslGf2vX7EF3YPQuKc4njhJcf22gTPanqW3+sI4siDpIBSURBPsSZBBrQ0ai08MPNzeLYbBITzOgrl9wX1xOlO8QCFa/QsPH/j2XM+58fDCJQIEDiHj480Rc/8HuM1MFwDFETug0CH43rNCMb88MCQhngci/E+ODgIFCiCWyht/nyh+0HDQn0KytdpogecF4fH34WDgwOVy+V7mgY6LY7nAJ2Rgv9qtRoodDg+zWYzjLmjeo70eJaZ5+hZflePY27yzkOTgSbI8Xw++zzx+euUm8TuNZIAiDs4ouZrFc4t7w7viithxo+LIAgiFt5TKv7+JTY1Hztolw8KdEDZnVIW32vi6L1/jncufh2+t0jTpFOcxeDH97njySxnErixZvf7/UdyWC+6IaYjKbxLtVottCq4KPaocyYJdBI7c0b9Bo4TSlrUz3jG1yF4KVoI3+v1AneYQmqy+XGo3WtuUqlUQFWoNUBdjI2/XC6HDZxamVTqUGFraWlJ5XI5dON2A9HZ2dkJTUA5b7VaDU4FaAQOdCp12COGDSeOuuCQo6aGE4PqFVlCZI0ZP7juvjlJ0472njlZWlpSJpMJBaRssjiuXBdqWIxPvV4PfYIYc56rU8Xgiefz+ZCRBNErlUqhhoaAhoCH6/cCfZ4pFDtH+Wq1WhjLdDodnsPt27dD0EkvHOYbm/jBwUGQZeZ+JYV5cdIozixztMaD10qlEqSEvSiZ4KLX6+nSpUvBIfU6Kpyl7e1tdTodZTIZbW9vB9W8QqGgdDqtRqMRKW53QRHmarfb1cbGRkDLkGEnSGo2m9rb21OhUNDCwkKYY8h08947xQ7alaSA8JIpZo4ndmh7e3t6//331Wq1IvQ/6EY8q2q1qqWlpRDcxEUI4sZzf++999Tr9QIljrXtxRdfDAhOPFi96EZSYmNjQwsLC6E29KikyWRyqMa5u7sb6ih5N3h+vqbz7vFOshdQs8c1SFPFUdZBX9fYH0lG0Pw4l8uFvYO9GuVO1kpP1nmz0sQe3lqtlt566y0999xzKhaLunz5sr7ru75Lg8HgkfrKXDRLAp3Ezozl8/lQeOxZfoISsoMssrNQCRx6Ah+nHTnfX4r2RvENxR1oZDvjNR1kudhYOAY/w7GOG04FGVJHS8iWQ/dxFTY/p6NEznn3bDebndPUGA/GSZqiYBzLaXtOQ4tnz31s44pKOJ5eI+S1Ojxf/g4ndTgcRgr7HWFjIwWJwAnwJnQEKX4vBLc4/9K0SapnGwkSQWW8JshrCZwO5aiVz6ezYPE5zv+hA/JceU6ujIdaHOPmwToS2qurq+FZgQxJCu8D5qieU1ecBshclaJznN+T2PAgx+coTh3vkgtW4ACel6L1p2FQ1kCTvQ7QnxfBjSN2s2otGHeOxTwDtQGhIGA6b/U1x2GO4j/I/Lk4euoS0c6G4Pn5/iopMh+kqIw3FkeJMOaPFG0u6ms+59nb2wtNZBN7NOv1erp9+3ag7RYKBa2urt7TlDyxqCWrf2Jnxq5evaqXX35ZlUol8LxHo1FwrnZ3d7W3t6dOpxMWTXiq0rQ5Gk0Zvakiizt0GW9+GV/MoewQPOzt7anb7Qb6Bf1fUOSSDrPI0Jmq1arq9fo9ilrS4aZ06dKlUNfR7Xa1ubkZkB6Cn+FwGNAM+PAuVYyDh0PKhuJZcyhW0LFw0qXpxuTZPDJ/OIfSoeNJVr/b7YaM/Wg0CnK9XCd/48WlUMDc+SkWi4H2xHPp9/uhX8rCwoLW19dVLpcDsrK6uhooaPFM8KwgDCfYDeSG+ioMZx5Fp1nOHEag8Cwa87lWq93zuzitxf91o1aN/j+3b98O6A30zl6vF9T7CoVCCHC63W6YGwSTkkJWmdqrdDqtQqEQ6J1ra2sB5eRvOR/rAU4ZWWTqgEqlUuiPlNg0EdHpdNRqtQJiTV0WSQX6aRGgxqmDbtSCtNvtsF6XSqVwPOo6nPaa2NQ8GPBaUYL+o/6GQNSTQtJ0jyNR5tQzR21Y+zOZjPr9fujV5ckxT3SR+HM6OXsS6F2hUNDc3Fzow4X6pKPizWZTX/3qV/X+++8ntLVHtFu3bumzn/2sFhcX9dGPflSXL1/Wd37nd+pLX/rSaV/ambYk0EnszFixWNTy8nKgqzhagxPDpiBFFaCcWuTSqBgbAxt1POvknP54AWb8s75ZO5Lj2fCjNnTO7bLX1CNJ9/btiaNJnsn2a4jT93AGnSaCQVXiswRCTgeMUwF904zLZVNTRCbeOf+erSdo8UJbz+xLCsEiQU2hUAhNH12q+aixvZ8R5B3lPFyUgvVZ4/SwDihziXnjFJuDgwO12+3w7kAhdQSwWCwGhAUBAoJexAskBYTJ6S7MEd4Db9Tr2edCoRAEKLw266LbaDQK1MN2ux0RcWHNYu1AMcslh2etbV7j4WiQpIgE/MMqrMXRek/qSIo0OD5P5uuwr5v3a3BLfR0y8IyTS0E7WyGOsDui64FNvLbOm0bH6zY5B3/raoeO+PqzbzQa+trXvqb333//GEf0fNre3p52d3dDnU4+n9fy8vLMpGpiU0sCncTOjK2srOiVV15RPp8PzSZd2pa6GO+pM5lMAsIDv7zRaASHmQUafjkZprjBNSZoAllgQ/XGhjh1LpJAFpQ6gvtlqqDWUcTO/bAJcW/UFYGgQM1yJSmvs3CqVblcljQNVKjZkRT66HBf9Bthk+KcniHv9XqBckcdED2FKGb3mgkPSEG7qtVqCBih9pF9ZOxXV1dVr9dVqVSUz+dVq9Ui2cAnsTgNK7Ens1QqpaWlpfD9/v6+Wq2WxuOx8vm8hsOhms2mpGmvnitXroTPHxwcaHd3N8xt0KJ2u63JZBJqg7xRqRtIUrlcDtRPeOuo4RWLxfAuXHTr9/v60pe+pFarFaTGcXJBYKBwVqtVra2t6cUXXwx/7+uhNEWFWbeoCfOanniS5UEG0kQwzNqAbP7KykpY2z3h86wjRQSC5XI5IJyj0Sjc5yyrVqvK5/Pq9XpqNpuhZhKEjd417Cm7u7tKpVKqVCohmD04OFCn09HCwoIqlUpYw2EG8E6TOKBuMy68Q40eSYZyuaxisXhPUkyS3nnnHf3CL/yCtra2TmJoz5V1u129//77YV0lMVytVk/3ws64JYFOYmfGXDKWbG3cicexJ9vkKl7StL7G6yz4uaMhLNb8LN4A1CF8R4D8nJyLjQFH+n5FpNIUMcJ5w/mOB2De0wJxAupbvBDe0SivhWEccQbI1rIhucIOGyt/45Kf/hy4V1Ae/xzj7bQ3ro/n5cEcWXvPui8tLQXKg9MyEjub5s9mbm5O1WpV6XRad+/eDc/X0VhpqmBIgMM75yICCBXg7Mb7O0nTxofQfebn51UoFIJoxiwxkItok8kk0Aa73W5ovisprCFOn2Ls4rVNXtfnAUZcnIA15lEQUoQMdnd31Wq1goPs+4DXebmdl/XBaw5Zt73+M27sC9L0OTJmHM9rXKFAx9dVR9PZl3hv2fdcxMf34zjCW61WVSqVQqJKmtJinZJN8+nEHs0QHXDaIah6YkdbMjqJnRkjg9/v99Xr9SIND8kKQU/BWfKFFgoGNSG+AcYVxbrdrgaDQWSTTqVSAQXybCcBAjUs/ByaDOfO5XKBanWUMtFkcigv3e12Q3d5nIqlpSWNRiPt7OxIUpDi3dvbU7/fV6PRmKmGBQojKSAmKJehrJTP5yM8bepkms1mOCe9ZlC5wuI0EtAvkCanFqLm44EoAc5wOAyoET1T6vW66vW61tfXQ7b2YXpIJHb2bG5uTpcuXVKxWNR7770XaRRJTRuSw/SFajQaKhaLKhaLAUXEuYLqiNPH3EX1jmwmKG29XlexWNSlS5fuW2d10YxaKnqu0DOHd1VSEO1gDajVaioUCjMdKNZl6v9Ym59EMrrZbOru3bva2dlRq9UK6ycBGYhCXKnrPD1j9p+5ubnQO4peQ7PGdjgcqtVqBYVFalMlhUbbvH+oTaZSqSBd7WqFCEaAoLHfkbzz3mPSNKnlicRMJqPnn39etVpNly5d0vz8fFBMlQ733e3tbW1tbenu3bv3ILSJPdg6nY7ee+897e7uSlKE5p3Y0ZYEOomdGcOB9/4O8eyvozKusiZN6z2QtPRMkysLYU4xczTDCy0Jesg0U/hcqVQi1C5QDklhgziKR06DPhARggqO4SpqBCeZTCYUCHvHdzYgl+tk83InxBWvHNVirKA5xDcvaie8BwqZX0fAPNjkPN6EkMCHv8W5rdfrWllZCT1e7leDk9jZNzLGq6urarfb2tjYuOczTpHyANydLOauZ7mdCsV7732LHG1N5tDhO4iMPc1xoagSLOIIkxWmLo6ED0kd3l9H3CRF6kqexFhj6O+Dky4poMTQkl3evVwuh/XxPD1z9rt+vx9Bvb0dQJzWGVfPg+5HfSXrLzVQvCskEDiOCxhwLa7OyD7lTAj2YL8enhHPBWl79tDEHt0Ifo/qnZTYbEsCncTOjOHMdzqdSKDjTQ9dApWMIspOZI+8EHNWv5nxeKxSqaRsNhtQCX6OWhTIEXA/C3q329XBwYGWl5eVSh02UUTNi/MuLi4eqcwFItJqtUJ2m02EjDabjBdju0yuU0OgExCE+AZEFjydTkdEAaTpRjoYDAISFQ9auAauESSHOiQCJp6Hn380GoVaC+9bhHNaLBZVq9VCoJPY+TCc0qtXr6rZbKrRaESy8E6RQhGt1+uFOoN+vx8cbSg2CA44VY0AyJ1wl9NN7PAd9345k8lE9Xo9kpgZjUZaXFwMdRu5XC7USiEKA8LGmuEyxJ6MeRJj3R4Oh2F9BAFmriwuLurOnTshibKwsBBqOl12/Fk3UPBer6fNzc0Q6KAeyH0yXjRoJUBkjSXIIXlE8gtxCBIJUNagLzvVmWPws8FgEOpnU6lUUNqDXdDv95XL5UJCzAPQwWCg27dvB8ZCYo9uBLTOuEjswZYEOomduhUKhZBRRGbWe+HwUsf54jg6IAeO+OBQEQCRefSaHy+E97qWeIDk2S9kUtlsKOav1+taXl7WyspK4CbPMi8GRflImvKXpSntIJvNhntyaprXHeCwOALl2XAybYwl6jheX8M9cX1e38PYE9QxPu68umITSNFoNIrU7PB7xopAJ5H9PX9GMII0OggmjjKfwUEulUpBjQ3ao3/O0QNXTmR+ObKJOAk0rItqjHm73Va73Y5k9cfjcUCzHTWPZ/l9HZGm66JLTT9KHc4sg5bb6XTCHJGm9VcEr71eL0JfpMYS6ha0rPNgiMeQOGLthlHgMvt8nnHz+p44vYzPsR+yn7LX+hzhs55s9CQiSQg+VygUgkof777Xa0rS7u6ufu/3fk/Xrl1LEJ3ETtSSQCexU7dyuay1tTUtLCwEDnC88SCZRzY/FwJot9thcXaJZD67v7+vXC4XHG42A89OkilzugyBjgsh0OjTEQ/p0AlYW1vTpUuX7nuvqVQqFNzTt4ANnqCHQAe+dlxsABoHKA1OIdQTl/R0EQY2HjZSL/gmCPSaGxAfaG0Ud/O3XA9jDmWOLJ/XWrDZIiFOHUBi58+oNaA2DMqRv6Ne5Ey9Gep/FCrj6MUDdxf74H0gyJ9MJmo2m6Gm4yIa6CsKZihy4ahSrzGZTCICEC70wnh6conn4HLeT0oTHAwGoS4HxFiaJl86nU5Ah0GfnU6L+tR5MpJzPDNPPLXbbUmKJJ08eZdOp0N9D+s2QQqf6/f7kXepUChEUAIQed9joDkToLAPIhBRLBYjamscy8WCtra29Fu/9Vu6ffv2TFGJxBI7LksCncRO3Wq1ml555ZWwQOJIsxg7RQpnGqpXOp0OvR58oY5nsVyOmsAB5Aj0xrOTBDAuw8ymwuZTKBS0vLysfD6vcrkcmoc+yAiakGl2ZAU5z36/HzZxHA/61bTb7UgA49QwxqZer4fPO63PNyCODcLDz9nYQHFwLOBW87c4Plw3GyPPimJmispBc6rV6kOpYU0mE7377rtqt9sqFArKZDJaXV1NlLSeEWOOg7LgNDF3caRB97xxpRdL894xv+ipVK1Ww9wHESXIuqh9c0jcbG9vh7HMZDLqdrvhHaWmT1IILAaDQaiRRBDkKLTGZaYdAX4YY33hb7vdrra3t9VsNiOy4qxji4uLQfjAk0ygOwS5nux51o2GyS6rzXsgKQSm1FbSnBMqNM+OsWH/Y//0uhnoZqz3XpsDMs/fSwrvmyceJpOJ8vl8+AKl9wbXyFjfvHlTjUbjhEc0sYtuz/6qkNgzb9VqVVevXg2qLyyw3o9BUqRAFecdh4fgw2t6PKjxYAdnS4oGMPyNpHAsKBNsFF6MOx6PA1XtUakT0Lgo6ncoHynpdrsdHEE2DRTpUMMhaGID6vV6wTEolUqBmgdq5MiQCxM47Q8UzHstwOEm8+soEFliF5FAKhwKUTab1fLysiqVykP3NRmPx7p+/bpu376t1dXV0N8jCXSeDSMZMB6Pg/MKrQbHDbWnWq12D3ef4MYFNUBzETzAEYaqhWN/UQMdqGA7OzshIbK4uBiSF6wzBAaslby7FL3fL2B4krFl/ZQUFPR2d3fVbrfV6XSCgt7e3l7EaXcJetZur1sEeT8vgc7KykrYd1AJZV1mDJ06jaohgSxjRqDjCQPWfE+Ceb0VtVnQA71uJ04fJWgCkeU5sV9yruFwqE6nozt37mgwGJzyCCd20ezZXxUSe+atUqnohRdeCIGNBzTSNPiAegZSwYLpTQn5PAs0n5emm7kXWEJRY8Nn4Sa7KCmSXXaeMpvN4xTBplLT5nAu8+lUHagBrjQnKbLxEMhwH2TsVlZWVKvVQh1QXOnGlewYO0draCCYSqWCHDZOE+Pg1+X8f56B11mA5jxsTc4777yju3fv6hd/8Rd17dq1kGn+4Ac/qPX1df03/81/k1DfzrgRhONgESwT4EgK9FAoVCCszP3JZBIaD9IBnD45NKhEpt3ppU9aO/Is2ng81u3bt7W1taWtrS0Nh8NIdt3XLkkRVNmFQo5Si3waxhoyq5cSaIDXUuZyOU0mU/lkUDzqGR1FPm/G3kJCi8ADdERSRPGOdZ79yIMif/bQRl3yXVJE1p/zQm/zc9BA2hEgaNPeVJvraDab+qM/+iN94xvfSChriZ2KJYFOYqduhUJBKysrEYlkFlbUYnCOUI0BmTk4OFC32w2ZYWmK5JDdgrtPIMH/QYGcysYmj8ABizwbqkuyxns6PIqRBfNiT5dv9swZ5/R788ws9AbGZ35+XpVKRUtLS5HMOdfryBaZUEkB8aEonM9xbVCC2HD9OXmwg3mWNZ1OBynYB9lkMtHt27f1rW99S5/73Of05S9/WdLhBn316lW9+uqr+i/+i/8iCXSeAfNAB/lg5qnTZ3wOU/BOsIIKW71eDw1BmUfMfxcUuYhNZnn/tre3devWrYDskgBxmXyvxSEQ8vXjOINE1gPWX0fYqcsBgeJaJQWkACEXqJCOUp9HI3hnHKAmDofDSFAhKSD3ccoa6zXJKZ4xKCsU8Fn1WNTPec0rCBBqiHyBIDkLQ5J6vZ6+8Y1v6Pr16+cyIE3s7FsS6CR2Zmw4HIbMLjU3LOx8700vyULiwDvqIk07dMeRB2pzUqmUCoVCREkIQy3GYXpp6hDk83mNx2PduXNH2WxWrVYryDSz8D/IXELaAy1EAJrNZgjsOL/XxDgdjUxoqVQKDUg9OMTRQWEnlUoFOoEXGBPUeM2OpIjcLxsi2UIoeIgk4HSS4aNfCtdyP2s2m2q1Wvp//9//V7/zO7+j69evh9+NRiPdvXtX8/Pz+vrXv67hcKiXX375XNBVzquRXHABD+bleDwOje8WFxfV7XbVbrc1NzencrkcnKv19fVQ6BzvT+XoLJLxFynI6ff7GgwG2tzcVLPZVLPZDGtdfM2cn5/X0tJSUG50KqDX0J0E7Y+1BoqV1z5S80GRvKSABkOHrVQq4Tg45MeJRJ0FW1hYULlcvqfedDgchucO/ZukIXuMJ+ZIHu7s7IREIYGQixSArHW73bCHdrvdgOqMx2PVajVls1lVKhVVKpWZtMfNzU3963/9r3Xz5s2kSWhip2KJh5DYmTBQk+FwGNH992yvNJVX9iDHM8OuQBOnfblKkKurAf+7tGacg+zOA4HMaDRSq9UKGS4QC5d1vp955tSvzXvccJ2uPEVzRWhAfLHhFwqFSNbWESKumwJer5fwmqZ4QzIcTD+fy8H6ePNM6H0C7cyz+LNsMjlU0Nvc3NRXv/pV/f7v//7M3+/s7OjGjRsqFAp64YUXkkDnjBrObLwAmndrMjlUgPLaPJBD78heq9VULBaDMIYf35XECKwvihHIdLtdbWxsaHNzM4L4Mv6sYwSQvMej0Sg4sdAFXSHxuK+dhAsIjjRFiEmqIJ5SLBZDF3gSTReNnujIjqRIPSv1aYuLi6EZtdcyeaLO3z32GfZXR1sZ/8FgEN5ZgijocCTeQH5cEVFSkAD/8pe/fC4V8s6SOb09saglHkJip27ORYaT7eo8oCts3Cy8cTpXXDnGgw13svi9O/BxJwyninN2Op1IfZCrFvG919c8SNqWjQYEBKdjd3dXOzs76vV64R5AW7xJGLK8oDdOP9vf39fGxoZarVY4Phk9zsn1w4tfWVlRv9/X7u5uZLFkc2ODo1GfU+toIkfA6KhPJpPRc889p3w+f18HCiTnl37pl/T5z39e3/zmN4/8bKPR0M///M/rIx/5iD7ykY9cWBnhs27UEtAQVJr2+RgOh8FB4p2WpsqDw+FQly5dUrlcjiCUGOqDNEvkWBcl6G00Gmo0GkGt7O7du2o2myHTTlYeJJUaPdYrHF7GFpGQk0TDCLAKhUJQmgQR5zpIkLDWzXKmL6oxRvl8XtVqNUh0E5ywLzitk8DEKdO8e/F3hz0KlgU0uNFopEqlomw2qw9+8INaXl7W2tqaisViJBDr9Xq6du2aXn/99SeieSd2r7kkfDqdVrVa1eXLl7WzsxNqrhKb2sXYFRI7k8bi6z0x4G9jng12BMIpahjQuwczjvB4ZtMpbbOuyWVrPfvINUELgCeOc8ZGcD/jmsiMoeKGQ+gqONwHDoojP9ALaNDmmxcBDfVJBGD83Ol92WxWxWIxUBPigQ5ZYae64KRAteNYvqGC8tAM8n5GkHXt2jX9wR/8gba2to787N7enr761a8GBDCxs2eefCBIZs7gMDFfXPYcpJFaNTL7cbqaK62hznXc9SVnwViLer1e6D9DMDkcDjUcDiNKdV6z5Agbn6H25bT6DnltiK9x3Kc/13i9ykU3Zzlks9nQv8xpbb5+OzvA9914wjAuuIPgjddgZrNZlUolLS8va3l5WYVCISKMwN++9957Sd+cYzBPViLkUalU1Ol0kkBnhiWBTmKnZqurq7py5Yrq9XpQB8MhmkwmwYmhHwu0FBZwKGwEON5nZ3FxUZlMJtT9eI0LDtTc3FyQXHUK13g8DpxnHC0K+70PDRsAxy4UCg9UYIPzTNbNER2CJKfiSYo4Akj0ch4cGza3YrEoSSEAotYhn88HaWpob45oERTV6/VI0DYcDtXtdtXtdkNA5AgYBbA8AxrQQTsis/wg5+Sdd97RH/7hH+rrX/+6bt26FUGvEnu2jEaObLq8x55kcNnxvb09bW5uKpfLBal4lNja7XZEdGI0GmljYyP03Dnvxehx297e1s2bN7WxsaE7d+6EMS6VSqFGieTMaHTYqBXURDpMKDC2vM+ZTCYo2Z2UsQYzV0DjyVR3u10dHByoVCppcXFRN27c0MLCgl555RXl8/kLKx/utre3p3a7rXa7rVarFSS6oSizb0BN8z3Qaymr1eo9iUNECly5lP1VOty76/V6mD8kwfy5bG5u6u///b+vd999N0lIPWXb2dnRtWvXdPnyZa2tren555/Xxz72MfV6vYQiOMOSQCexUzMoU6ioOWIgRRXGvG5mVg0OTnYcwZEUWcA5TlxtLV6H41Kc9IXxfjf+Oc+OPagORVLIvBIgSFPFszhqE0edOK+kiGMQPw5jElfg8Sye3zMolIsV8HO+fMy5FldmI0vsmdd49vAoazQaeu+997S7u5tkpJ5hY870er0I5YV3zJUEoTj2+311u91QEM+c8V4e/P14PA7BE7V75x3FcaS22+1GKGsUhxeLxSC+QtLEFbegpeHgxovPqX05KSMxJU2l7n1dIaFD4ot1Lo5WXGQjqw8jwOvhfK/02lVJkb1PUgg4/XOOGEjT+iD2jEKhEOrmZqkcQlt94403dOvWrQuVjDgJGwwG2t3dVb1el6SgRnmSyYpnyZJAJ7FTs2q1qg984ANaW1uL1OZgpVIpbHpknxzJYUGmKJ9CWu/NweedfpZKHTaqG4/HkeJLp7PxMxChnZ2dCFUgn8+HY2YyGeXz+dDQ8igKCJnqjY2N4KRI0vLycuBSQ9dIp9MqFovBsXO1OD++Ny+VpE6no4ODA1Wr1dBgzwMd5GRBk6hzgP5DMSud1A8ODrSwsBCobXt7e+p0OhoOh0EBySmBvsm6AMKDEJ233npLv/Vbv6X333//8SdUYqdqFB73ej298847AVll3hSLRV25ciWgjY1GI6iF7ezsBJRzYWFB1WpV6XRaw+FQvV4vOMZQ1vb29oJal4uVnDfb29tTo9HQ7u6ubty4EUkooJBG08hsNhve2XhXe7Lt1DKVSiUVCgU999xzQfzhJAPGeJ8WarD4maSghgmq7yjxRQ9ypOl+Ih2OJ4gecusk4ZCHRr67WCyGAFKa1kp5YpD6HGmqbHhwcBDqpCqViur1eqiho6aVd/2tt97SG2+8oUajEZpuJ/b0rNPp6ObNm1pdXZUklctlra2tJfWqR1gS6CR2akYR7FGSsDjHnu1zcxREmtboSFO0wftySFN1Mz7v5n/Poh1XfvMspAdmLuF6lMOAw0HwRKBTrVaDE8c1eb0SsL+rqGG+WREUUhPhSkazkK74NXH/8esg6OOYZFYJZjgHAVocmbofnY/n1G63tbGxkaA5Z9g8i+7vHd9TZwbKMJlMwsaLk43Dms1mQ7d3/zo4OIjUxzHvqNlx592RnvNiccU0kg7NZlNbW1shKJCma46vk/6OkwzCvE4Dlax8Pn/iQY6b12nGa0bS6XRYt7nvh0HML4I5yufrtSPofIZxi9c3HcUciKuV+hziOUA79Ro6/n5vb0/vvfeebty4Eem5ltjTM+idvB8kOy6KGMujWjIqiZ2alUolPf/886GgnZoQR1XIRBF0uHKQ84d9oySTRW3PwsJCoNLgkMc53k7/QpkGx4pFRZrSxVxFCtnTeAO3uFE8jZJNo9GQpHCd1M54Tx+CDzYtbwpHhm5hYSGMDcdvNBpKpVK6dOlSCKL29/eD2AHHLZVKwaGCvjaZTAIaRH8T6iU6nU7Y6NbX15XP5yPN/aDX7O/va3l5+YFzAEduc3NTGxsbiTrPGTWCXLj6TjGcTCba3d0NHdMJtjOZTEAlU6mU6vW61tbWQkDkx6xWqyqXyyoUCqGWDPSx0WiEmg1q41AWTKfTWllZORdZfhIIrBOM8c7OTgh0yuVyqGsCvU2n09rZ2dHW1paKxWJYh1gbvPcYmXfWOyhvJ22sY6PRKMhLg8Lv7+8HB5rk0qVLl1Sr1RJqjhT2Ceqz2u22Go1GCHRd/n8ymdxDaWo0GhqPx6GeC/QvLgACOgTLYGFhQUtLS6rX61peXla9Xg/1Uv7u7ezs6G/9rb+lN998U+12+zSG6Nxbr9fT5uZmJFkKOpvYvZYEOomdmqEW48X0jgDEldF8MfUslGew4r93bjfOuHPB+dczxJIiTr+ksOl6Rtuvz7OScYfLM7RwqWehRwRvfDbOsfbjcY/Ou+b+XDmHzQvnyRuw+thSG+VIjFPPcEj53rOEXJMHKUcFlHHz+ivGOAl2zrZBZUEdkKaD/AzHCIqaNFUGQiqdn1ET5mipq4NJiihAOY0G+g2Bz7NIjyE54Y4/72mj0QiOLAgZawiULkkRVUY3X5N4r+OKk6eFkPi6HP95vCaQZJfL6F9k89pI3j8Xb3EqNu+VN3n2xJTvFTyLOErkCBs0bWo/fb9jLrdaLb3//vu6e/duspYfk+3t7anVaoXnDlqeiHTMtiTQSezUDDUg6kRSqVTY0KgrcbSBBZwNz0UJXBHN63cGg4Ha7XZwzsk+wWP2wMZlbgkQoKNVKpVwDoIiHDl6xPiG4ubZSjKW4/FhV+nFxUWtr69rPB4HDj7XTpd4KGGe4WSTIXDBIeCYmUxGk8lEm5ubSqVSunPnjvr9vra3t4Mj6spxOE0EME4jIYvv6nPSoePZarVCtrDVagXlt1KppLW1NZVKpfvSYpgDly5d0quvvqrbt29re3v7Kc2wxJ6WUUuBU9TtdtVqtbS5uRmafFLsTv0INTnevBYHbHFxUeVyWZPJJMiqQ9PECYvTUHHsUFtjDbhx44Z6vZ5eeeWVZ056eH9/X9/85jfVarVCp3pJgU5KADQ/P69isajRaKQ7d+6EYI9mvLyDrJ1xBct+v6+5uTmVSqWQYDpNPv9gMND29rY6nU5YU0mc+NpPDQjo1HkXnngYY69B9ANpcZ69r+Gg8yiWEvAyvwhOnDqKWA6sBf5uMpkESWmvycH29/f19a9/XW+88UZAbBM7Htve3ta1a9f0vd/7vZKkWq2mK1euKJ/Pn/KVnU1LAp3ETtXiHPI4khNXNHO1Hs9A+bE8KPJCeTZ9V+5xVIdzSNMsFsfGkfCABjqIK9LMMpw0gpZMJhOCLLLaBDB8hvoiv182G45HwISTwH3gjCK6AFWFxqFxQ1zBG306wuUIEs6ujw3olNcExZ/dUcb91et1vfjii2q320mgcx87zZoUR1FBbJhvrt61sLCgUqkUEJz4HGAekYX0Hk/QbTimz11pilI4ehF/l8+qcV8uCT8YDNRoNEIvHKeskoCgRg/xBd7teG0E5/CMPMdzmXfWmFlqWSdlTlOL7wE+X2bdz0VHdQh+Qf4IXuLm7ANf+72+x9f4+P8dEURsh8BamtaEYXt7e3r99dd17dq1RE76mG0wGESag2YyGZVKpaRG5whLRiWxUzM2es/gxqlhLLKSIkXKrkbGojpLNjOTyahWq4UaIAIEHOx44Xw+nw9O+3g81mAwCJ/l3/n5+aASxwaCg3fUfY5Gh92kqftpt9u6c+dO4KhTU+RUHDJuXAd8Z7Jw8aCQDujwr6Gc0S9Ikur1ukajkVqtVhizfD6vlZWVSH2AiwKwIUI1wblyR8ppdnzhoN7POcHx+vZv//ZQ33P9+vUnmlfn1TxgPq3MNu/k8vKyisWiWq2WUqmUKpWKCoWCrly5Eurb4k4rRkBN3xxoWFtbW9rZ2Qnfg77u7u5qf39fa2trAanc398P/SKokfPxOYtG89R333039D8ZDofa3NzUaDRSsViMoNHUJVJb6BQiEBmXFQah9TWQ7P7y8nJExACn9aSNe+v1etre3o4kmpzCCk12MBio0+mo0+locXExceY0Vdza3t4OKKAjNk7V5vPz8/NhH4UqGe8D56gpc4R5dvny5dA7p1AohGfosuSdTkc/+7M/q6997WsRNCixp29bW1vq9XqhsXatVlOxWEwQnSPsYq8YiZ2KLSwsBEnTuOKSZ2rJBuM0OUfdudw4Be70H1XPQ3DiyAVoCg4E4ghxFSN35uPc4/tlRwmonDvt90RWkyJs51BLU+dgYWEhcr8EN5zbM3L8nmCDoMk/57VPHIesfC6Xu0ee1muNfGxd/tuRtLgy1/2sVqvppZde0srKiorFYtiML7p5EB6f1ydpOOm8k7wf5XI5qCdCoXpYnrijQyQZvI6NuQtlk8w0X8xjENButxsEEE7TCNRwJHnXaRTcbDYD9Y/GvKxLcfWxVCoVkVWete6R0PAkAw4oz4t1B+GTXC4XSYScZHDoa7UjCNwv1zyZTMLatr+/r36/H6SSLzKFzddjaSqe42u115CyXjstMv5FkOkMBQQJQHOgEZJs4DmOx2Ntbm7q/fffD8IhiR2vkZR0FsVZTfCcBUsCncRO3Mrlsq5cuaJarRaU1lARAuGBD0zfBySi4RbHZWbJBLJRxgstpcPACf6xZ369NqhWq2lhYSGgP6BG0r3ZMof27+fgOYfenRHQHTLWN2/eDDU38/PzAeGRphQvb65Ig8VsNhvGjnEgk8v1E0ARkMDndtpPsVhUvV4P97S9va3NzU01Go2IYwldguaO1EzkcrlI4OUF0w+yl156SS+++KI+//nP65vf/KZu3Lhx4Ts8UzMG1YiA9DSMXi68k+12WwcHB7p69Wqoz3ncQIyanZWVFS0vL4f39s6dO2o2myqXy4HGRTd4EE/e9729Pd28eTMot50mvandbmt3d1cbGxtqNBrhXWu1WqFn1cHBgZrNZiSZ4pl3rxcsl8uh942j4AcHB4EiyLqB83PlyhWVSqVQN4ezC+JGgqLb7Yb6n5MwHDISKjRO9j4t2Ww23EexWFQmkwlBIvS9o1oSXBSLB4XZbFb9fj/MA9Znr3eSdE/yi39ZW5B+Z2ypt1xdXdWlS5eCuqi/78PhUL/3e7+nb33rW4nK2gmZJ3wSe7AlgU5iJ26ZTEZLS0sqFAqRYIFaFTZ+R0mQvyTT60IE3juAzKirOHnmcG5uLhTXewaEv+XYcWU0R0ugiFCn4OIA9zMKb1utVughwrk5P99LCplez9SR+Xan0utpnLJAsMf4kf2Wpt3HyRKTfZYUNrxisRhBapCmJkDyDKBfB/YomWLG4NVXX9X3f//367d/+7ePDHTm5ua0vr6uy5cvn2uVGc98n7YtLi6qUChIOkQsXN78aWUSfU5DqcSZ29/fV6PRCGtAPKOMZPVJyg9DK+P8OO3Q0shuk6ih0SlOKoqTmPer8rXAKYCgMZIi7zrrEkkePs8YVqvVgOb4OoVT7BRdfw7HNfdYq/b29iLS/QR3cRQC+l4ul1OhUAiS4hfJSLRBLWY+ed80pxJjJEn4maNozNt4HSwGQsocdlYC1zQcDvWNb3xDX//615M+aImdSUsCncRO3IrFol544QUtLS0FR3s8HqtUKkUcGHfwDw4O1Ol0IgFILpeL9IjAScDh8YVZOlzg6S8xK/sM/cUpWCzocWrIcDgMGwBZrvs5fMD7jUZDd+7c0XA4DMgLNQ3xZm9kMcn0UrxdKpVCLQ0IFw6PO0kEGn7tKF21Wq1IYAgHngAGamG9Xlej0VCr1dKNGzci3e4xHDaQMjL82Wz2kSVh/+Sf/JP6ru/6Lt2+fVtvvvnmzM9ks1n9R//Rf6QPfehDgZt+Hg3a0lmwcrmsYrGo7e1t9Xq9QDE8LkulUkHhD7SS4lsP2kli7O3taXd39x4Z+OO0fr+vW7duRXpB3bp1K/yetQoUjPeQtYLMuTueKBl6th4RgfH4sPljvV5Xt9sN66Ej26yJnL9WqymXy+mll166510kwJEUaqJcQp7ncByUSZCpVqul7e3toF7Z6/XU6XSC0hprXLPZDMh0qVQK6nEXicLGXtBsNrWzsxP6m3U6HXW73SAvHA9Wi8Wi5ubmwvocD3JarVYIoOOJlUKhoPX1dVUqlbCux6+p1WrpV37lV/QHf/AHD01VTiyxk7Qk0EnsxG1hYUGVSiXQENjMnUPsVClJ4XOSwobtAgHQIXAiXGyAbNcsPjqbgjcjdQ64K5tB+YI7Dscditb9egaw8fj1spk7355zgly54hRjRDDj33tdD7zsvb29QHUioJQUuPpQhnK5nCqVSkQmmw0T5woKIbQanpPXN3FuagoepV4Dy+VyqtVq+uhHP6rt7W1985vf1O7ubuQz2WxWH//4x/XhD384aSB4QkbCgSD3pLLpHuxVq9XwzkG78jnN727duhXmNRSpJzHEP0Bw+JemfQQavG/ekyReI0MCx993DCW0UqkUHFFH9SggZ13gM/wftDebzQbKV7VaDRSm+Pnm5uZUKBTCOuu9ifyaHbl7WgEPgZ7XHyGZ7HuCI/Oe7Lp7964KhYIqlUpAoM+7Ecy32+2A9jOnnNLqKqQI0czNzUVo4bzDk8lE5XI5fE/ygDlUr9dDk9p4UDkajfTFL35R77zzjnZ2dpIgJ7Eza0mgk9iJWzab1dLSUnCafdGFe08fGzKe0rQJJZK0bMos0KVSSZJCkNDv9yOw/v7+fiTjyUYAjxkHxTcO7+EzNzcXyXpRrwKV4EH1KARHcceNYIzjck5qEqBzMDZcMzVHNBOUFOpkoKKhIocqlXToFNbr9RB8lUolXbp0KThaXCO/ZwyhskEfIkPI5ug0PoKiR+XSF4tFFQoFffKTn9SVK1f09/7e37sn0CkUCvov/8v/Uh/84AcvhINzFozAGaf5JDPpONqXLl0Kc244HOrdd98N7waOM31pisWiVldXg5P2JAai0O/3defOHXW7XW1sbGg4HKrb7WpxcTGoHeVyubDuoHzoxd2SQn2TNG2+Sq+uxcVFVatV7e3taXt7OyQQQK04JwGB1yql02lVq9UgDMGYHaWuNj8/r3q9rv39fbXbbfV6vVBvx5rHdZVKpYjC1pMadUHD4TCgyQSqrMsuSsBzwNFPp9MqlUp65ZVXQkB7Fiiex2nMv0ajoV6vF0lKsW+5TDT7I4ggiQHWdgJNEo5ObS6Xy3rxxRe1vLyslZWVmUHuwcGBfumXfklf+MIXdOfOndMYksQSeyhLAp3ETtyo0QEx8NoXaaoCxv/5ilNSvI/NZHLYeJAsJxloMpIs9nEnzdXEXFa62+2GzzkfGVlnfv4o9QlkHguFQuA+IwlNjQGUNg+mQF5KpVIIRqQpnU6aZo7JLPNzgo+4zHO73Q5jNz8/r+FwGM4BYha/T1Aadzy8GJLxxuHc398PdLhHcUJSqZSuXLmi+fl5fehDH1Kv1wtCDdVqVSsrK+GZnnfn5qzZcVGZHsbIKrdaLY1Go4iYhqRQXI3wBoXS4/E4IDAEHQQh/C2UOGlav4bj50EGDXdxrn1tcJUz3lPWnXQ6HZIP/Mv9cAyK8nFSoZV5coM6FQIbJPR5R0ulUkgW8I48yHwdmFWnQ+KD63Qk93HnwcLCQlBXJIDydYZn6deApdPp0FIAZIfxPoqWfB7Mgxb2KsaIhFK8HtX3KFcYBaX098f3QZ71UXWCGxsb2t7e1nvvvRfW5sQSO6uWBDqJnbjlcjmtra2FHgkswL4QO5Lj8sleJOuBDr1p4B0jOkDGyp1yPzY/98Jmp5aAAHF+mvpR70OB9oOM66Wx12QyUbValaSgIkXAg2qU1xvAzXflMy9sxhnp9Xra29sLThJOgGdKqWPyOoB+v69SqaRarTbz2nGqHBkCieL7fr8fNsjFxUXt7e09NuLyyiuv6IUXXtDnP//50DNlf39fq6urunz5cgSZS+xkzOlXp3FupN/ptUMWH+oYSApiHwgD0G/izp07Aemo1+uBIjaZTLS9vR0K3ieTibrdriaTSWh6WiwWtb+/r42NDS0uLmp9fT3QOh2VoO+IXzfUV95LHEwSHRTmdzodSVPqKkIHnIfPU/vmCRy+KpWKyuWy6vX6Q797Hug4BZiAhnUUA/lG2fFxbHFxUZVKRcPhMLKGEpiBkOO806iSNbrT6YTxKRaLoeeLU4PPm7EG85yGw6F6vV6QfHaqN+8LAZErr9H0mZYD8bHyANbrVLHJZKIbN27o3Xff1be+9S29++67x3/ziSX2BJYEOomduHmtistfehZpMpkEPrwX6WNkUBEgcKUx3/DYGHHqoWHFaXFxZIdNlXNwfdAEOKdvLEdlEV1QwbOUBCoEJ/l8PoK84Mjg1MC3JiDywA1HynuP4LQ4/x2qDzU+yNPe7/rJFqK61m63Qw8QHI25ubkQtELnGwwGISP+OBnWubk5/cf/8X+sF198Ua+++qo6nY7+2B/7Y1pbW1OlUnnk4yU2RSscNX0Y4z04zeJvR4Chr0FRRaVNUqRGbDweq9lshvoZnDgcZWxnZyfMV3/veZ+okZlVzM17EE+e8P52u92Q8fZeONyHq6dJ08y7oxjeowjp+WKxGAIhECoQnUd19Fn7oKqCLLvwiAeU0HS5nkd9v5HRRlyFxM3BwUGg7vp1xb/nvqHuIeONgpjTk0Ha+AJtfppUvOM05lq329XW1pZarVaQkgYhlKJ9qVxd1JF/0EIEfdjr2CuQki6VSiqXy2G82EtZPz772c/q85//fEJZS+yZsLP/lid2Ls2zkL7Jspiyqbrjz2bqtSbQSvy4ZB8dyieo8saHOE6SItKcFOvze5dNxlHyDuQPcuQ9yMFpcieKTatQKGh/fz/IrdJYFYeHYKbRaIRzw7P2jZ9aJDZ2KDTSVLKa+yQTer/rx8lqNBqamzvsm7O5uRmOT6YZuVOcpH6//0RiAel0Wt///d+v0Wikb/u2b9NgMNCHPvShU28I+SxbnLv/sHYWsuRer8K7MBwO1W63Q20LtWKgEqiYUUtGgoOCbu5pd3dXw+EwvDe1Wk3pdFqdTiesR+5ge7DlVFlJwfkkMAFB5T10qi5rg4utgCYTZOCMuspkoVBQtVoNawfqj6Abj2Jx6jDXWygUIkgO10oSg/nzOBQ26h0J2ECpPXD1RJSj+ewLXOtwOFSz2dTCwkL4W8ZyMpkok8kon8+H+koSMb4unmVjrrfb7aB62Ov1wlpPosvrwQj0GUMPpL2uDaox7IR6va4XX3wxUCJBjkj8MWc/97nP6Rd/8RdPa0gSS+yR7Oy/5YmdO4OK1O/31Wq1gvNAcON9bsjKxmWTx+NxqDNxmN2DEjc2Trj9jiqRBZOmG+os5waHI55tfRClh3PwtygHscl6UIdymnRI3yBwcwfDlZA4N3/rstrpdFqDwSBSo0PWm/NTCO0qdw9yWnAocTZAkuK1Vdvb25pMJnr++ecfbmIcYel0WpcvXw51DadhIG88j1m89WfFUBac9aw9Y+7IJc7QaVs6ndbq6qoKhUKQgo8jhz7/9/b2tLOzE6H7OKICbYtnCj2OAn/eRZBh6HLUA1KDBjqLOAjvnVNucaxJeECDxSH1po4eCO3t7YW6G2mqAre7u6t8Pq9qtRr65DzO++F1GJ7YYQyQ9mZ9gKLHfT6J6lmhUNClS5cCsg8q7Ap1kkJShTXW65UQt+EzfJ6x8nEnoB0Oh2GenPVav729PW1ubqrVaoUgBWqhpID6u8Ioc9Tndz6fD++0NBX3gU595coVlctl1Wq1sP/G97bPfvaz+vrXv6533nnndAYjscQew5JAJ7ETN/jn9ARgU8fZJpMIGuFZT2naDA3nGqrarOyfoy5ser6IOy0OZ8OdPXc6cFYINJw2cD8lKqcITCYTNZvNe+qFPNCDZkatEdlOsssETXRK9z4a3ltEOtwknXoXd6qg4xwVIMYtlUqpWCwqnU6Hou84osbYk2X14O1xLJVKqV6vP/bfPw1DiMJRSO7pLDtJcYtnd2eZyxaDmlAfcNqWTqdDb5jbt28HpMbnlzuv1OygAoiTG5fldRGBdDodnEGSLrxPONAkHLz+jaCEYAfqGo67C4lAASNY4meSIhQ476PlBvoB9atarT4R0hkPdJzi5L3LUqmUVldXtbi4qFarpYODg1Br+DhGTRPrmwfgBJdOP+O5gkKD1iAz7et5nOrMWILql0qlEPSfNlp5P9vb21Oz2VS3241QBgnamcfeYJo1HmSQOcx67WIFKOCh0Id6adzG47G+8pWv6Nd//dcj/aISS+ysWxLoJHZiRiGtUzDY5CeTSchGen2JU9ucsiYpbE6uLsNijrFRukNPdo+NXVLI5Dqq4bz/XC4XimA5J/VAD+sAeod0+nLAS/cePARUvvnCacfm5uaCo0RhMLU20lTBqdlsRuSx3YEg00mtzoMobPTboaaI8SLYIqjiObbbbWUymVATdZadifsZgfn7778f0DwyoThUpVLpmagbYk7fryaLLH6z2TyzyNXc3JxqtVoQvfDGuZ4YGA6HyufzmkwOVRmlqYqYU0MbjUb423jQ73Pae+bQKJR3yAu8OUcmkwnvC0kI1j4CGqifZOybzWZwUL0uKJVKqVKpaHl5OTj+pVLpsZEcjCCWzD90sMFgoF6vFwIMBAFAk0GznkYAXK1WlclktLq6GgLRVCoVagERaGCdpL4GCXtk9QmEHOWXpsgOwe7e3l5IqJFIc8ToLNnBwUEYB64XJL5UKml/f187OzuRIM+VO+MMBZcbpwk1ycKj3vXr16/r5s2beu211/TVr35VzWbzpG4/scSe2JJAJ7ETM3jZ0ETc+eb3vtHgEGBxB8Q/iwPhwZAjG57p8sWcDRQn3AMOR3+gwaBmlk6nw+b6sJsjDgN9C0BEvJCZe/H7QZDBZZ+9SelkMlGhUAgbGNlY6n/29/dDdpn+RH4+kJ4HNYHEccNh8yywK9V5sEXfkydFdU7LPFO/u7sbMsALCwuhpwxKSM9CoIMD/aDPHBwcqN1uB+f2rFkqlQoBTCaTCVl65jVzH8QEpMV7RXmBv68XXq/CuXg3UC30uolsNqtyuRxJsrB2eX8wp8D6u8A18K6CFnniRZoGqcvLyyHQIOh40rEkSMhkMsGRbrVaod4vm82qVCpFnOSnifDl83nl83ktLS1Ffr69va1utxvEIhB1QHyBpsv+XI8y9hLmA8ktF3A5a8accmEeR7lY00EQWZvjdDwPdBxBpKGui07Mss3NzaCwdvPmzWO/78QSe5p29t7sxM6t5XI5XblyRUtLS2FTcoU06dBBAI0gCPCaiFQqFRqf8Tc4FxyHRZsghHPFgwqORxbMM61k/ba2toKjg3NDMIVzNT8/r16vp3K5/MAxwEHzWhauTYpSi5w6wu+4Tugx7qARAI3Hh30+yFYzhk7N45i9Xi/0vXkQpYlx63a7QcmKWqnRaKRCoRAKuKGVoFIHxehZM+4P6fJerxdEGfL5vFZXV/WBD3zgoWXGnwVzRz+Xy6ler5+5Z0ddzGAwCBQqnsn6+noQ9SBb32g0IllvaZo4cfoYhtPH2sA8pnktP6OnTb/fjxTW8942m001m83Q5DKbzQbnvFAo6ODgQJ1OJyR88vm8Ll++HHnvS6WSlpeXdfnyZS0vLwehhONCSUF4KpWK8vl8hOJ00kZw5dQsR7xJMnifnweZ9+yRpip9/D31oKfdhHQ4HGpzc1Obm5va2dmJiNl4UmthYUHVajXsbV5H5vsojaUJEiuViiqViq5evRookOwhPHP6T/3Gb/yGfvEXf1HXr18/tfFILLHHtSTQSezEjM0Th5jAIi4f7eo6cQqCNEVhvBAYgzIiTVEdD5LiymdxmoNT5ZyORbEnX64G56jFw5gXzPqmgjl9ZlbtDNQzl96OF5FD+yMIIrOMdKvfo4su3M+8lgq6EAWwoGIgX2zG8az0s2aTySQoWzHm7XY70v+EXivnxbyuDQTyLBrXyVzEGa9UKmF9AJ2gT5UnNziG/63X+UnRZAhoCwGJ/w1opq8TJEL4gm7kCApoD9cL4kayhYCK/jhPSlN7GOOeQbxO00CuaD4cV3hz1P1hLV6/Ew+Qzsp6BarWarWC+IWkiFocCA0BCogzf++0P9AwD1yz2awqlUpgWrCeM+fpQfXGG2/otddeO52BSCyxJ7Qk0EnsxCyfz+uVV17R0tJSqFFx6hN8dGhaZOkoisaBICPnMpnStIi60WiEbCuOgqSwuMcLl734l02CgMC5yxzTxQnS6UOJ5mq1+tAOCEGICxLwcwqocYA4rzc8HY1Goblnp9NRr9cL1AZ3yHGqJpOJ2u22JIXsqNMFvTP5/Yz7LhaLIbtNZ3OcP0lBSQ+OP3VNTnt5Voz7gB9fKBS0srKier2uD33oQyoUCsHpPQ82HA7VarUiqmFn0ebn57W+vq5CoaCNjY0gbe4Z65WVFUmHz7BSqUSUwnCg4+9PqVQKYiBeo0dD0e3t7bD2sE4RTA2HQ+3s7IRrdBESSQHJgbKKKuHa2lqEfuUoN6hhsVgMSPhxGcHZWZRdLhQKYc0dj8ehoav09MVA2C9Oa+6j8Li7u6ubN29qZ2dHOzs791AMuUa+UFZz9TWYD0iSIzywuLioWq2mcrl8T9Dn6Oav/Mqv6B/8g3+gt99++8THIbGjzesME3uwna3VLLFzbSA6OAeePfXFmY2eL68BkaZIDQo0rn7lAQGOQ7yu56g6H1dn45wucuD0DTZdzhNXfrqfcf1xJId7wDyY4vz8nACFDLOrqOGwkJEnOywpUG0416MWm3NMpxPyf84VL4Alm+1j9qwYiKHLZxNkrq+vz3yGz7I5Wvko9WcnbSCGCHE4+oqQiauQ9ft9VSqV8P4TDLXb7Ui9G88YNNkpq9BZSb5AEWU98aw5f+s1UTgmNBD1/jle00PiYXFxUeVyWcvLy0E17jhtlhDDWbF4rWa8/vJp2mnPeZ9vBOH0JnM0P85WYA0mSeeJQepjEbAgaTNLhIC9dTgc6q233tLnP//5SO1qYqdvR+3d8drkxA4tCXQSOzGDS0w9A8GEFwCTrZMUGpax8UJdYNNzBMULgb3uRlKk/oV/nZ7jTp07eQQwvnAQXHnggxTvozgI4/FYjUZD7XY7ZJm9h4TfL0ICSIqy+bVarTBOOEssfN6jxxc9Ao5+vx+cMZTgXK1ulnEO6lR4BlARcRapVaJOiBqEZ9H29/e1sbERsvClUknf+Z3fGeqsnrXA7UGWyWRUr9fDe3HWM4Zzc3OhgzvBzSwqE/1a4j2QSLBIh+8M0tW8iwRTCIiQKOD4oJdQz5CQx8kcjUYBTcKpJKFDrQ3qX8ViUblcTs8//3xAnMjSn8T7Q/JEmqLbZ3F+Q6tDjQyE47QDlKdl9PthboEKEpwQ6LgQBfsRSS32lPF4HAKaarWqXC6nS5cuKZvNqlaraWFhQYVC4Z7n/KUvfUm/+qu/qs9+9rMP3BcSO3kjoRl/NqwXBLuJHVoS6CR2YsZG6s3cvPDZAwcyUl5ojwPjCI7Xmri0Jr/3ol4/pwc58Z9J0YJVR0z41+lyftxHMUepuFavDeB+yAqXSqVQYO1CCKAKcTTIr8vRIM8Icm8PU2PkyBu0Qc8kQi8kAABR43lCSzyLztMs8xoQxotg/bQLlY/LoHrG69fOqvFuMC8JJFx5irnPu+LrhjRdd3jf4/U6kiLrAmtRvIeVI9S+plA0H5eXBgXO5/NB5peicKgpoFMnZel0OlK3d9beV0fbnar7rJuv1/v7++r1ehFnlfnjVGsPuH2ee20nz9BbOxBQQ2XzIJq95fbt2/ra176mu3fvnuQwJPaQ5muMG+sGdYuJHVoS6CR2YgYvmM3U6WHpdDps8HGnG5QFhEeaZmLj8quZTCbUkHhDNcwLNt2Zc8cknU6rWq2GTWU8Hkc2VPjOKD4NBoNHDnK8pqVarWpxcTFQFHq9XqAaIKBQqVT0bd/2bWo0Gtrc3NSNGzfU7XZDwAHqgyRsqVSKUMlKpVKE8he/Z9CY+1mr1dLm5qaazWboui0d0oLc4JE7qra/v69Go6FsNvtEjQ1PyiaTSajfgNe+vr6uYrEYmYfnzdx5ehbMER3oNteuXQvrQD6fV6lUUqvVUqPRCMXdiJJkMplA6aH2hp430mHT2+FwqI2NjXsQHZekd2EQfsb893o/3utcLhfQIzLs9NPxQFM6uVoREkaDwUD9fj84TSibnbZRazgcDnX79u1AKS0UClpfX39mER1QGFQdW62W3n777dBvLZ1Oa3V1NchIs6bzvGgtwBrusubS4b6bzWa1srKiYrEYVBRnKdVtbm7qG9/4hn7nd35Hv/mbvxnaKSR29iwe6KRSKRUKBdXr9SBCkdihnf7qldiFMS/MdSQD83oU3+T9hY5nS/mez81CWOLH8PN6HRBBjnPmvTeH34df61HZlQcZx3BExh0cPyY0IrJ6s+on4hlors1RMa6fzLQXQT/IUSAw5brjam0+dl5bBYJEgfuzYGRW9/f3w5jjOD+rDtV5NkdhqJNxBKXf7wc6D4ikKx7y5bU6koKz6UpUsxS7HMF0FMcz5vx9oVDQ3NxcoBKVy+VASzoqqImvL45Ug9g6ivU4huPMsbnus8b35zr9OfF8nub6clxiB/c7H3LO/X4/ZOV5ri6WM+u6EMVgPWb+Q5v0lgPx9Z4x3dnZ0euvv673338/aQp6xm3WvCRx86zssydlSaCT2InZ4uKi6vV6yBhCyyDIwBGRpt3ICY4mk4larVYkQPJAg83ZMxnucOC0xmkpOCOgRgQGULromE4W37ngcMXn5uYeCyqmtgfHC5lqlN3gWMPZvnv3rtrtduBuQz1wB61arYZr90Cw1Wppbm4u/M3a2lrgZxeLRS0vL6tUKj3w+eXzedXrdWUyGd29ezfU+vB7VOikw347BAqoVElSrVZ7pHE6DaOGajgcamlpSdlsVuvr6+eyLudZttFoFOppKpVKECTA8e90Otra2grvV6fTCfMS6pPXeEANRb1qZ2cn1LXFJaGlafIGRNWpXrVaTaVSSZ1OR3t7e4Gidvny5ZCF97+537yCzkSgBULFO07Tx9XV1ccSLQCVAn2CwniWal/IWNMg1pH57e1tZbNZLS0tPZX3k/VXUlCVPC7zmqPbt2+r1WqF+2NPQZq8VqtFEkiSwr4ZZxcsLy+rUCjo+eefV7FY1NraWpir8Wc6HA61tbWl3/3d39VP//RPhzrZxM6meWsON3pu3b59+5Su7GxaEugkduJG9ki6N2CJ88JnUTgIRjwb67Ut/n/M623im5YHSx7kuHoZWX02VtCMeC3NwxjBDdLL3W5X/X4/nHMWD3s0Gqnb7Qbutnda55od6fEaGrLcHqjFx+F+qnEeSMb58H7/ccUXrweiB8lxK0c9DeN6yZRns9ngyJ4Vpy+xqbmoCdlvaZrhRmgDxzGOlPIOOQrpn3PRDpxeRzmQnZeiaG+pVAoU0r29vdCrhBqJTCbz2A40CZu4stbjHo/1gLWYY501QQLW8Ww2GwKyyWRyLAXzT4KMPerfetLLaY9eG+qIlVOy4zU5PDfEMYrFYqCFe2NUbDwea3d3V1/+8pf1rW99S5ubm2cOxUvsXpv1jB61ee5FsSTQSezE7ODgIEDz1D7QHG9+fj7A9fS/cadBOsyskXWUpqpsFCNTDA/twot4yZ7SE4WNnOaXBBqgEY1GI6j6wKuXpsW6rlpGIXG8u/osm0wm2t7eVqfT0VtvvRXqXrwBKecB0cJxu3nzpnq9Xgh4ut1uoCEUi8V7+uG4qAMKbVAg5ubmQrDF+Y5aHEHg2u12CMygB1Gv5Op4dIHHeSIwY9wfxxE4KQP9Qughk8lodXX1wgQ5Xqd2Vp+RG0kTr70j489zpK5BUgiECGqYo+VyWZlMJqAv/C6fz4cat8XFxdAvq1arhaQK6xioEcgPAQ2oMeP5oIBkFmWKdYDainw+r5WVlchnH4QK3c9wjBcWFgLSRSPKs2Q8v3q9Hn42Ho9DLcvTul4YBY9qTu992EBxNBoFdJy/zWazIdkiTdU+YUC4Kh7zn72AgIgAZ319PdRozjp3v9/XF77wBX36059Wu91OgpxnwEjGxFkk1GMl1LWoJYFOYidmZN4cFYjXkrhSUrxfwqyu2I4mEITQ6yKOBLlowVGbT1z1jGCK6yJ4wKl61MwnY4ADNhwO70GFuGcKnEF0er2ehsNhKFz18eO+uAd+Bk+b2ijGzNEjanWOKjjGyeKLRTReNB03vw+u56wrwUwm0745XhN1EYIc7Kw5tw8yRxvj0tIEQu54OrILcgMCCmWLdx7nf2lpKaAxmUwmSKi7qtX+/n5E6h06Gepps66bc/r3s8aftfFhaG5PYozRrMz/WbJ4rcpxJiIetVbnUYNOXxvZW9hnfE/gHkGaWZecqi0poIXlcjkIdRzl+LZaLX3pS1/Sf/gP/0Hb29tJAfszbiRdk0Anakmgk9iJGYo+BwcHkUUbpSNgdowFnwwzqAzoCplaHPF8Pq/9/f3A2efvqMGBv8x544X0kkKQ5AXDbDQHBwdqt9uRvhlx2tbD2GAwCLS1wWAQurEzRjjanU4n0FNc/a3b7UYgaj8/tUI4cBTPk53MZDKhHorml7lcTtVq9chAh34NvV5Pk8lEvV5P+/v7KhaLIZvqVER3xkDaoM2d5Z4MOMXUcFy+fPlM1SichB2nE30cRr0MzTxdEADKGAgmDrxLnqNcuLGxEZBbircXFha0vLysYrGoq1evBsT2fmPkNNhZ/XzcoMXybpL0mIUkPOhYT9NO8lxPwx4XfXkY83qsh0Hs74eMP4zBdmi1WmHdhjLJXgbiGEfg+Pnly5e1vLysq1evBgGVo+br66+/rp/8yZ/U5uZmEuQ845ZKpbS0tKTnn38++EiJHVoS6CR2YkYWKq6SFs9Wej8ZD0i8m3gqlQrQPtQ36FPxDKlnfP3YviEhxxnPxvEZzxhzH2RruZaHQSsocnU6jdcsefAFmgSiQ+DntQMuwkDQSJbZa5cYd2p7XMGHnzsH3A2xBGh/UHQ8uOM5MlbStOEpG3QmkznTDSh9XBy9epYc/yexWaqEZ9k8oCB44eeSwjtKk0V/P/09Z/2Bhkk2PJPJqFKpBPrZ/WrYvFO9H5/fO13T318f52cpuHjW7HHpstQ3skYzp57kOjzZRqANLRjUnmtFQMcRHGjZXk+VyWRC/djy8rKWlpbCvJ11351OR6+//rq+8pWvaGtrS51O57HvKbGzY16rldjUkkAnsRMzqGBStJknzcz4OXx7FmhQHRAFHGzU2KiniCsosRFwPG9cGUcv4LU6ssPG5pK1Tk0BRZqbm1On03moQntqQHZ2dkJQRpAhKSAg8/PzWlpakjTlUaPE44Ea9DdUeQj+6vV6+F5SqGNCsc0pcqBH+Xx+5ibearW0u7urRqOhTqejRqOhbrercrkcan6g2YF8kR3nefCM75ddPG0DSWOOkv2/CBYXCHGn/Swa7z29cCSFecf7QTF2pVIJc9iDDAJakE+U9YrFoq5cuRJq3qT7B344qtTPxMePtceRYWmayODYT+JAJ3a0MbcfB20Zj8dqt9uRIOdJlN1IVNEv7vbt2+p0OtrY2FCv19Pm5qYkBdU/amZqtVpIejFvXf6eHnXFYlEvv/xyYCIcZXfu3NHf/tt/W++88452dnbONNKe2MMb4itnnSJ+0nYxdvHETtVcAca7gnsxHYiJO1xe/zIe39sgFKeFv6EgFb79LI6zpHucOEc24l/Q3ei5gSMD8jIYDEIh8oM2C4pGoZURiHFughjukXN44aGPk6SgCOaKZ3yOceD4nnn2Tu3c31GLIxurF1MzlmQUGVdJIdvIv9RCdDod5XI5tdvtMB/OkjFu0ETOsqP/tIz5QoBNXdJZDUal6XPyHjcuiVwulyNzluQJyQUQYO51MploeXlZ+XxetVot1NYchebx3nvg4jVsjtzwHjrVFVTzYeiuHIN/OUdij2Zx9NnrRHnOnU5Ho9Eo1LSw1sffB4Kex3lHXCVuf39fzWYzyEJ70I7oAL1vQGuYy75PEABVKhVVq9X7qvn1+3197Wtf07Vr1/TOO+/ozp079yhpJnb2jWceR4ET1bXZlqyYiR27zc/Pq1wuK5/Ph00aeVAK8/lisccRcYe63++HzzvFS1LgKTsdDqeC3gEU3Hvw4EWsswKMXq8XjgcNhs+60lO/338gx3k4HIbeOFJUXMGDIH5P4IMAQfz31BJ5V2zU1PjyQlqQKTjfBCI4bkcFHnyesQId4pg4Bk5Zo5aKzwwGg5BRr9VqqlQqZyrQcRECOodfBPMaq+FwGAqZz6r5c2q1WpF3ilqNtbU1LS4uRnjqg8FA77//fiSYzefzgd7z0ksvqVKpPFTwQdDkFFbeD5833iPr4OAgQuV8WOESr2tEkfJB/a4Si1qcIshYYqh87uzsqNvtqlQqKZPJqFarhToZULl0Oh1BZR7VcEyhqd25c0eNRiPsQcViMUIhzmazKpVKqlQqkT3H2QmZTEb5fF6rq6taX1+/r5PbaDT0z/7ZP9Mbb7yhL3/5y0HBNLFny5C0j8/BbDarYrGYJENiloxGYsduBDrFYjFC5/DAwmkzBBo4CB58IEpApo3jx5GQOBfagwRXG8PivFY+y6ZDJpXrpjEhdRyuynYUGkBg59fhKI0HVN4ItNPpBFluzAurHckhy+NjwblTqVTomszPoZSVSqUjC3ppRufy3Y5KcQ38DGfOETXv4eON8M6KeZb1IvGbmct7e3vq9XpKpVJn3pF2p5N5FX+v44FELpfT0tJSSBpAVSsUCpGg/34BCHOEY7izMat5H+8GSR1HmB7WSCzwXif0tic3alx6vV6oPUylUmq1Wur3+0GoZTAYhD2GZ89aCyL+ICMw4fnx99CAJYVrkabNSX3dZC2lHQBIps8vKMEu9ON2cHCg69ev691339Xrr7+u9957L6GrPcMGJTeelGJNShCdqCWBTmLHbgsLC6rX60HqkowsDmW8gI6ghs/gFJM1LRaLYeMgiIkjPB5UkK32Y9KlnPN5QMU1OZef7LA3+2Sz85490H9mLTRkEl1GFGoL50aWluxwr9fT7u6uhsNh2IA5fj6fD+eHlgYa5mNCFpwx9KCEzHetVjsyC9TtdoP0KIEORbKpVEqVSiUSXDkihXPGhszzP0uBDmMfn5cXwUAM+/2+ms3mmS+I531n/i0sLCifz4c5Sc1N/BkWi0Vdvnw59IEC4QVpfpjmnSCig8FA3W439Mk5ik7GGvW46CBBURLcPF3juTQaDTUajbCm7e7uhkCHOkdHdkgW7e3tKZ/PB/TlfobCJgExe9bm5qaazaYkhaDK0XECHL5QDW2320HMBuGMQqGgcrmsVCoVRGLi+89gMNCXv/xlffOb39QXv/hF7ezsHNv4Jnb8lsvlAt02/vME0bnXktFI7NiNQGFubi7IZ0I3cecY54RMk9eEONKDw+1KR2xWXnhKZsu59Dj6OObegwCEwkUTXPUGUYB0Oh3oDWSEsbianPP1+/1+QGdQkHPZay/mPzg4CBnHYrGoQqEQji1NN0cEGrxOwakP/X4/gkBRf8K1EXTNUl0jMIM2x3NDGYixJLsYR8m8E72kSE+as1AD4tRAD0JPItDx88TH/SSNwBrH6GEENU7beB+Z8zxHkA/WDxcNyeVyqtfrKhaLQe6c4HsWGjPL9vb2tL29rdFoFOomLkoj2fNotBCAurmwsBCkyp0izBpK8+j7SfHHzet80um07t69q16vp2azGRJX0uF7R+0Ysv8ujuN0ZAIe6jNLpZLq9XoIfHw+jsdjbW5u6u7du/qN3/gNvfnmm+r3+095JBM7aSPBE5+HNBM+6wmrk7Yk0Ens2G1ubi68fN1uNzjMOMI4HU5jG4/HYXNgwfdAh+waHGoCBAIelGhc9piMGRuaozg4nRTyAf/6Mff399Xv95XNZrW8vBwWFbLBcSpcfLHp9/tqt9vq9/saDAbBsaa2iPvg2vr9vubm5kKTwlwuF+mZQNDhwRjXz4ZHEASiQ+bPgzcoW/w9Bp2JQKDb7YbnR81BOp0O1A+U6xxpQ10Pp9I379M27rvT6YRMvaQTCXTiDQFPK/hjrvO+nOX6HGkaFDoqSuACcuiiJa749yS9VpjPW1tbmp+fV71ej6iyzfr8WQjmEzvaisWiisWiOp2O9vb2glIZ1MTNzc1IY+fbt2+Hd+Rh55K3ICDo2NzcVLfb1Xg8DsqVxWIxoNxQvdkXh8Oh2u12WMf7/b5arVaYg+VyWUtLSzPX1vF4rNu3b+utt97Sr//6r+v69etPfRwTO3nDp4qjxdRrJYhO1JLRSOzYbWFhQdVqNfSzgI5B4bw7e5LuQR1mOcZOLSOgcVTGay4IJKRpoTwLQZxGRaCFyhrXQeAFLAxKglNFM0ynHMTVl5CV7na7GgwGISjI5XKRAM8lpLkPrpdrY1wJHkAIoMGR1fZstSM/nNv7xbj6HJQ6qBwgR2SSJpNJCAYZX47nEr9cJ9dwcHCgZrMZgsxCoaBSqXQqTiHn3NraUrPZDHUbJ2HxHj2n6RR77duzkAn0d99FR5Dg9eTI07ofT3ZgCCLwvlArIT0bfYieZSNxE0ewH8cIhln/WFc9aUayh/l2cHCgRqMRHMujjHngST0PfHxv8vWYdZr9i+QgctLLy8taXV3VpUuX7kF/MFoB/Mt/+S/1jW98I6GrnSPzNc7N64UTm1oS6CR27DY/Px8a7xHouLQxRoBAhtlrdvjX62kILFwlzQMjsr0gI96DwDMe3rOHWh7O4RluVM7YpNi8oJGxkcWz81xft9sNUqIEUixMyC+7KIGLDLj0NpsdKBjBBpQ3grB0Oh0ygxzX7xXUyYMzfs7YUfgKvc7Hjg2bzdqpQi5W4E3rDg4OQnaSeyoWi5JOzzlsNBra2NjQc889d2L1EGcloGBuUtT/LGyQjsDyniArfRzjynvnFE9JEYETAqBngfr3rJvXHBKgPMlzB+WPn4N/8/l8EGRx5bV2u63xePxQz9zrGkF5RqORdnZ2Iog867qL1IA2s28sLi6qWCyqXq+HOo24wzuZHDYj3dzc1K/92q/pq1/96mOPT2Jnz45iATzN5M55siTQSezYjSx+LpdTv9+P9M9x+WgMtTVHRDAPkqB6YfQe8AwZ0tJsIK4y5WID8cWhUCgEapc7Mh4QOAICFW+WuXQ29+2oD5s2Dj/jgwoWn2m32yGQIVCA5ua9iRAZIACKG3Q/NstisRicB5fu7na7ajabarfb6vV69+j2I71NgOmbtNfnwHkfDoeBU073eRT0TsPB7nQ6oRngwsJC4K6fJaGE4zanfj4LQY40XTPa7bYkaXl5+Z7ahKdpo9FIzWZTg8FA5XJZ0qFAB0IDKCCdR9EA3t+9vb3QX+gkjfpAr8c6ODhQp9MJdN+FhQUtLS3dI+/9uMba5bWimUxGV65c0d7enrrdbpgToPS5XE7VajUgMOw50BsbjUZYa+i75m0CHIGkKelgMNCtW7c0GAwiwgWFQiE0B53VzwfJ+H/8j/+xvvzlL+vWrVtPPCaJnS2Dcu0y6fhEiRjBvZaMRmLHbiy8ZKQkRYKMeJ0MGwyLuNOgqD9xZMGDEL7nbz3IQcWMYAvKWVy0gL9LpVJBgScuf+3XA7o0q/EavyPQcbSGDYpAwdGi4XCobrcbOYaPkyM0OOoLCwsBWWLTJHDxpnfQ/RBTQJ7X75PgDZrd3t5epBGZB1uciy+XoSaAcucE9T2Qu9NysKmZYrwICmc9x/NqqVTqmdoUCahd8KNUKh3rPCLoPzg4UC6XC0G7I8RnXZL7cYy1htrE0+gtRV0KtYgg161WK6ApuVwu1B4+jWt0NMURQ+TJEaVhH2k0GqpWqyGR1+12VSgUQq3E/Px8ECBgrYwj907DpGZHUoQ+TOBEXQ7U4VmBznA41Gc/+1n97u/+bthHEjs/Rg1vPLnqdPbEpvbs7HCJPbM2Pz+varWqSqWicrms/f39UIwJbYaAwQ1HmqDFG1FS48CmgzqYCxqACpF5XVxcvKd/j1PZUG+Totxq6ApcL9x86TDQqVarQYknvvGkUocdt8n+dbvdEKxBuyO44XeooeHQ4YxSy4OCm/dSAKly5Tg3KHgoCHlhq9dKpdNpdbtdtdtt3bx5U++//34IVLzZKudDaIBju9PM573pqtPbqNM6rcJtgsl2ux16WiwuLqrVaoVA8FlBOS6KOY3Me8s87WANBKHb7Wo4HAbp7ZWVlUhNG3Sj82asxU4lPo37RA7cUXQCSwJO1pKndX0kxphTnJs9aHl5Wfv7+yqXy2E9plbH1wvW5/F4HIIVAjXQedY+6lJJPFEjCaLllO9isajl5eXQ+yzu1G5ubmpjY0ONRiOsvYmdL+v3+9rZ2blHQc8TjIlNLQl0Ejt2gx5D13V3bEEEPKvlqIXLHvOvOxrueMezGyAlbCjSvfU+nkmLByl+bD7DeXq9XnDaXT1rlhFQDAYDDYfDgIzE0SEPzAjwuE6CNb535MZRFkdtfIN2QQIfX6dpMLYorLXbbTUajXvoEY4o+fUz3u4EujBCvB7IZcNPOqBwwQVUllyBDi58EuicPcNxiwsPPO1zkDWF0ony4eLi4omJVpyG+doo6cg+QSdxHU4FY72gzgXJe5ItTyuLzVyadTwCP/ow+c/ZC7x/G2wDV9KE/nZwcBCURVlHuQ8SbHEWAPsAAVH8uUwmk4Ae3Y9OndizbfggLo4iRX2WxKaWBDqJHbtlMhk9//zzKhaLAcnxon02UhrwIfcrTbNrbCSTyUTtdjs40CA60tTx8WJOz0xCXXNpa++/wd9KinCoMZxjamAWFxdVKBSCyEKv19P29nb4GeZOU7fbjcjhcv8eNIDKlEqlEOxRAOu1PqBUvtnhoOAYuGy319OgEEUhK4W1bNRkF1dXVyPXhlORyWS0trYWJLY94wtaRCC2trYWIHV3nry7Pc/oJMydCA8icWAS2P/sWiqVCsim98R6moaDPR6PVa/XNR6PQ1PcWYXf582giiGjfFr1W6lUSisrK6rVaoEmls/nNR6PtbW1FZIULlxzUkaw5dcqKQTCrLndbledTicEHKyv1Wo1QmED7ZEUAhlk/Ofn51Wr1UKADcI2K/jk/UC2OrHzaeznccNHSBCdqCVvQmLHbgQxUMck3YOEQAvIZrPBmXcHOF4bQu0HWXlQo1kIhyM3Tn3xglPPCqdSqYjKWBxpcillpJNdqcyzvfHzeW8Zz0Y7Nc4VzNy8IaoX/3ug5BQPD9ziDSodFXOEyIsbodh5htIDg1KppHw+r1KpFM5DPRaOALRFAiL6ADladdKLskt4O5rnxcxJRuzsGu/FcTlyPiehL/LvRXAeSSKRJDlNVDObzd6jNkmiTDpdGe9ZAW9csKXb7YagWZpKo1M7CoLM75wOTJNnr0v1esij7h3arSPmiZ0vO2rvJABKnnnUzv+qndiZMBxJRyZ4IXE8gffjHFMCABAYd/RRRmNBn6UCRh8EaFioklA4jxKOS+xyHDZ76n+45lTqsLHn7u5upHlmoVAIzjJqRdvb27p7965Go1HgXxPkTSaT0KEbmh3yy6j7QN2ArlCtViNBEXQFghRqfECxOE86nVatVgtKUWRGpenCORqNIiIEwOMHBwehOSoBTKVSCXVX6XRajUZDk8kkIEVLS0uhhxJB1cHBgQaDQaSB6P027eMwZLOZl07Z6/f72t7e1mg0CtnZxC6OgRwgbuCJmItii4uLQar7YdAykNHj6t+RSqWCMiTrfDabDQInJJpm9WQ7bYvTY0HyPbE2Pz+vtbU1zc/Ph8baqECy/rfb7aDgxt521LOpVqtKp9Oq1+uqVCpBcCWx82PM9/hzxS9ILGqPtBr8zM/8jL7ne75HpVJJq6ur+q/+q/9K165di3xmMBjo05/+tJaWllQsFvWpT31Kd+/ejXzmvffe05/+039a+Xxeq6ur+kt/6S8lXNJzbGTMvQ5mVsaBrN0sjqkjI9COJEUy8vFjuhPL8UEacNgdxfFjzXLAHRHhHrgWFhzfwOCXozgnTfs+uAMV773jGUyakjoCQZDm4gcehHngGF/4nPIAZaLX64Uvgpx2ux0KsXkmOBqcGwUgJLwJKqG8lMtlVSoVFYvF0IzT/8bRrJN0JF1dzp0zxtwphsm6dLEsLmLiNW8XJdh5kCMdt5NAZb02E2EWmg3P6mPjSS+vu/P12um43qvsad5P/FgehPmaDZuhUCioUCiEdRJasO99Xos5y1ibl5eXtb6+niRrzqHNoq7F65ATm9ojITq/8zu/o09/+tP6nu/5Hh0cHOiv/tW/qk9+8pP6xje+oUKhIEn6i3/xL+pXfuVX9K/+1b9SpVLRT/zET+hHfuRH9Hu/93uSDjeSP/2n/7TW19f1+c9/Xrdv39af/bN/VgsLC/qbf/NvPv07TOxMWDxwIOhwZRukn+OyxRgvNk4q9DY4+7NU1qSpWo6k4HCjgkM2st/vR4IIkB5XYXOaGA4wjrKr47DQdDod7ezsqNVqaTgcBinUOC2GDWxnZ+ceKW1XdXJpU3fAoAHyNxzTN3DGmevk+j0w4u/pxA3CVa1WVSqVgrNXrVYDYoNUdCqV0vr6eoTqMisbTtbyNB1HUCUfW9CcyWSijY2NwKsvFAq6evXquVTWelYMqipz5jgpa6hiId17Ee1R6U6uDnmSlk6nQ5Dj6yHv8nA4VKfTCQkn1lMU2uhFs7u7q36/H4IKghGo0E9iUHbxj6CnUY/T7XYjNZWcc39/X9lsVi+++KK2t7fVbDZ1cHCg3d3d0GPnfu9CNpvVX/yLf1E/8iM/or/0l/6SXn/99Se6j8TOlsUTMRiJzQTVidoj7Ri//uu/Hvn+F37hF7S6uqrXXntN/+l/+p+q2WzqH/yDf6B/8S/+hf7z//w/lyT9o3/0j/ThD39Yv//7v6/v+77v07/9t/9W3/jGN/Sbv/mbWltb03d+53fqb/yNv6G//Jf/sv7aX/trSfbhnBqZrbjilgcyXuwfd5ilaVY1nmXl91DWnL6As+/H8Uxf/HyeZYvXwxCYxa+PepO44gn1KL7w+AKF84xTQbaO4MkDwVmqaR40znJMvD6JQIZzQnPj2hy5cNlTAka48mzKIGKMix/7QXbaDqRnWR1p5DlCN6FXB5SY8ypS4HP9rN6nv58nYRcFvYlbHLV+WHM0+iTH7qi1xNdJV5UkgSZNEXdX1fMaz6d1fSRUMplM6GnjfdG4Xv71Ncf7wJGg4rod1Y9fcyqV0pUrV5TNZvXKK6+o1+vp9u3b96h0JfbsWoLcPLw9UWqMbr31el2S9Nprr2l/f19/8k/+yfCZD33oQ3rhhRf0hS98Qd/3fd+nL3zhC/r2b/92ra2thc/80A/9kP7CX/gL+vrXv67v+q7vuuc88Fsxepgk9mwYCzdOO9k0Fu04p9qL7gmOHFEh60ZQzIbB917Tg7QzmwHqYk7HkhSUbOL0BQpfC4VCRMI0l8uFoIUghm7FZOb29/dDkb+kUHtTrVZDISr37oiXU7rcXLCBe4TahnE/BCDcKxvsyspKUIiLOyXQtpxGUSqVNDc3p16vFwIbNu3jkPU9DaNGanl5WdJUce/WrVvKZrOq1+sqFouq1+vn4n7dJpNJkOnd2dlRqVTSysrKaV9WxE4CMeB9R0ERB/IiGQmPh63N8aBImiJvp9kEWJqK3xQKBVUqlYCgsKbiT2xvb+vOnTuh5vA4UGb2AhqKtttt9Xo9dTodjcdjVavViFQ1yE273Q49e6h7RC200+no/fffV7lcVqlUCiqfcUGdcrmsfD6vv/23/7beeecd/fk//+f17rvvPtX7S+x0DP/IkRun3l+0tetB9tiBzng81v/8P//P+uN//I/rox/9qCSFRaNarUY+u7a2pjt37oTPeJDD7/ndLPuZn/kZ/fRP//TjXmpiZ8DYQLyOhBeV+pq4xDKIi9eUSNMsWXxT8ow8mzZfbL6O0vhm7qiQX3McgfJrd4cgXlczGAxC/Qs0OFcviy9EXNNRmVEPvhyNcFTL6T1sej4WUNokRahqs4p3Z/HIOS6B0FnM/D+M+fMkgCabKx3WGRIgj8djdTqdIDRBsH3eMv4EzWe1Jum4x9udhovkJDjSfL8M8WlQ057EeIZO7/E13PcJkjeeyGI9fVr37ftaHJX34/M9+wVJFySrSdCxJoEWsb/58fjd888/r7m5OX30ox/V/Py8rl+/niA7z7i5cqib722JTe2xA51Pf/rT+trXvqbPfe5zT/N6Ztpf+St/RT/1Uz8Vvm+1Wrpy5cqxnzexp2PUeqTT6dA0s9/vB3TGM200apyfn9fu7m6obaEOJZVKaWlpaWZdSiqVCoEGGwuBRTabValUCufPZrMRqhXcfOo0uC5vcjkajdRsNgN1gIJ7rn8wGKjT6WgwGGh7e1vXr1/X7du3w7FZmEB9yOKx6bA5I5eNEVihwEPTUQqGobiB8sAvT6VSIdDCge10OqGmyHnobPIINPA8BoNBqC2iCHhtbU1LS0vPrENIsS7zrVgsBvRvb29POzs74TkuLi7qvffeU7FYDGOxtrZ2btAsUMJ0On2mA53jNsQnUMS6CAayK00R4FnUcZzueFPOuDN1VuvYXIAF6u2sYCOeWILeSy3l4xgJu06nE8RdQAydMsw11uv1gLBOJhO1Wi2l02mtrq4GtH04HOq9995TrVZTt9uNUIrn5+fvEdnJ5XJ64YUX9PM///O6du2afvzHf1y3bt16kiFN7JQNJkc8YF1cXAxsk8Sm9liBzk/8xE/ol3/5l/W7v/u7ev7558PP19fXtbe3p0ajEUF17t69q/X19fCZP/iDP4gcD1U2PhM3agISezbNFUJcQjquQBZXPGMz8Mw7vyfwkO5FQ9xc0cyRiXjmw2tWZqnkuJwzRhacTQqaGMGGK6Y5UkTA4+efTCbhe+45LkzAv/yO47CZet8gxiReH+SKPVyvj5GPPf8vl8sqFAqqVqtBEehZdgYRq2A+QWH0JqIO/yP+0Gw2w/NmQ3HK4WluLmR7nXb1MIEYc4Ag56IVsfJ++Nx/luf2o5ojt/czXz+fxYzxLNTkJIy1RlLYF6jz5F1jvWbNJqHkzZsJYDKZTNhThsOhut2uer1epHdbnHZJrSXB0nd/93erXq/r9ddfv7CJjfNgs9QB3Y9KbGqPFOhMJhP95E/+pP6f/+f/0Wc+8xm99NJLkd9/93d/txYWFvRbv/Vb+tSnPiVJunbtmt577z194hOfkCR94hOf0P/2v/1v2tjY0OrqqiTpN37jN1Qul/WRj3zkadxTYmfMyByCMEgK6EGxWAxONrC897/xWhpXpuG4oBwcU4oWmLNB0B/GZZJnBVlsQGxKrnjmTiS1OZ1OR8ViUaVSSdKhU4wjAGed6+QcjEE80OP62cy63W6k74LLSYOKeWYH5Ir6H4ItaHY4K9zX/v6+ms2m5ufnQ/8fpJ/z+byWl5e1srKi5eVl1ev1SJPRZ9my2awWFxfD2EAxJKsPDYRn32w21W631Ww2NTc3p7feekuVSkVXr16NdCE/rYw283V7e1vdble1Wi08wwc9q729Pd29ezfIizOPL4oNBgPt7+8rn89Hsv4XwdLpQ9UyKLdHBTsEfyiZea+w82qz6LyPY6h8NhoNSQpJIvYh2AckU6DSXbp0KQiiSIokUxydB9Hp9/taXl5WqVSKIFhumUxGL730kn7u535OX/rSl/Tf//f/vXZ2dp74HhM7O4afcFHWsIe1Rwp0Pv3pT+tf/It/oV/6pV9SqVQKNTWVSkW5XE6VSkX/4//4P+qnfuqnVK/XVS6X9ZM/+ZP6xCc+oe/7vu+TJH3yk5/URz7yEf23/+1/q//9f//fdefOHf2v/+v/qk9/+tMJanOOzSlWnomI1+lICgiI/9w5zi5WgBMvRVXG4sbnJYUsPso1HhTNyuZyXKherozjktdQ2qDe+b27aAGSqC4YQKDj1xnPMHtdjf8N9+eoF9fI/3HenafOosh9E+jQo6JcLqtWq6lUKkVqWKB8tdvtQPdKpVIqlUqhRu8sB0OO6Dn9j+fH/OMefEwJmBcWFtRoNIKj7D0vTnqjgcvPHHUqJM/2KMfNBS08yD9thOqkzFUZL8L9zjJPttzv2btAi1O/zisC5vSvJz2OMxEcRSMJx/7RbDbD/gHCwzV4jWShUAh/NxgM1Gw2Q7+dxcXFkKiZVcdaq9X04osv6gd/8Af1zjvv6Etf+lLSUPQZtFnzktYQyfOM2iMFOn/37/5dSdIP/MAPRH7+j/7RP9J/99/9d5Kk//P//D+VTqf1qU99SsPhUD/0Qz+kn//5nw+fnZub0y//8i/rL/yFv6BPfOITKhQK+vEf/3H99b/+15/sThI7s0avGxZ6b2bpRZVxR5rslxdosiFI0148BB9x9TGvbQH1QOEGNARHsFgshk2Hwn0pKmk9Pz+vSqUSHAIWlMlkEjjVo9EoNMnEuSwWi5qfnw+oEPUu0KQc5fLsKvefzWZDI0+cDOhToD0o8hA84awTzHid03g8DkEemexsNqtisaharRZoauvr63ruueciVLfxeKyNjQ01m0197Wtf09bWlra3t5VKpfTRj35UKysr+p7v+R7lcrmTnGKPbNSmjMfjiEJeu90OzV1Bx+ghBGoGPXc4HAbEsFqtamVlRZVKJQR6J+U40+AUPj7fc30EYDhAbiBa9Gji/giAz7vxvl00yp4b7wLP3ulWcfNgB0T0YZDDZ9Ge5vxHqZKEhAvKjMdjNZtN9ft9vf3222GfIfHkAVc6nQ6Jp06no3a7rXa7re3tbbXb7dCvp1arqVqt3lNzxX700Y9+VP/wH/5DfeYzn9GP/diPqdPpPLV7Tez4jbkQrytrNpu6e/du2MMSO7RHpq49yLLZrH7u535OP/dzP3fkZ1588UX96q/+6qOcOrFn2AgSvO7EAwhHaVwxxrNYcRUwV28jMGFjIhtG4EP9Db/37ticD2fHz+dZcDYnr+VxNAn5bJxhrh30iADDaXL8jnvx//v4UDvhWUFpWpsUrz2aRcHjHlz5jXHGWafupFQqBdlTCmnZhDc2NnT37l21Wi3duHFDrVZL7XZbkrS7u6tyuax3331Xa2tr+s/+s/8swh0/a8bcAZVCfc3lyh2BdFTRn7+P4WnQBhYWFpTL5SJNbt2B5/0AvXInlg3RHVhoj8dtONaMF9d3kuaU0Itqs1Qw3VhfECuIy+A/znx3Ou95RYTc8vl8oF9DSybh56gYKmvQrH2v8MQdxv7J36RSKbXbbc3Pz9+XhkqS6+rVq/rRH/1Rvf322/r3//7f31NPmtjZtLgvgCGIlNReRe38p+wSO3UDdWEjdYd/bm4uoC2+qPsXKjJQu0BAnDbGws7nBoNBQDkmk8P+GChtuUiAB1nQApx+hHPLdbfb7UixP7UeXBNoCo7A/Py8Op2O9vb2Qq8eAph4gOOGY+o8bhxx78fg9BGnSLApci2cq9frRQIgV1MrFouqVCpaWVnR5cuXdXBwoF6vp+vXr+vGjRv6Z//sn+lzn/ucms2mBoPBzMQH9/Yd3/Ed+tVf/dUzHeiA4nQ6HXU6nYD2MUZkQ725nz8nnD/GjgzqSTvN+Xxe+Xxe29vbAZnhmiWp0Whof38/ouzkghaSIt8jUHHc1u/31Wq1IplqaJ0nZQR9FznQAeH1gMaNoBlZeexJmnuztrCenXer1+uh3+BoNNKNGzfU6XS0tbWlg4MDZbPZ0D9Hiq7/nrzyNTe+/iKLv7GxocFgoKWlpfuObTqd1rd927fp7//9v69f+7Vf0//wP/wP6nQ6SaDzDJj7RW6tVktbW1uJfHjMkkAnsWM3zypDF5pFFfG6Es8U8rcs7O6EsQmTlSWzTqYMx9/rbCjG99oerpMMs9c1xDcYR48khQAEVRzqJPgbl6qWFCSsqQcZDAZh4eJcLrLgvHhXTYvfD/86xz6OhsXRKmhrtVpN5XJZlUoljN/bb7+tN998U2+++aauX7+uN998U+12OzjT97O7d+/qn/yTf6IPfvCD+lN/6k89kVN0XIaAAFlWAs5utxsJPh2RYy6jBIl4g8t9H7dxHcx9zosMbiaTuSfQlaYoKRLjzGHPDjImJ0FbI4g/qjbuJCy+BlxEi9dOeg2grxMgwv5zKdrf7FHOGa9lPC4DtXWGAAm20WgURG78/o/TCOrjDAMamyJIkE6nQ7NrV2rjfYUSnk6nA/rOHtbtdnXr1i01m00Vi0UtLCwE4R831oGXXnpJP/7jP663335bX/rSl9RsNrW7u3us45DY49tR6mo0yT3vYiGPakmgk9ixG049GUEcebLH0hSCj4sJpNPpgGi4M59Op9XtdrW/vx/60lCjQnaMDQPaAJuYO3LuwAL/g0Dh/DlliWvyJqZ8n8vllM/nlUqlAo0olUqFLCjnohcPEDObfrlcDudxaVFHaDxgAOWBurSwsBAprpYUaZTqBaogVtQnra2tqVwua3l5WdJhdvAP//AP9a//9b/WtWvXHrmj9s2bN/WX//Jf1h//439cP/ADP3BmA518Ph947V475qgGdBLmIfOsUCiEHkO5XO7ExFQIgnGMcrlcoB0Wi0VJh3ODOp2tra0w76VDiiHOFlQ25M5RQjwJI8jHWTuNYOOi1CLdz0hAYQTzLoYyNzcX5htrB+swiPis4vej7CTHfW9vLygmsrYvLi6G96NWq0WU0I5biCOVOuwFN5lMVK1WA5IDXY19Yn9/PyCeJAjZAzKZTNjr8vl8YBvQ+4yi9IWFBT333HMqlUrhvmfZRz/6Uf0f/8f/oX/37/6d/s7f+Tt66623kkDnDJsncN2q1aouXbqUCHvF7GKv8ImdiOHsp9NTaWVXCPO+OJ6ppkDWM4heu0NxtVPF/GtWhv2obF080HIxAimqauY/w0FEzpfCb5xHGot63QwbvCvnpFIpdbvdcH1+H4uLixElMAqGvQgYOp5nWdkUHdFhDEEkQHGWlpYC//6NN97QH/3RH+n3f//39c4776jZbD7Wc59MJmo0GvrsZz+rF154QR/96EfPFEUI9I1Ap9lsRgQcmDue7ZUUxonnCzWSwPS479FRP+YOTifm83dpaSnSK8drjjgOgc9JUw1xkOMNfBM7PfM54cZaNGttdMQbOuRpBZB7e3sRChiJnzgFmDnnyOZkMgkBB02VK5XKsbzT7GHsKYuLi2q1Wtrb29NgMND8/LzK5XKoZXNEWVJYq0ClW61WhAXQ6/XCetRqtUJiBzn8+PNJpVJ68cUX9SM/8iP6/d//ffX7fe3s7CQBzxm0+DsnTecTwk+JTS0JdBI7doMeRZYKBIRAh+w5zjq1NQQRBAoutQxFx6ll/hkobGTp4/Qdd/6lKf2CY8/K6MUzawQui4uLARlAxQz6B4GOB23xovB+v6/RaBSQKRTS2ISpbwIlGgwGIasH7QE6GWPidBIcde7B60qee+65UJdDsHft2jX9wi/8gm7evKkbN2480bPf2dnRv/k3/0Yf+9jH9OEPf/hMBToEpNR2tVqt4Dw4rYYAhjlGNhUEsNPpqFAohB48JxHoEOTgkM4KEgjOV1ZWNBqNdPfu3ci89bqjubm5MH9P2qDVJHY2jPkVd5ZcJj9eK+KoNyj9rIzzSdhwONTGxoakKUUOlB8EFIXCOHuAAKHf76vdbgfE+7jeaRCWbDarbDYb0FaSWcViUf1+X4PBIAQ61Jd6IiyXywXmBJ/r9/thjeJeCXJc0dTtlYaDGKEAAQAASURBVFde0SuvvKJ6va4bN27oW9/6VhLonEHzNdwN9dSLjlLHLRmNxI7dWJwxghkkbxEjIAioVCoRrjSBBc3RnEKWSqUCdE92nfP5OTmGiwTMEkLwIENSyAZms9mA6JBVc0cRFApVNrLUxWIxUBMIaDzIcs51tVq9Z+Nho4LDzfWikhZ3dnFSHC2j0Hx+fl6rq6uhJqdQKGh9fT0gFG+99ZY+97nP6Q//8A+DotqTWqvV0he+8IUwbmfRoHngUBA8SgqUkPj8mUwmQZJ1MploY2Mjki1eXl5WrVY7luuFJsm/R9EUMplMEMMYDoeh7xHBrlM2oX6ScPCkwtM2D66QdU/s9Mxr/xx5BgGM19K4IqSkUD/i6nl87qSun8QY6zCOPEkJklGj0ei+VEn2E5+Tj3IfrqSGdP3DOp3z8/NaWVkJzUN5Z6Gi0ecLNAqUh+QK606v1wsMgP39ffV6vUhj4FKppGKxGEkUSlG2w4c+9CH92T/7Z/Ubv/Eb6vV6ajQaiQT1GTIo+T63JpOJWq2WNjc3A7KX2KElgU5ix24EHi4u0Ov1QvCAA8+mhIMGlY1aFrJVUNVASkqlUkA8pOnGh0jBwsJCcF6hyLFRE5ywOUNniFOD2BxRVYsLBLhUMQEG9Q6NRiM40aPRKCBUBFEgRajykIlj8/ZAB1oDCBljhONLjQl0CA8W0+m0VldXVSwWtby8rHw+r5WVlVAUfv36df3iL/6irl+/rps3bz6VZ9/pdPTaa69pdXX1TAY6ZEF7vV5wKBh3xpL+RTg/OHbdbleSAn2y3W4Hp2phYeHYAh2CkAfxsAmE6cGEpK1LvXP/JBno9UHwexzG+5lOp5NA54yYSxmTwHHJ/Xig4xafJyedTeZaoZsRYBDcsAc8rIGOuAT/w9rBwUFYvwnmH3Y85ubmtLS0FNTTms1mUOxk3Udpk/eUfRMmQKFQiKiOjkajoCLJz1dXVyUpqC/Ooim++uqrevXVV9Vut/WVr3wltBlI7GwYqFx8btFTKVFdi1oS6CR27IYjTS2OOzZA8l4sL00Lrr22ALUy6mAowkbgABocSjXQu3K5nIbDofr9fuS6xuNxaAbJF04gmwRBDceERoZzCJWDpovtdlvlclnFYjHS1wSHAbTIldnIrLXb7ci1QE1zLraLFkjTQnmuE0cdihIIFY7x+vp6kJHmXre2tvQf/sN/0GuvvaZr164FfvvTNApruaazZASm0ClBn6AplsvlgNbw3Pg7DOcdtKfZbOrWrVuqVqtPfL+OGrpK2aw+Cm6gf/EAjvsgGeDIHcc+iWy810Aldrrmzxy6sDStoZpl/X5fBwcHYY05aRuPx6FY32sjvdbycUUuOGZcHCdurPms7UjV8zfQyh7FFhYWtLy8rFwuF+7x9u3bEWYEYw9iIymC4lP/SdJwMpmE+qrd3V2NRiPVarUQDB51j9///d+vSqWif/7P/7l++Zd/eWYrhMRO3nhH44HOnTt39MYbb4QkXGKHlgQ6iR27kXFzhIGMFCiMU8qcgkCmzikBZJ5RGvNMe6/XCxsNgQ6OpiMgOI/QFLyGxXnQfJ7CTudJu1Ps1AmCEn4WFzmIc93ZjD2YcklRD8II7BgbxoTrJMsHjY+Aj7FARto331arpa997Wu6du2abt68eQ/v92kYNUhsrGfJyGR7jZfXvUCN9LqceJbbkT14/tvb20GJ70nMqUTUYuG03C/QidMdyVDjlJJEgFJ5cHAQ6JMnEej4mCd2ehafRx5AP8jJ39vbCzSxkzava6TOzoMyR+Qfxx4Gadzf3w+KbtBEvTcUKP2j2NzcnMrlcqB1p1IpbW5uRlQ32Z9cOpu1q1QqhfpUVxfl/yAzBEiFQuHIMfrQhz6kD33oQ3rttdf0m7/5myExmdjpGowRf+8Q/7l9+3ZoBJ3YoSWBTmInahS8A8Xj3POFwwXVisJ5viQFWVyCBTLSrjQGJE/thTvvcdU0l/QEIfFMHtQ1MmU4ZwRjUJ+gSGSz2YhUNEEP10gAVSwWI+pxoDBsYjinfHldkNeRuBKc9/OZTCahz8uLL74Y6nK8EWa73dY3v/lN/d//9/+tO3fuHEuQI0ndbldvvvmmrly5MrMW6bQNys5oNArUE1f4o0DYZb/5PfOQ58OzabfbIYh+HIoWKCbn2NvbU6fTCfMP5PAoYyOsVqvKZDIR9JTnzJzm58zv43g+cTrQUQW1T9PiUsmJ3WvUN+I8eSLnfmOWz+dnBjkuhHJc19tut4PMezqdDgkAKFutViusw74esh4/Kb0OalkqlVKtVosohaJsViwWg6Lj49jCwoLq9brm5+c1HA7V6XS0vb0dEirQpEm4dbvdkAxBVTOXy0UShXt7e2o0GqFGNp/Pa2lpSfl8XsvLy0e++//1f/1f6zu+4zv0f/1f/5f+3b/7d08ydIk9BTsKraRG9CyJ/pwFSwKdxE7UCHTceYfuAyxPoMMm6sWW0lQYgAV/MBhEnCU2Xu9HAK0BxzUuJEDwgTMYl1Hl7/nKZrMRmli/3w8IE7/v9/shO+pOM5xo79XDeVxKFATJ0QKu1elpvqh5nxxJAQWq1WqBqsD44IzfuXNHf/RHfxTQteOwvb09bW5uqlKpHNs5ntQYZ8bWuevxn08mk+A8M0/8MziPKLLdr4fFrOuQprVsOKAkB1w5737G3IKaAhLo5yHQxuINZ5+m8d7Hi94fN9BxFPYoi4tIJHavEZz7PGbu32/MjprPxxm4cnyoW8jn48hDHdvc3AzIhjQNunhvn9QQx8lms6pWqyEJwtpfKpWeuEYPsY7JZKJ6va50Oh2hrDmDgERMr9cLtafQ1kC5uEZQXgId7ofrnTU+H/vYx/Qd3/Ed+sxnPqPf+73fC3t0YqdjRwU6IItnLZF42pYEOokdu+EAQjNzZTBv5okz753e3ZHkxfbMOUEGzj0ZLj4vKXSKZ5Obn58Pi75nzCXdE0BwXOB9NhiCELJpLmlNsMO1H6Vq5ONDoLe/v692ux0cWegYTlWam5sLheUIOHDdFN9SY8Tne72eFhcXI4XnW1tb+pVf+RW99tprIdN+XHZwcKBWqxXoEmfFPJglqC6VShGKYqPRCM9aUmSOOpIGarO4uBgKiqnnopkfXcpn2Wg0CspofL7RaIQ5wHuE81IqlR7q/gqFQkgScGzQIeY5Cm7Uux2Hs8p94GwhksD7wvnvF2iB4FKbMDc3F7Le7nhzfwSjnvzAqfeavItsvtbwPWvpLHMp+1lO8XFmk70X1Hg8VqPRCHP44OAg1CZUKpUw7712R3o6NWGZTEZra2thTpNJJ/h4mg0bc7lcaPqZyWTUaDS0vb0dEm2I1fAcfX9k70BhlPpBnhEoUL/fVzabVaFQODJAS6VS+tEf/VF94AMf0D/9p/9UX/nKV57aPSb2aIYIk+8lJK38XU7s0JJAJ7ETMc++OjffkRkcQ+8hAy1ImgY6ns1yHrb3i/Fzzc/PhyyYoyuSwqZAIOHICQXoOERQ7qRpcOJOktd4kGVzmpwjOjiT/sW9cJ1A0F4szzEXFhYC3cQ3ccbNuemMGVQ4xrPX6+n111/X+++/f+xZWJerPavGWEPfIVh0xNCfJ8bcgyKDiAToideozZo3fAaq5WAwCEp9jUYjPGPOQ1D6sBlV5gQCGf7++LwEOSWQPw7j+AQcLqbBPH5Q4AE9p9vthpoExpbEAg4gGXZXRqRvF+vFRQ904vVmDyriZxyPQnSOazx9DYlTjVlXQSoIml1cw9fDJ7U4ouoJtqdtvLuSgkBPq9WK0G25hlQqdU8w6AEsAaE0FQJibyHBRr3TrDXgj/2xP6Zqtarf+q3f0je/+c0QPCV2suYB9qzfJRa1JNBJ7EQMB6fT6UQcLGlKlXBUxNEQL+rnWPxdKpVSpVKJ1EhAGaP/AxsSgQrOjvdb8EyvU8agBNy9ezecO5fLqVKpqN/vB5RiMBioVCqFQn9HehBP8PoaCtalKF3I++kQsFDETwYfOlImkwk1QnweJ8ARHxdRAHGQpK2tLf36r/+6tra2jp2GwLidNSlhnAECkbm5uZCx9gBTOqwNQ9mO58fvmFtxwYlarabnnnvuHuqio0Gj0Ui7u7vq9Xq6efNmoLx5jQ4OyNLSUuij8ShZ41QqFeYM6lDehZ3AIJVKaXl5WdLxyQQjhZvJZFQqlYL0Nb97mPug/oJxJysNIoq0+/7+vnZ2diJUxHQ6raWlpYAeJRY15u9RYwNKwpoJfew4DZll3g/WVsRkWDNd3ZLgIy628Kw+81wup0uXLoV3n7Hw+lYCFw8+eI+h2Lnc9Hg8VrPZDOhqqVTS6uqqlpaWdPny5XuUHVdXV1Uul/XDP/zDWlpa0m//9m/r1q1bJz4WF93cL/KfQdtM5KWjlgQ6iR27sRC7k495vYxnEp3yRSDkx2OzIpBwRTXPYsUzeWyGfn4Kvb0AnayXK15JCufid77YsLH65oCzzP3zdz4WOHeOCLlTTGCIsZnH++Z4gBaXAeWcXNf+/r663a5u3bp1Iv0RfBzOmjnKMsshYv6isOd1ZtxTnPpHQAGiEEcQ4+fvdrtqt9uhuacLHzjl0Y/5qIEI15rJZDQajUICAOTKVfyO0zwQ5N162LogkCfQVq6b9wk5bX9/BoNBKMQmAcFzeFad3pMwxieOZjoycBJ1Gl47R1KJmjeSSIhonGfjnSkUChoMBqEm1M3ntLMFnJXA+89+QXLM0U4U36QoQkCQe/XqVXU6Hf3BH/zByQ1AYhGbtXaRuErqp6KWBDqJnbixcUExoMASOBaUguADZIa6iFkNN+NqWHyeY0Hbcj41lBYcvPF4HBTdUGpjk6BXBOjL7u6upENpzlwup3K5rEwmE7LKqPtAifOaHzYnKFIgBPEAEEfW0S0aj85a5JzWl8vlIgGeSx93u129/vrrunbt2oktiGeZusbYg37RVZprjSubEZhCeXQKC3LSnU5H/X5fnU5HjUYjqN3Fu8hLh3SU119/Xd1uNwRP1K4RJIFMQtVaW1t7rFqDVCoVgiQaAr777ruBDpbNZlUsFk/EaQRFos7mYREqRwVBzbDxeKxqtarBYKCdnR31er0g/wtyOxwO1Ww2NZlMIgXriR0a6JjT+xCHiUuqx5MwT9sIpkAAqZVEcczrui6KLS4uqlKpaGlpKaC/vV4vBIGsMzRPJTnD3lWr1SKJAVRLPeHB2lOv1++RyE6n0/pP/pP/RB/5yEf0b//tv9W3vvWt0xiGC235fF7r6+v3qPq1Wi1tbm6GPSyxQ0sCncROxLyQXlLE4Y3zgcky8eXcfae7ORokTTPW8fofqEgEQnzOOfp83mtsXCEKxIRroY4Gx3VWPZFTJ6R7URVXAopT+RwBcooTmxM/87od/pZzcD1xpGh/f1+3b98OUqUnZQ+btT9pO4qPHqdK+uf5vQeTLhwhKaALnU4nyH7z2bgNh0MNBoNIDZkHu/7++DkexzhuPp/XwcFBSBxICsmE47b4e/so8s/x8YsjW7yTs/6On7t6YbyeLrHpGsT/peNXU5tlvjYyb+OI+UV7biAu2Ww2NMOO1w6y95H4wryWyFFpAlYSAf1+X91uN6wRcSpjtVrVwsKCVlZWVK/X1Wq1zmQS67za/Py8stnsPYieK/IlNrUk0Ens2M0z0qVSKVB0yB5lMhmVy+WQVUZNxtEPFwJoNBqSFFEiw3GjpgInM66+xOLPBko9ELLH3W43BEbSoXoPDhjX47QXd3rZdFmEyPSD9KB2hTMZD/qgxZFBdUGE8fiwd4TXKqVSqdArwRtCenADvWN5eVnlclnpdFrNZlO/9mu/pjfffPPENqf5+XlVq9Un6itxHJZKpYJS0c7OTqgFoFCeOcpYQ/3CKaAGhHoT5l2r1dLW1pYkaWNjQ9/1Xd91ZONQEI3FxcVQt+Uca2iG6+vrKhaLT0XRibmcz+e1srIS5vP9+vI8LXNZee/K/rQc1nQ6rUKhoBdeeCHU0VGDwPqAk9DtdtXpdFStVhNk5/+3hYWFsB66Ax0P+FnHjhP9Y01kXWYf8SSApEgS63HMqcQgH2fVGPdKpRLYCP1+X4VCIVLvydrOfgcKRtBCMg1mgQvYdLtd3bx5MyBq1Wo1sjawHv7oj/6oPvzhD+sXfuEXdOPGjVMZj4to9D/KZrORnz/ttfS8WBLoJHbsRpYIp9sz6EDrrsTkjrzX6rAwex0KtDWMAIZjchxf4DkvNiuj7/x0PgsC5D/zc/ixkMf2+hn+Lp59I0CKK0G5iIDXKTEunv33e45zs7kWHPTBYKBbt25pc3PzxLK0Xr90VsznklM5CBq9BsGfmW8iXvPFcwApiB9zVmaU31FrsLe3N7OGx4Pep+lYQgVlDI5LgCBu/r4ch3ldoNcjOKroz/c00IqzarOQ16OosidBcfR6S9A6qDlP4tAdpYD4LBjocS6XC2gxiZm4efJMmjaYhv7t+wXvAfRFEgHZbDYI9/i7+9xzz2lvb++BPb0Se7rG84/vp/F9K7FDSwKdxI7dBoOBbt68qVqtpvX1dU0mk7AwplKpkAHn5cQpd8fR6WG5XC78nCAKOo8LB1C8Xa/Xw8Lu8qTSlPbidTJeyLy5uRkyxDjrqGHRywMnNZfLqVAoBOeRa93c3FS73Q7Z5FqtpoWFBTWbTY1Go1ATQY8h7h0FsN3d3XBtBEOo01G3w+KGCMFgMIj09SmVSqEeamNjQ1/84he1ubl57P1zMJCQk3KkH8ZQ66LJYKfTiSB6zKNisRjJkDpitrCwoGKxGAIQL+jN5/OqVquqVCoaDAba3NyM1KKMRiPdunVL/X5fCwsLof7AG9iSjfW6s6cZLC4uLmplZUW9Xk+7u7snMh8ehab2pEa9Qrlc1urqakCTQXrK5bLy+fy5L2R/EvNEkwcFJ5m0KBaLIVAFnfOE0OP0Q+r1eiEB52qfz4rBYKD4PE5b8lpCr9OpVCphrRoMBtrd3Y3sHeypHMv3TFRFOf73fM/36OrVq/rZn/3ZUxuHi2hes4lNJhO1221tbW0da/PvZ9HOjteR2Lk2r7nxTJpnD5365ZQ1//LsOV/QJzg2VCOyuKAk3m/Aufp+nni23jPDznl2VCV+H2w6BCTFYlH9fj8IHMQ3ZKecOQ8dh9iV3vw8jkj45hbnZ3vNjitTneRiSKBzVLPM0zDGkucV/52LQDBfHF3BOfa6AXcocAjo2E6TXAJ1GtY6pYRjwpt3BBA09DjsLKviPan5PdE8EscNcZJnMat/WuZrykmNG2stySyn6Dxs7V98L2E/ggL9rM0BqMl8xZ1e9hXmOs/L69LYo+LIjtfqgBbNUmSEsvvCCy/o7t27un37duJkn4AdhehA/U8QnaglgU5iJ2I4lGSKvNklaAyLLHUILL5QiXiBXY55fn5ehUJB4/FYvV4vOP0u+4vDjxoZtRapVEq9Xi/Sq8Rh/bm5OZXLZUmKdLn3LvLwxVl4UO1qtVpaWlpSqVTSK6+8ovX1dc3NzWl7ezugBUhHd7tdpVKpSE+IbDarpaWloMTF2CCTCwWNY9AFHMd8e3s7UArYyCVFAq6TNJCDcrl8ZhwKV0Fj84duyDykjxHcfee688z9Szp0MqjHaTabgSY4Pz+vlZUVZbPZ4IQwTxEEALWrVqvh3HyuWq0+dfof7+R4PFa5XD72QDROHztpoy6JPjClUunCqXY9qbGG5/P5E0VoU6lUqEOZJdbyIDs4OAj1n16Dxxx41gyp9EKhEFTynL7Me91qtSKNuBEgWV5eDv2o2NtQOmWPZv+s1Wqq1+szHehsNqsf+7Ef08c//nH97M/+bNJX5wSMuuZ4vWa/39fu7m7SRydmSaCT2IkYaItn4OK1MY5UxPvY+Ff883HzmpW4YICjIB7QeIGmNOUxcywCCmmK2HjdDCgUwZJn+dlEcaxp0OjXKimcw+kDOOCz+unMqtXBAcAB4VqgsJ1mJ+uz5ky6w0MW0hEcUB3GizniY0omFcfAa5E8GKK/gSv3pVKp0AuDQN9rybx+hjnrtMunYY5WUdB9nHbac4Dxd/RX0jNHWzppe5ianZO6DpAd1kgK44+6JtZOxG54t0CHnvXnTvBOEo93GpaD18Wy1/l7zhrntVDseayPIAWz1p65uTlduXJFe3t7iaDHCRmCKvFEwyw/IbEk0EnsBIymfVBFJIUCbBSvyDjNzc2F7s0gEb1eL9TpuGMWX7Rx8p2agAwni7akkLUi857L5UJgA1rCuR1N4hzj8ThQkVBRI5BYXFwMtRkgR8ViMdTvNJvNgKqUSqWIUAHZMwKcfr8f7pN6IEn3BIvSVGaa36NuViwWVSwWw/XQFfs0FsJZzTJP06jLaTQaoUaMGpnRaBQy/QQlTiVE5Y+eTFtbW7p9+7by+XxAJBcXF0O/phs3bqjZbGpjYyOcn/oxeoSACnmQlMvlQg3bcDgMtV5PUx2Ned5qtVQsFo9Uh3ta5zpNY4z7/X6g5kjSyspKUqtzhMUROBdMOQ1Lp9OqVCqhfjGTyahYLM4MWEA2ULzc29tTrVZTqVSK0I6fZSsWi1pcXFSj0YgEIr1eL9QL+h5bKpUiyK3vJSRpWAPZ91qtlnZ3dyM9q7D5+Xl9/OMfn9nXJbHjMRgfcRGIWTTsxJJAJ7ETMs8yscE4bcY5xHF0JV6bwIvsiBA1IK46wtcszjALeTyLHYfm2QTjWS+uF6fXg65Zha1Q08rlcqCvOULgdDuCKz9uXK3Lx9GRJ0fO4F87OuXI1EnbWXMooCDyDKHkMN4gLy5n6zVgPlf5PAFtnMpG0ANdhrGAiigp8sy8X5P3NCEYRmziSalD3MdRfRnOq5GQ8PE9a/PzrNrjFv8/LQMJlQ5bDRwcHKjZbN7zzvFvt9tVq9WKrNGzlDafVXNxmkwmE2ECpFKHDUKhdkuKoDVQciWFdQ20y/eYvb09dbtddbtd9Xo9ZTKZyP4G+vzqq69qOBzq+vXrCX3qGI29K77+J7U5sy0JdBI7dvNiebjx0iHXu9vtSlJkgYWGAARLhpl6ClRmMP6WfjLdbjeiWrW/v38PnWgwGIR6m1lS0859hSqBeZE46mp8BocxnhVPpVKq1+uan59Xo9GIIFHQDQimer1eWMi4lvF4HOmzg5MLAkCxaKfT0cHBgSqVihYWFoIsKKgRnzsNiwdrp22OtHW7XW1tbWk4HKpYLEaacnrTzsFgEGrBPBMKZ3ppaUnLy8vhHIPBQPv7+0FZj8wyz57ASprOs8XFRRUKhVDrxdzgneD8pVLpiZEdp+J549DzbiCdZL29ji2x+9tpKyeC6MzNzenNN98MyZtMJqNKpRIceZ4nPa3q9bqWlpaeukT7aRtrQy6XUz6fV7fbVb/fD2v/8vKy0um07ty5E/ZcKGmLi4sBDWM/hIlA8oc1b2trS+VyWXNzc1peXr5nj8tms/rkJz+pF154Qf/0n/7T0O8usadvzPWn0VPtIlgS6CR27AZy4w0Y+/1+BFEh+CDD5HU1fhyy3NJUPjPeD4OghsCIn3kjOBAVeK7IS3MMBAqOQj8cfXKKEplOz8JLhw4lTmmpVIrQ+bgPikm5HwI6JK19HKBhkIUn8OLvB4NBOK/LjBL4nTSFDKrXWSr6pUaHgJpxZIx8TAkIpOlz6na7IQCFH48z1W631e12I1REkB2oJNJUsQ0aJCgkAhuoJA0Gg5DB8+eLs0KQ/agOHHPJExHPes3CoxjPh6z2WQvGEzvaWOP5v6RQR9Lv94Oznk6nVa/XValUghDFeTJPuLC2sLcwtwlkvFl2fK47pdmDRRdh6XQ6gSrIWsYx5ufn9dJLL4XEQWLHZ0chOonNtmSUEjt2Q1mM3jNwpgl0WIjJbqMU5hQ3p43t7e1F6As4kfQjIUOFA7m/vx9qJjgWyBLBD84tG0an0wnOH4IC8doYNtpcLqdyuRycTQ+Q3PGku/SdO3ciqmleLOoiBKAB7mxLCpsOgaOjSgQ6nU4nUKK4Jvjqp4HozM/Pq1qtnikONxLbBDUgic1mM4w/z5jxdQrb9va2yuWystms6vW6VldXVa1WVa1W9c4776jRaKjX64XzoPqXSqXC/CJI4VkOBoPgSBAs7+/vhx43+Xw+JA3a7bYmk4nq9bpKpVLo9fQo5mqF0PQuUpZwOByq0+mEZ5rP55NA5xkx6uagbUlTVcmdnZ2AaC8tLen5558P79p5NBcaYE6zfoDUxNsNOPXZRX9cYppkoHT4ruzs7Gh/f1+VSkWLi4uRXi6ZTEYf//jHn1kVu2fJFhcXVSqVzu18ftqWBDqJHbshk0vAsb+/r2w2G1Etk6I1N3HhAY7jlCECHEcoCBb4LI6/qy1BPXN0yOWh/RwENXGExq8HNS0oQPv7+0dy/j1I8p4svuGgwOX1NWzakgKa0Ov1wrV5EORj5lRAHIPTymielgjCUYbAQ7vdVqvVCigjz8Tro8hcgpB5tpjAnf/3ej1tbGyEWiyXwfVMq3SI7rlwATQT5g8F8iBHIDgE6MxJmsYy/x094rMEWwhugF7h6DtidJ5qGO5nJEJ6vV6gP12E+36WjaQWiYjxeKxmsxmpLYE+XK/XI+jDeTYSJPl8Poht+B7Ie0/ijDpQ3nl+BxPClR7jdag+1hgiONB0Ezs+g3KYBDoPZ0mgk9ix28HBgdrttgaDgfL5fMickkECTYmr4MTlqAlovHgSWpw07UlCMb+LEBDkOHID8oITCXXJz+tFtwgGOLxPJn5vby+SUbvfpooTySbizj+IVnwDYRPjb3d3d8Nm5Hz0uPw0lDqOh8rcSWetyRCepWJJMp/NZjMUNXvw7UW+BBVx+ed0Oh2Qym63q3w+r3w+r9u3b2trayvSt4n6KwQ4ODafKRaLeumllzQajXTnzp3gONCziY7wCwsLIZO9sLAQgiuCeAIaUMZisahUKqV2u639/f2ApoKo9vt95fN5raysBGT1vNUxHGU4gKwvcUWqxM6e0RsGOXdJ2t3d1cLCQpjD+XxetVpNV65cuTBCEzQiLpVKIaHiiT0CHfYwlE9dbp0Eju/NBDoe7MxCx6Bno2iX2PFZJpNRtVo998H707Ik0EnsRMxVyjwjDYrh5miOO8ZknEBeqHNwmNwDIxcfwJHxPjUcU9I92W+OSTadjQOniACIfg7Uy8SzXHHDeSUwcnNEyVXopEO6XqvVCscYDAaRuhHuiev3AMc52YVCQYVC4cQ3fpyPs5SBItBgA2cuQu3gWeIAEAAjeuGS43QXJ3CgYBcjwOa83gPBM6vMV2RcPfAge42jRzFwr9cLXx7E48igwATlDfTC1QmZw/R5uigKbLlc7p6ESmJn30g8gEb4s6NmDjT0ojxThAbYO1i/PCHmsuD8S7INqjdNiqVoXxZvVL2xsaF0Oh0k9r3+NZfL6Xu/93tVr9f11a9+dabqaWJPZp5wS+zBlgQ6iR27OZ2KegRXDcOJozichprOGeY4BBcewFCr4w6bU8lAarx/DpkuR5EcjvcsPFktjuGyzdlsNvDCcQ4flGXBUXaRAIJAaAWYB2n9fj/S7JRrnpubC2PGdVH478dLp9MqFAqnUofgtVdnxXjeTkXDeB6SItlOguDJZBJUheIOlyTV63XVajX1+/0gzeoiGoVCIdLkkDnFM61Wq5IUybwS3Pd6PXU6nRD093o9tVotNRqNEKTMz8+r1+uFOcY7QCAGuikpXDtIJJSgeI+G82j5fD6oFp5mb5jEHt4Icnz9iwc61IlcpOdJoMNaxZoB68ADHZJxvj/wHkDD9cQKCRSQ6+3tbc3Pz4deLs58yOVy+sQnPqFqtarXX389CXSOwfwZJvZgSwKdxI7d+v2+3n33Xa2srEToZ2SJpGmGgg0MR8xRHX5HkOOOIoXcoCFStG8LARBBC7936J7NAPobtDj+lroZrg+n0+kvbK4EMvHFCHhfkpaWliKF6KBN8Zoiis4ZA66ZMZSmSmAu2DAajdRqtcJmNzc3p0wmE2pC4mpuF8VarVbYrBHJiNd4IRSAMyVNA1KXes5msyqXy+H5EbCMRqNA1wT98d5I1WpV2Ww2zBPva+Hm6CHCHcViMaB1g8EgSKWTxeadov6H+by9vR0J2j3Ypj4JGl4qlVK5XH7isXYa4Fk0Al0EGBIVo7NvrKG5XE7FYlGj0UjVajUkvRztuUhGU+idnZ2wHkFrJiBkfXFWAwwLEmCwBRDlabfb4TOwFiSF9cj3YemQYfGxj30sMCQSS+y07WKtBImdiu3t7enu3bva2dmJwOQ0S8QJcqfSVcRcKc0zUSyicQ5y/FggODicjigRODjaQ2AARYh6CjZPb7gGfYlzsui7gpub0+IqlUpwVulMz7VyrwgPcCwcVzYYR6bYzCSp2+0GmtLi4mJwbuFWX+RAp9fraXt7W9vb2yEYcSUi6sVA51zNTpoG3Ix7qVQKdTIEoBS5e3BNoEOwksvlwrNlns8yrotjE4CBJKLAR2CDalyhUFA2m9Xe3p4ODg60u7sbxAxAingPCHIIkp60P480fe+Ym2fRnkbT1cRO1hzFx5aWliRNawFnUYPPuyFEwPvsawoJE99PCYL4TDabDfRnSSEBA0rkx5EU9vF4MLOwsKBXX31V/X4/ebcSOxOWzMLETszS6XQomPSAB0csXrPj37M4w6cnWyVNG5K6YhpoDE6rZ+O9Czwdnh2ilxTQJKep4XDGFWtwPmnghYLWUdAySAyd2VutlgaDQQiqvOjdG4MhOcwXdUNs7ARzjgrhaHvPiWKxqO/7vu/TO++8o9dee+3IXkHn1cheIsFdLBYjwQjzqdvthgZ6o9FI29vbgTrpY8q4ej2UZ0wJfFKpVJAEbTabajabQYnwxRdffGAR78HBQeDgIxUOkoi0NPMylUqFYJfaNNALn0Nkx1EeI6D2IOVxKRJIVyeZ3cROyuIU5ItkXgcIBZX1zXu0ocZGEsdRnvn5eVUqlXCsUqkUoaWBCsEyqFQqGo/H4Rx8plarhUReYomdtiWBTmInZs4b9myTF/n7F8EFn3MOPY6YFOUZs7Dy+3gti9cseMZemsof40TSV8TrJxxRAuHJZDJBzczVaY4yHD+QFQIsb5jq54OSVK/Xtbe3FzL50OfYwKi/wKn2zJ5fey6X0wc+8AGNRiN95StfOXOyz8dtOEKMM0GpjxW0yvF4HGpy6DeDNLqjaMwx78vjNWDMBwLsdrsdkXTN5XIPVMPjPQEtckQUlIaAXlKEpoYTk0qlAmrjNW+YzxOQxcc1kg0E9hepXiKx0zNHHS6SQaGWphRc9kASFvxcUoQh4clC6NcHBweBqsb+wn7c7/clKTAGqHWTpogbYhCJJXbadvFWg8RO3eK1M9DQcNQIThz1ifcxQf3Fs1E0RqPoHd69B09kvnEUh8NhJFDy4CIuiczvkJnmXFwr3bjb7bba7fZ9lavm5ub03HPPqVwuq9lsKpVKhUah3gDVRRfYoLxfD+MCFY0Ajw2KongPvJaWlvRjP/Zj+vrXv66NjQ3duXNHb7zxxoUJdrxzuKRIP5yFhQUVCoUICoGQAHOCOZjNZkMQ5MjH3NxcUDbje+Yi6Ao0ROoMkJG+XzBQKpV09epV7e7uanNzMwQ43ifDaY/MI29GivPDHEfMgABtOByq3+8Hbj7iFY9inNMbFCaWWGLHa91uN9CfYQCwPkgKiT1aOzhSw94L4sMa5YEKe+5gMAhJovslMBYXF/XCCy8olUrpzp07Z6q1QGIXy5JAJ7ETM1fGcUqayyS6U0QmGsfN/9Z/TxDg0rpO1fJjs9iDokADitPlCAxwFjmP3wsBh9fsUBiO03xUoAONCXWrvb29oMbW7/cjgQ2BHffrstluIEzUCuFIex8d6RDR+chHPqLxeKyXX35Z4/FYb7755oVxSAkMZgWxBCBQEkejUah58UZ6BC9xpTYPyGfVi/EM/VwPq9YHAogqGvPWa9S8H5WLW/B+xEUToDe6GIcnAR6nw7krFSb2bNosUYxZPzurdhERRBBeaSqdH08Ysm/52uTtHPwZs46RNOFYrBGzxFPc5ufnVa/X1W63dffu3eO9+cQSu48lgU5iJ2bpdDpSCB+vwYFTjAPp9TROw4Ke5bKY3tyxUqlImjb4nJubC3Qv6Ef8vdOOJIXrYgH3omwXA2AThQrgmXOUvB7kJELlq1arIUPmgRfH7Ha74W+gBHDf1CGROc9kMsG5JeCJNyDFXnjhBf1P/9P/pM985jP69//+31+YjFuv11Oj0dCdO3fUarVCIO3oGRQvKIVex0RACV2D+YNKnqsREQBzDpAd5gqSzo1GI3DiH+SgVSoVLSws6NatW7p9+3ZE2Y2MrKsY/n/s/VmMpemW3oU/e8ew5ynmnGrI6upz6rjLbh8w9qEty5KtQ5sWEhJ3SAw3IKwGCZCQBTISg8ASV9xxgwQX4AskQC0sC2GLvxuB3XS7u+kz+dSpU1NmZWaMex4jduz9vwj91n6+LyOzIrNyzr2kUGZG7v0N77ie9TxrvSRuU6yD/DPPn2G8w/wg23uaMTGbzdRut5XNZoNN9PLcS3u1jfnBfGC9Ho1GibOjNjY2ngoIP2+jBDKSqrfFNjc3ValUop/ov729PWWzWXU6nZBk+2Gik8kkmFsKpQwGA7Xb7URuqstcOdet2WxG0C4d1KtWq/rhD3+on/70p/r888/fulzQpb06tgQ6S3vh5sBCWpxTQl4DtLkvsq4tTkfiPQmS73ikyXNwcET5rkf+3Mn1ss2Y5/nwGT+A1FmXqzqIaKIBcJ4jctmBjhRPABSRfOrvwLumo3ZpK5VK+uijj/TFF18Ee/Emg520PGM8Hms4HAawhvmiDb3kN/3i7ewlWz36mR6zl8m3/FmQnV01l4VEa6oo8T4AXs+zkZKR2XQODvdjLBO9pbAFP2lG9XHPyDyQlMh/ehuj7K+DeX+RnzgcDmMM02/MGSmZF/mqmM9tckjSbPabbF4ZlH6j0ubq6qp6vZ4kJfYoZ2dYOzx3x/cjl+jCIKNeuGwsIF1rtVpvTR8s7dW0JdBZ2guzs7Mz9Xo9nZ+fR0le8mxI/KccLiWgYWOkhYNFLgOLMM4mDtr+/n5iYU5T9V5cwM1Lb47H45DNra2tqdFoRG6Pb6S813g8DhlSNptVrVa70uGYfDabzerevXvBxOB4wkSlD1dlkxkOh7HREDWXFM77/v6+BoOB9vb2QsqAodfe3NzUr/7qr+ro6Ej37t371v38qtpgMIjcE5z3XC6nUqmk9fV1lUqlRLllzp0BiMKETKfTGHNUPqtUKsGcMWZxNMiJkRTMDSwlrByH9F1FPggTWCwWVa1Wg8nJ5XJR5ILS4efn52q1WvHMa2trqlarURGNSm48Zy6X03w+1/HxsYbDofL5fFT+Q/O/uroaf7/MVldXtbe3F8+alsQs7dWywWCgBw8eJOS/khLOsKTIJ+Nw4lfJeZ3NZur1evrFL34RoJqKYPl8/o1mdgAq/X4/pNOZzMV5OKPRSK1WKw6jXl1djf2CPNZyuRw5nzC65OkMh8OQUgOC2DtZv9iPRqNRsMgEjr773e/G2ra0pb0sWwKdpb0ww3mXlIgqpyuDeU6NR8NZRD2nR1Ii0sw13fHHyXOZEX+mJUlEptKMiOdYpA939FwfnjP9jI8y5GVIDbiGv7szRelouzsjfN4rw1GgYTKZBBNAG/LOhUJBOzs7Ea1908wr6Q2Hw0TpcNrEC1Y4QKFcM59xWSHjAGBO5cD0ff3f6OipkkZ/cq2rGk6HR6wZd+T7cD1kctLiTB6uIS1Kqfu1/Vwdr05Ifg/sU7oyojtEkmJe8L3X3dLM3ptiziwSVJGSuS6et/ay28DPPWNedTodnZycxLOurKyo3+8rk1kc5Pym9RtrCIEv1nA/Q85Zf2d3fT/1/e2yap7OUANyfJ/2Iie+D1arVZXL5Teu3V9FWwaTHm1LoLO0F2YskCTcUz0KZ51DFCnpTIUXjxK5w0m+jbQo2cw5AEgYWKgBK0S9vfoV33dHljNNarVaAAaXqLHo4xjU63WVSiWVy2Wtra1FZZpvKs+bzWZVrVbj7AEiaWinvYDC+vp6JMcTxaOYAdIlB2Gc0XJ+fq779+8Hy5PL5bSzsxPtubu7q9/8zd/U7//+7+uTTz554xbM8Xisfr+vk5MTHR8fRxvD0u3v76vT6ajb7SYKOuAs+dlEkiLnhHN4hsNhJNwWi0VVKpWIejMuPR+Hs5ay2WycdcNYfZK254yL0Wik8XgcgIOTzRlHjGnYwcFgEO8JoCkWi9rd3dX6+rry+XwCgI1GI3W73YSUj4pt165dUz6fV6/X02w2i4p1XJtoLvPuWRxE+jKM/u/3+1pdXVW1Wn3Zj/TMrFgs6vbt2w9JYS+TDxOYIVDwMuz09FQ//elPNRgMQjp1fHwc43N9fV2VSkWDwUCHh4e6ceOGbt++Hezkm2IEV9rttnq9XuyTsMXkflJEALt+/brOz89jTo/H48TxBb6uraysqFarxf4CkCInZzgcanV1NXLx0mfu1Ot1VavVJaPznC0dfF1a0pZAZ2kv1DwPB2bC/8/PJ/FIcboyjLMcmJ8946UvL6uYls43SP9OUiLSxQLCM/n1PCoGYHqSsrpsEGzCnqc0n8/DKYaB8gUNAOd6emeleHY2L4DRxsZGtFWhUNC7776r+/fva2trS8PhMFEA4XU3quGRtI8TT8U0xqFX2nNA7YyjtBgfntPkzAXA2oG5yyydVXvUWL6KIR3DgQPAcV/X3buE7PT0NHF/zy0CmHn+mjM/gGlknYA3WCM/QNXzm5gXfP51POcknYP3pjAEAFFKDksKtu4yMPOymTmkxcPhUMPhUOPxOJgcmBvGmTObb4KlC9UgnUWKKyn2PvrPcz6dbU0zsV7Uh2v53sae5ioCAnqsNVyLPzmvbGnP35Yg59H2+u02S3ttjYgoVDsOEIshCy8LJrpqcglc3oVT6Qu/O5LkFZBP43IvZDyeeMlCzSbO83GWDRvG+vp6lON1R8clH09TXWZlZSXyHniX9fV1DQYD5fP5eA8/K8Wjk7yHAyCc7LOzM3399dfxvpVKRfV6Pars7O7u6oc//GHkVfzhH/6h/n//v//fU/byq2eTyUStVivAHoCHfDFydjY3N6PEN/p0xmo2m1WlUpG0kEbi2Nfr9cQYOT8/V7FYVLFYDCfEK+pJSlQvwol40nHDvNnc3NT5+bl+/vOf6/DwUNvb28rn85FnxkG2HPz34MGDYJsymUywlp1OR5VKRaVSKcZXPp9XoVBQv9+P83YYg6urqxoOh5IUlRBxuLhnoVDQbDYL2eDR0ZEqlUqMtdfFyIuiyhiO9FXy8F4Xy2azr8X7IEU7OztL7A2wpfP5XO12W7VaTdvb26rX61eqZviqm5+VxV7a7/cT5eqdOQGMM2ZhvQB+fmAyUtT19XUVCgX1er0EqGHsw/ICslBiXLt2LZGjKC2AzusY1HjdzJUIS3vYliNwaS/MiKx7oQAWwssWaq8Sxe+JTHkOSzovwi0drZaUiJw7Y5MGCenPOshKMzYe5Xqa6lIUWVhdXQ3pEc+Mk+zykXSU3p/Zr8m7UVoYB3cwGCiTycTp1eVyWTs7O/roo480GAx07969h4pF8L6u36b6zqtWgQlLs4Dkozj7gsNP+/F7/3F2Rlowkw5wXC5Gu14W9fd+caBMHz2J4cggY+PZ0tFVf18peeYPli7Z7vOOIhwu4/PxxnvzXT5HZTiX0bEOOPvzOhiBAl8nXjVLz1Fn5yQ9tEa8rubz0OXLFA4BbJfL5aj897obBVGcsffcTK/ISZuk553n6vmaxjz1YJkHHpFqS0kVBHOZdfKy9cv3+TeJXXsZ5nu/Gwznsn0vt9dnl1naa2+np6c6OjpKHGZJvszZ2VlEmNKRCa96RQ4CCziacUmxEJPL0u/3w6nlup7Mz9/Tjq60OKQzLUfykrtpcISGGWboSTbXtbU17e3taTQa6d69exE1I8HdGRoHQbQB93NZFUZ+Bp89OzvTnTt3VK/XI1+kWCzq/fff1/Xr1/WDH/xAf/Wv/lV9+eWX+uKLL4KVYFM9OTnReDxWq9XSeDzWV199lahC96qYsyREesllIVcsk8moXq+Hwz0ej9Vut0MGlgYNa2trKpVKCYDHD/1Plbz19fUYs4wZKSkZYXwRbX0awLi1taV6va6vv/5a3W5XkqKyWzabjQII5NHAPJbL5ai+5kUweEbYS5iYVqsVeV5UPmQeN5vNkP/BknmxD+Z3vV7XdDrVvXv3VKvVtLW19bTd+8LN15pX1XBmj4+PNZ1OtbW1FWusdFFS/lUEaE9iXsSDdQlJ7t7eXuwf9Xo9csheZyOwRnVG5hzStMFgoMFgEBXUcrlcjAOqprH2+Tlfm5ubsRYQpGGtJ5DhRVmcOaKC52g0Cna8UChEXpAb92LvWNrTmzNwbnfv3tXdu3fVbDZf0pO92rYEOkt7YTadTtXr9ZTJZFQqlR6KtqerpaXzY6SHIxpE7vgc+RD+bxZsz1/hWunveV6CR7SJdPGMafYH5xnnzitdXcX8OkTPcIIdaLjczrXXPFP6wFMO+ENmwPe63a7m87mq1WqwDxyCubW1pffeey9KLvs5C7PZTN1uNxL8x+Ox3nvvvUhS5XmogkTlne9973svPKo6nU5Dx8+GPx6Ptb6+npBXsJk74PXoIxsL44X3WF9fTzBbgIp0X3qejDMf0gKQApAAI4zpqxjPSyEBB8QOkBlTODcUquA0dR8z/N3PyvA24r78rK2tJUAhjhEgitLVhUIh2sArNb0JEfdnbb6OpY0+dcaRUuVE2Sn6ks4bIw/rdTTWcM+5m80WJaSr1WoUgSmVSsrn86+FHM8trVqQFnsVclXm8GV7JP9fKpVCMsre4XmcnrNDsIzgkDM0fJ5ABuuKV217nPSW4h2DwUDNZnOZS/ItbH19XY1G46H9odPp6P79+69kwPFVsCXQWdoLs+FwqDt37ujWrVu6ceOGer1eQhrV6/V0enoaTI9LYQA0OFXSIsK6s7MTThURaBbeXC6nYrEYzi7Mjju1aekbzqDT+zipnDmC9A6nIZ/Ph/N4dnamo6OjJzqZ23OOkBHgZDabzXAcc7mcqtVqbEacDYMVi8WQLsxmF2dLTKfTWADX19ej+lY+n1e73dbGxoY++OADlctlNRoNbW1taWNjQx9//PGlDENayuafmU6najabajab+kf/6B+pXC7rL/yFv6B6vf7CHY7hcKj79++r2Wzq+Pg4KqtR3U+6cBjJ1Wk0GglwR07Y1tZWMD4OhPzATv8/xh1AyJO8Pb9FUjBH/Pn555+r0Wjo9u3bV0765r7lcln1ej2uhaXlS8ViMaKvPLv3Jyeir6+vh0MJi+qlqGmD1dVVVSoVra+vq9lsBiA6Pz9PjN20nI8KT/l8/rWPuj9rI4LPupBeoxhfvV5P3W5XtVpNxWJR3W5Xw+FQzWYz2GDkdtlsNsrMU3HwdTOAebPZVLvdjhzKra0tbW5u6p133lE+nw/m6nV8x+l0qna7HblHMKi5XE6bm5vxXuPxOIAcawWy2fX1dd2+fVsbGxv65JNPdHZ2FkoHwGG/308AJ9ZEKkEOBoOHJHCsLeR3FovFKGThBSDccrmc3nvvPc1mM927d28JdL6FNRoNffTRR9rZ2Un8/ssvv9Tv//7v6/j4+CU92attS6CztBdmbFDb29uRqI2zKCkiVLASLJjpCmzOoPhBZxQd4HNcxwsRYJflKHgekP8eYDOfL0qq4jy6VMzZHJ7lqkYeDT84lDgqtBNsjsuqXFLAM8HC4OBQjQjJAUCo3+9rZWUlpFOUB75sw7qKwV6sr6/rO9/5jgqFQiTHvyjJDMCu3+/H5lur1UJaRlTTI5z0G393+SFgBraGvkbG5FXFGJdEZWFDkHjgZKSZEdq62+0Gm/Skjlp6TDMWGDM4JOmorY8lfk8lJc8dQs7JZyaTibrdbrS3z73LGE/exfOZvIgBwYKlJc8cw2h/+oq+8/7zMTmdTuMQXCmZHP685+J8Pg822dfrZ2XOYDMX+XHH/3U0Sj/Tv7wXazl9BzgpFovBmPr5XLQRaznSNfZB1gpyQjlSIW0+5nxf5hl8L2Q8pj/zphSEeNkGU5eW0E4mEw0Gg6cqhPQ22BLoLO2FWb/f16effqq9vT1tbm5qPp9rMBiEUw4bgfyCxdOj4efn51HDH8et2+0G80ElJKrHIF+CxSCaXiqVYlNwS+cqAFZgWwBi0PVsKKPRKDYWKuFcu3btym3DNTiTxcELGwQAhaR6l5Qh18CJGQwGGo/HqlQqoV/nT2d6ms2mBoOBRqORrl+/rmq1+q1OEs9mL84eqlarun79evzuRdpwONTXX3+t8XiswWCgarWqGzduBEuIQ+9lVBlHXiKcCCdRTPJfGGOcKE4SsJ9jQY4YEfZOp6PJZJLIcfE8LnJeHjx4oNPTU92+fVuSrpwT4iBLWoB1+lxSgC2AC0yWgw1AOqemuwO9vr4eBQn6/b7Oz8/11VdfJaSUftCktKgut7a2Fg6oF2Cgalsmk9Hu7q42NjaeuL9f1eIAXijkaZ7NAzsuNfTzjQiI0E+1Wu0hJ59140W2D2yepACwVC38NubMYzabDWahUqmoUqmoXC6/1iBHupgX5Gmurq6qXC6rWCwG0MCKxWJIVSlIQn4Oyemj0SjmXrvdDsmxdNF+AJ96va6dnZ2ENM2l3UjXOKPHc3X8d0iYPRcsn8/r1q1b6nQ6r9wcfd0sn89rZ2dHpVIp8XvW0mUxgsttCXSW9sIMqZWXifYkd6IRXkWHKJFrlcmrAFykzw9JV8+i5C0SGc6lSW+a0uKUeMr++qaZBkEuWwIAce3LFvQ0qPLP4JTi/J6fn4dciLZxyRgOp7NMMDQ4Nfl8/qGzJIj6l8vlcJay2WwslM1mM8pZO7uGE/skcqqXtal5hSHaCjB8mVTL2bo0o+iRddoMoOBVj7zgAFr21dXVcAKQEQF0GCMwbAAEyl9//fXXqtfrunHjxpXbEakk4537+DjxKkwAcmcGOHCXeZSei4BpnNf0+TnOINAPUlLWx/95wYPxeByHi/K+6YAH0jdpUYHK5+Lp6WnkZvD83p8vw552HviY4N/86ewhTqzngl12vxf9/s6MSoo8mict0vIoy+VyiSqFacb+dTTk1+PxOBxZDtAuFAoPSX8ZA8g+/XyctDTbWdX0jzPazGfWJgfRkiLIx+8pdMAaAsPjBqNTrVaXQOdbGtL1tNQXcLtkdC63JdBZ2guz09NTtVotjUajWCzRoXtBAinp+MM+EDmXFhF4HDQvSuARY3ItyNWpVCoqFouRSO95QND4fjo4TgTOG7Ikj6SSpwOL5DlAbl7sgHti3Ov8/DyqJMEgALzYVGg3pCGeFN9sNjWZTLSzsxOOhbSI5tdqNa2vr6tWq0XOEbkSnU5HX3/9dSSNcxYM/VCpVJ4oSf5lGU4wmy/MSqfTCfmZtBhjbOSwYjhjyAWpILa5uRmVAyWFE46TQMEHxuPa2ppqtVqwMv1+PwEWs9lsRD5J5qcvfvzjH+v69eu6du3alcFlqVRSo9GQpMh9w3nyPCHmzWAwiOdk7nH2COCDvie3CFYGQMM8Ql5D1SdYBG8T5g3tXSwWVSqVIh+AOY1jdnJyEqezr6ysRAUtrsd7INVst9sqlUp65513Yj6Sr/Go4MPztG9zP9Yz5jXgIF24IS0nelXyHxhPSBwJMqXP/3qa62YyGZXLZUkKZnEwGESBm9fVzs/PdXJyovPzc21tbWl9fV31ej0Y5LQBasvlssrlcgS/0vuPA34HxkjOGGNIfRlvhUIhEQRyAMPe2mq1tLq6qhs3biQYbrdcLqdbt27p/v37S6DzLa1QKGh3dzfGP4Z0bcnoXG5LoLO0F2awL0SN2KTTTEk62R1HEKCD88SfTrfzec9fwVHCmRyPx7FQ8x2vSuQ5Ezwn/89zeRUbfzYSsfkOeUjpCHl6wU8DHa7hTvDKyspDeR78pDX6tInr5JFuZbNZ3bhxI+QlvV4vDoQ8ODhQoVAIuRzSEJgI2grH+VVJ9gUM9vt9nZyc6PDw8KE8FC/v7PlYjI10nzC+XH+eZmxg3pzdkxYHgTrTSP5TJpPRcDhMVGjjGekvpGVfffWVKpWKtre3r9QGzIvxeBx9Rxsw7xwsSIpxT8Iz7+rP4vOE3AhJ4dw0Gg3l83k1Go1E5T8c3VarFVXAYJ5IhEYmR58wvvzUd+7l/YkmnX5BitdsNoNdzeVyAfRms1ncEzCL7O5VOWyPcQxYlxRzjTWG8UJulMtmmacA0fR7uRzuedrKykoUTeH5n4QRvszm83nIexmTBJ58zX5djfWW+cXPN/WV55BKiznKNVn3KebD3uXMq38vfT/P80OBQXsTQGu1WrE3p8sfIzF8HYJkr7pRGTXNmrVaLd29ezekwEtL2hLoLO2FWdp5eZTUgs0RB5J8GvIYWq1WMBzkUqSBDg4D0U4cHy8WQPTLCx2wuZCf0Ol0EuVZnerns2traxF9xcFFrjQajR57eCQG0AGswCwBMrxYAYwYSbgAITYzl+vgrNdqtdjw1tbW9MEHH0QVnMPDQz148ED9fj9Ort/Y2IgqP/l8PhFBov9cSvQyjf7gDKLj42PdvXtX0uJ8o9PTU+Xz+XCoPV+HTdyj5pKCQfNN3s/uoI/7/X7CKYEZxAmXpM3NzUj2ZVwBggGqnPTOGGm1WvrZz36mGzduaGtr6xsdcW+H4XCoarUamv1MJhO5XzhTAAqYmmq1Gs4iTjRMDm1HQjMb7fb2tqrValS7usx6vZ6Oj481Go3UarVUrVbVaDRibOfz+YgeS4vDU6n21ul0NJ1OE1FrAKaXUibXDMfMWR0YN6LfvA8sA0zsy7bZbBZ5i1RK9MIX5B8iNSUPA0kwxQauXbsWbKyPG8bjiwA65GGenp7GnPs2BvgHuLq8zxmK19VgzZG4XtU8V1RaSLalxXlY0gXjy14hKVHq3dcD9jiXizOXnLGWFPPx8PAwpOmAbfqCqozFYvG17p9XwZDmphm+/f19ffLJJy/pqV59e/kr+9LeSiOS7ounsxXSomoa0WXXeJOkKS3OlJhMJgFG2AhxYHEgfAEn34DIO+b6f2eDzs/PE4croo/GCSZqTFQsvQFhbCyXOVZIBk5PT2Oz8/MiuCf3Go/HAWZwyomcAvJcj01SuUfceR6M6Hiz2QxtuAMdHHMOifTnBiTS9lLyJO1naThRk8lEx8fHkfQPWCDKiWyR7wAI0tFm2sKdJ49w0g8Obhl3tK9LdGgn7uuHkDLecMydpfN+QBry5ZdfJgpOEMmmoAfFOPr9vkajUbA6tJEfkEsOjzMEmUwm8rVWV1cD5EiLykq8Mzle+Xxem5ubqlar3wgSmEsUx+j3+wFAJQX48rwnxhPyPqrREc1HgkPVKKSG7XY7UYzE5/xgMAg2jSqDuVwu/qQMus8Jd0AlxTqAQ+nljJ/G6Jderxfsl+dMFQoFTSaTkD7m8/kAOPQ38x9GeTAYxLNTcYvnzWazkbT/vMEd/fIs5n4mk1G1WtX6+rparVYil3J7e1u1Wu21dqSftq2Ys6wHnU4nzuTyYj/sGexNvseR+0FwgTl2GfvNz3w+V61WC6YUphoZq+c6cl7b0r6dIWdcsmNPZkugs7QXbmzKVIQC4LicjCjSyspKgIvLSi5Li9PA3VGTFsmaSMr8ADSSxInwsxHgZOGAsaiTS9DpdAKk4Nimcw9wjnEs0rpx7nVZHs/KykqU3uZMHi9KsLKyEsUCYKccTBHFdyfeNy02IfrBy1B7RJ2ypiSSutGmJycniUpksEBUfkonxD5raQlAr9fr6Ysvvoh3oYS0y3mq1WpEwD3y6WMB6RPOgAMd7oXziSPNOObzhUIh3t+j5kiMHOyQzAsIX1lZUb1ej++dnp7GM1EAgMpKMIiz2UxHR0eRf+RMDNf2IgnkrLkckWACDEI2m41rEMkFXDUajYgMr62taWtr60rVtBiL6+vrkQ8wGAxifDUajUTAgyqM5+fn0Z7Hx8fB0hL19jkEWAXoeGSb/kCiydit1WoqFAqR4Hv79u2Y68wJzlZytov2Ho/HcYaN9OR5Oawd4/FY+/v78WwwjawvBHtgqDj8FoaDAAb9NxgMAgyurKwkxocD1xfBYj2reQ/QoSrk+fl55M9Riep1BjqSnqo/0mAEUNztdmNd8Tw5DKaNeVmr1WI8ssbz/3zXg2eML0kJoENuItdwoPO698/Ltlwud+mBoUt7vC2BztJeuLEwEyHGuQE4eNQok8mEI+XSBF+wcdaIxLskjoUZ9sgLCBCVcj2zR8acicFhZ0FHMuZVobg318N55L6AqsclRtMGOKEkGbJRuQyPCDY/khIV5XAMfHPC0f/JT34iSZHnsLW1pcFgoFarFffCIZ9MJuFQuBNM8vm9e/c0mUzUbre1tramjY2NhJNPuyHd8uRYpDjpPqV9KJkJyKAt+T2J7O12O5xDKVmWeXV1NVGcwHNBkGEBhgG45Hm55Mbzrhx443AjC2m32w+NB5eLoG33/K8088bnYRu73W6iIEAul3sIOCH5or1d4gVLhzQSJ5fy67wX7EahUAi2EtCOHI7numr0GQaId0LuAlgsl8uqVCoJME8BAW935pTLr9J9RN/jdHlhCMpjU4ShVCpFiV7AHqwYc85ZUOki+Z28I1+3YIRcvuc5gjB/fMf7lPZHend4eBigl/dkDo9Go6gg6H3v+RUwcl7c5ejoKNhIJJc8F6yZPxtzgwIVyBq9suTLMuY/77h0npPGePCAiue9+rhmX8zlcsEMM2cIBObz+Sha4+uZ72P8yb7q4Iv5vHTOv73BOi/b8slsCXSW9lKMDZYF1jdhoo0uHQFMuDPsESZJ4fjjfPAdFm0iU+kSjA50uIdT/OnETpgonGGege95cicSIp4PJ/9RGzQgikjsfD6PXCWiy5eVsHZJlrTQ4BOJBrwQCR6Px8ESOJMwHA4jj4pNcjKZaDgchsyPTQ/g2ev14owEnKyVlZUEK1YoFLS1tZXoX5xCB6CYy8U4W2g6nQaLgmyOBOVutxuSHK7pMkbf+Gl7By30l0sEcZBx6AG1vBdOpCfVe7K06965TqVSUT6ff+jQPgc5jCsf00i9vIQrRSRKpVJcExkdzqykAEQkqpLMSnEL2pb2RAMOYJAUzrWPr6s6uyQjp0E5siyq+znQAZDBRvDsLrlhrtKGDvZhtpBz8jmADiwNQQXmDlXfyM1zJm02m+nw8DCe3XP1YFDSQIfEb+YDTNvp6anu378f+TW892Qy0cnJSRRsYF7w7/F4rHa7nSiKQcCFceaBAf48ODjQ2dlZFCmg0ARno2xubkaOGHJQ/rxx44YkRWn+NFv5og3Wgv5dWtJcmQATk24nxqpLjdOyNUAtDGJ6/fRrMcZ9LeMzMN3fptre0i5sdXU1VBtLu7otgc7SXrgNh0Pt7+/HYojTi7OFk+InuuP8IDWSFlF3nFUWWxxwj9xLCslZukwmEXWPTrlEDucWp0VS5HLgVAFmJIVTCYiAagaQsTHwXJfJFdCfA26Q9jkDQG6HV6Ly/AW01ziLfj8HasViUXt7exEp6nQ6Ojo60mw2U6vV0nQ61eHhYUL2x7sAnogCnp2dhVPVarWUyWTCocSBI+8HhxsmBIdubW0tDo89OjoKoIODS0SXtqGSWFp+RUK7jxUkQDiWgBjazDds3hGGxtkixggMHG1BP2Bs+oAHIvGcYcO4gHlDRtnv9+OZAEl+SCzgl0R+8jZ83FAsAkc/LcGDqXHGY3V1NUAtYBaZGwxsLpfTxsbGpSd0X2bT6TROegeMAVyoIsTvAQNe9IEfHxe0tbMZ6XnjrDDrhAMLacG4UaaYfCPmFM7fV199FWsXgBU2jPfBOaRPfG2h7WH8eE9ArCTV6/VYn+bzudrtdmKutlqtOAQXQE2SN0YfIlEDxMFuMSYBCfQxbcI650w1Zb4ZhxsbGwFcs9lslEJ+Uba6uqqbN29qOBzGPGHeP4sS1q+zsWakDyTm/7LZbBzpQPl41gaCH8ViMSSgjDPYdNZAPz8LIENVN67tQTnPG13at7PL8n6X9nhbAp2lvXA7OztTp9MJmQz0uTs1OLQOSvx3OJo4JFJSEudgwlkcl0r5xs6mkI584ezidPmzSAqnCykIDo1Xl2Pz8eR8Z1/SG0Ba9gQQILqLgyEp7o1Txfdx4nH2aCeeEaeeyl+UB+be5IaQ7Ez0fzabaXt7OxK9kbs4m9Xr9TQej3X//n1ls1k1Go1g7pC7obNn88W54hwWmI92u63hcKhutxtgj/7MZBaHSAJY3OE8PT0Npsd/iKhXKpXE+KKvPTI5m83iIEvXrNM/jDeX/aSlk7B0+Xxe/X4/cSYSoBFnlTaEmeGejFGP1AJC+KHvfCNMvztjnui/M5K8B2MpLYdiniJ18iptjzNykZgTMGn0Ee2GjAswR3I+7+aldj267PORNSHNkvF7WArelTWFZyMnhvenwAkBBJgm8ug44JHnPj09VbVaVbVajXfzcZPJZHR8fKzJZBLyE3LKvL/Ozy/KZzM+mR/M9X6/H06+y3lxPhkvrAn0uUfZWacoVuDrC0GHXC6nfr8fcwAgDsOVzWZVrVZfaMWzbDarzc3NKKjA2HG29W0FOgB8L6GOed+zXrmMm88SWENpgcyUPUNa5MVyXUCwz890Xo9XpVwycU9nadXI0q5mS6CztBdu4/FYh4eH2tvbU7lcDseTRZqyqL5Iu3zM9caZTCYcBpwu/yybtW/gnq9BRNvzAVzq1G63E5WevPIbm4En83PPYrGo7e1tbW9vq1wuJ95lNBqF04IDl3YY19fXtb29HfkEONc4zDw7kXDui4OLU3ZyciJJ4ahsb2+rVCrpxo0bIUuggAD5CkT8qVDFRggTMxgMNB6Pg8nh+clzwLna3t5WJrM43M8Tw3ES3WnP5XLh4D948CD6cj6fq9VqhdxmZeWiCpbnUxGZ5FmR6EiLcsVedQqH9vT0VMfHx9HumUwmDuljTOLI1ev1BDvh44IxR26Vn/9Cn9NXDkhxzNyh9fwMHAj6FmbLy2X3+/2QHZKPA4iifaWLQhXI+9IMl+v3eW7Om+JMHiRcjCEY10cZ7BHAgopisB4425SdbrVaiVwkpHIcfgtQAGTgnNOXfJ5CGoBmj/Ijo+P/2u22jo+Pw5GD2XHZ7Hx+UcmKOXB2dhZSQj/El+h3p9NRsViMPt/e3g4GyJmdZrOZyL1rt9vBSmaz2RjjANLxeByJ3kTtq9WqSqVS9DtjXVKwcy6hcwDP2klQwvvFZbuwWoDLg4MDZTIZbWxsRMCjVCqpUqkkDrl8XkYf+rlq5CBRWTN9oOLbYOyjFNqA8XbpM4Ez1kYP1LRaLUmLAhkEWljPvLofc8SDi+S4nZ2dqVKpPOSM7+zs6Ld+67f0+eef6w//8A+XzvpT2BLoPJ0tgc7SXrgRad/c3Aznw2UVgBM2VkmJjZrfOavieTLuSAISHIw42EnnuuCE4BSTrM99nJlxRoj7eXS8WCw+xBrwfDglj4puETWfz+chK+FcHU+OB5SlpQFcFzAC44SDRLUpyv2Sw+SACekO4IG+AgDQNsh90v0DYKUvKQNMwrxLlEqlUuKdSFiH8SMviLZls3bZIu/uhQEc0ND3XojCpXfetj7mqFSHg93r9RKMnDv7gGBYBcyBdVrj7kwhbYc8juvRn547QT97QrpLudyxlhaFEHBCkI8hC3WJpz8r0f35fFHB7ioJ6bQ7z8F4RNrlrOx4PFa/34/8E2dqCUZwT8Ya70Vf4+DTJ0gjyS2h8AlgKZfLqdlsajAYxGd6vV6wTdzLmQ/AI+PTJXaeS0Zu3unpaUj8uCbz3QMF0uJ8Kua5z0naCYkh/YZs0Zk+/zuyI+7tgRAPoPR6vZC7sh6wXlIVjjHc7/cDVFIFDcAH+HqeQIc+p218PaZP3kZjHg2Hw0R5ceawtGB8GIOAGcYsvwMIO8NIkMYZaz5PgAow7rl4WLlc1kcffaTT01P90R/90dJZf0pjXi7t6rYEOkt74dbpdPTzn/88HCbPQWCxxnnAIZvNZgnnQ1I4rVRocumFtKDqcfw8Kd9lOUTDcXb5zmw2CzYGWZdH3rmO5zCwyA8Gg5DnbW5uJkpek3jtzFPaPFfEIzgwKc6iuNQNc4BIO3HGS6lUUqPRCBkS3+/3+/r66691cnKi+XyuSqUSZULZ9DKZTGx4zip5Tos7UfSLMx84c6PRSIVCIdiB9fV1HR4eqt1ux7vBVnW7XZ2fn6vRaIQT5hsuzrk7oPQXkc5SqRSlgx3k0mc4TGzagEei3C6rgpHzPkDed3p6qmKxqI2NjQRYlxasoxd7IB+D50fzzvlFzAnGsFdNczYSp9wlaS5X8hLT0+k0CkjAUjJGOWBzPB5HYYxGo6FGo6F6vR4A9psMp9jZ2nK5HExpuVzW2tpatFm/34+DVD1Ysb+/r06no93dXa2trQXDwHVx9P1A1M3NTX3wwQdR1Q07OjoK+SLSHJczUgGNA3bJwaOv6TPOP2LdwtEHXDHPS6VSMKOAHQds0gJYerEJnFGeCaaGIBFjmXsw9vwgZDfGO2NkPp/H31dXV4OFcRbQ53W5XI55DNBGctdqtaLICeMWwPM85WzM3Xw+r2q1mpAivo0GY0wpe1g45omfb4Y0lKDQ+vq6tra2AvAwr6TFPusBMGnB/DAmCAgxvtKBkHq9rn/mn/lnIsCytCc38pddLbC0b7Yl0FnaC7fxeKzj4+Oo8e/OGpFLp8fT+QJsZAATZ2rcqcYBdmfPo1ueQwFoSZfM9ZwEfu/OK38SCfUkfRwCj9jzWZyOdHT/ss+5ubTosoh6mp1yCY7nLCAFcoPdIK/Az9zJZBaFDy6jz/3v7vj7czggY0P1wzUlhWPH8/lGyjV8rLhDz3W5jzuMOHRUbWPz9hyOdP8S4eZenk+CI8j7+LsyjjyPyWUjDsw8z8kdatqdaGv6bAvvawAb7eBzJD32uRcVthzgeCABORXtTSGCJ00q9vmWzWZDngibQXsDdmAOHMT6eUA49J53R9vS95wzxNkdlK6eTqfRp16ZzPvcxyrt7dFwd/a9sAntzpjzv3vZXgCqA0VYJmeOnXH0SDptBWjis4wr2sDXQZ6Pd3DZIr+nXbwQg7eF9zn3ZI0DhHMf8uTIoXmeRjvncrkEC/i4dfVNNuZbupiHM/0e8OM7zE1AkI91Z3fS+2i6EAiMczrAI12w1BS9eRv75llYet66n7S0R9sS6CzthVuz2dSPfvQjfec73wlnIZfLxQKJ9p+FFUaEhRvHR1o48LAobHxeecfBEv/H93q9XlR4g/Fw4EW0Gw0ykVtPzk5LJ8hnQA6DbCtdkIBI82WAZW1tTY1GI9oL58ojchwINx6PVS6XE1IXr9jG9VdWViLXIL0JSVKtVtNHH32kzc3NyClw5oZDB4m44xh73pIkNRqNiLZOJhPt7+9rdXU1osKwYPV6XRsbG5GLREWrWq0W+n+XrNHuRNJns1nk1xDVJx+Ef3e73ciJgv3DcXS50mQySThwPm6oOCcp5EBI2CqVSrB+nuTL5uM5S9Li3KKjoyNNp1NtbGxEyVCXX/GeMA3kycDwuEPHvUkWxkkl3wQHxmV8mUwmEsrdkca5h4EhVwsHhkpXhULh0mqB6TG8s7OjwWCgwWAQ7ArlwJED9no97e/vRwnrYrEY1wcAuMRzY2MjymKfnp5GVbzNzc0AieTI8N7dblfNZlPtdjvkccwTmAgqlNG2tMnKyor29vZiHksK+Rjgy/OBPOAiKb7DuPICALRpNpvVzs5OzHtYI9YU8mmI1uN8Ms8ByuTdNZvNkJ3CRjrQdlaWcQrQwYElyAOTBBiCQWK+wdohUT04OFC5XI6S9c/aqZ3P59GmXtmSOZnJZKKE+IusBveyrVwua319Xffu3UsAFWeqCQSwPs/n8wg40vesQ7DLLmHDAEyNRiMB7Nmb9vf3VS6Xdf369cRxAteuXdPGxsbLaqLX3vA5WHubzaaazaY6nc5LfrJX25ZAZ2kv3Igos1k5qHHj99SNh22gWgyf8Qg5zjD/5ws+Wv90BNw3dV/YpcUBnB4ZdSbII2Xu4Lik6VFyisdFO3Fc/Zn8naVk0QWP9nvULf0MtN9lBhgZDoeR4JzWdztAAjC6jI7n4P3oK5wsNlKeiwg30cBMJhMOCuB3NpuFE5p+R/7fpYve34Ayj9L7T7oP0v00m80imRemA/AC25SWEPI9xpZHVD0qT/Q9zSxdxi7Rz4BUd/x5Dwf9/uPf5ZrS4rwZHGmfKz4//Aeg6wfTXsYcAFJyuVzknAA60PTDhrjkinb281rIHXBWjPnhcj8Anee8rK6uRv4PgYF0xDk9LgB6vmYAENKgy/vAmQXPb+L7Pk+kxblNyMq4DoABEOPP5fJQruGR9TT7chn7523pEkFf9zDeGXCGowUI9+dkbnBG12XBlGdhrBXS4hBpxpJX/nrbotzO7DH2fO1hLDvLz1rmzLivnb6u+I+0OAuP/3dWG/Y7rSooFotvFfh81pbuAwq5eH7i0h62JdBZ2kszXxDRphcKhfh9uVxWoVDQ7du3VavV4uBAotB+HZyq9ObtGzqbMKwRUdXz8/Mo70suiLM2mUwm9P+XbZ44CGikYVY4M6VWqyVKN1/F2LyR2YxGo8hdQVNdqVSCeUFWRDI0740eGofcS1U/yoiMdrvdRBUsr+YFKCJJPZPJhBMrKc7Rmc/nqtfryufz2t7eTsjjqOSzv7+vVqsVeRucCeOSGFgT31h5Jm8zxg8HPnpVM0ALZz7gZCIDQl5E0j4OJJHxer0ejjZR0UqlolKplADMtFe/3w8nAiN5nP4hunl4eBj5NYxpSZc6l2nGgFLdvA8sH+MkXcUQh8ZlZKurq5Ff4kwoY5szgChpzLPyjrQXY/S9996LcVIsFnX79u1wppvNpvr9vsbj8aWbNEAWR4nvUcSCamQEQDwnaTqdBjMIi0GuULfbjblJH9F2tDvMlTNqDjCZT7QZoMTlnjBu/NRqtWCZfF344osvNBqNovw6kW+q4pHPBNBm3aJPJcWaiAPJulGtVlWr1QKMeEDGZZd+3hZzirEHwIEFg+ml3TudjsbjsU5OTnR+fh5FJgCrafD/rGw6nerLL7/UdDrV3t6eCoWCGo1GsAaMubdNHgULwzrQbrdjHWBOOWjhkOFWq/WQTJrKgiggWBdY/wBS7J8E5WDSa7VaFKoAAK2uXpw/RSGSpT25raysRHVD6SLn8J/8k3+iZrP5kp/s1bYl0FnaSzPXyftGjHOSjrh61JHINSyFR/OlRcQSB8HzIdJRWb82n/FIv0e6/Rn9maVFtJ5rpNmLJzF/58vaAocHNiTNOrFppdkMz7t5lAEMPWcC8MdmSdQdSZ+0cMq5Bp9FxufnG3nU1duL79BP6Uh72mH393f5mLdZmu3wdqKtkPM4m3FZH/r7e2Us2of/S0dV6QcAL30AUHQWjDFN9JV7+jh0sMO49LGOZM0T3tOfp73SeWmYs3W0nQN5ZH/kjACs01WXcKowl6QBONIMLf1I36ZZChx6H+v0l59n5HkkFFdgXhBM8QqP6TZireD+AB5fazzCehl767JYxiuBHUCsn0NDn9NOvhbR9j7uJpNJgv0FmNJnrGdcN820ePum34n38rwPxgbtz/9TBAEg51USn5Ux9nHoOSi0Wq1G+zxJDtmbZK5qkJLjkv/38ck6S5sSsHCGxtsxvff6tdN7n6/zfg1nPqXFkQNLu5oxn1gTxuNxnDG3tEfbEugs7aVZt9vVV199lYgAwuYQLZrP5/r000+Vy+XC6b7sTzZaj4hS0QmZHJHPYrEYCz7nrbizR9SKCk/OFgGqLjOiryxEODNPu+myoVDdiKglIIeqXPl8Xg8ePIgcGiLV7qzX63VVKhV95zvfiXNhHmVsepPJJKF1Z+PzJH0HFK73ly5yKXwDpT9g5WazWVRoos2JTJM3QxSc+7jEK5u9OBfIgQI5FbAV/vz0Ra/X03A4jN/hFHq5YE/+ZkwwFqTFGU1cwwGO/xuW7/DwUOPxWO+//35EOmEwM5mMdnZ2wnkm74Q+4H5EVam852AC55J2IrqazWYjL8idVhyR+Xwep9z3+/0YA5yLQnQcYHB0dKSjo6OoSnZ8fKzpdBr5Je5MP0665ECECl7MM9/IV1ZWIl+Hc4McLMIQ0k9ckzYCKKysrERlLtaMyWSivb09NRqN6B/GdprV3d3dlaTIz2JckWfnYNOj5wQWGAvktpXLZb3zzjtxT4Isk8lEnU4nwCFV1mBx6LtCoaB6va7BYBDlzmezWTAuAGsSv3nvbrcb96SfPXjAnCTXh4IarH1HR0cxRslx45qsjbT9Z599pnq9rnfeeeeZFSVgnAOyms1mPAcHDbNmAiLfFiPAQXDpxo0bms1mMf+9ip4HFJhHVPPb2NiIfRTmXVKiGirzlwOb2as5x6jb7UZQIC2HrFQq+vDDD3V4eKj79++/jKZ6bY09g/17MBjo8PAwAP/SLrcl0FnaSzNkJmysLIjOCpAM7ZFfT5J3psMtzRRwXZxF/s/PY3DGxisfcT13fD1a5ezOZVrmb2NEZ7kP90wzCjgizkI4E4JkyhMZ00b0G809Py5tweH2SD/mIIDP0YdpJgSnHuDGTzqCyPvhRHk/OPuS7jfuxY/LN+jPR5lHGb3NYVnSuUmwD4AkHAqPgqcZA2ef/B14LjaydETWGRvGIGPdx2c6kur/Jyn61JkLxg3Xd/M8kPl8Hvk9JLX7Ya6eV/Yoc+bWGQzAtLN79Lm3gxvP5KwbbeLzEDDrBR94N28zZ4sdIDIXJUXwwa9Pu6UDJ5RcxgBqFFXgeZl3vBNst0fcAV8OptLj3EEFlc+ohIY505dmwn1ceeDAGbPJZBJrgjPPzkBS+IRctGexHvo6Ky3OIsKZ96IxOIXOUrzJTA/BiMvyMNPt7/OJoJDPI5gX+l9K7oGMGa5zGbv8qEAH5eo9sLK0qxnrJH05Ho/V6XQSUv6lPWxLoLO0l2aclE7JSS8tzMZ5dnamZrOZkGPA4DDpqXRENSnpYgEYDAaxaBMBpboSkVk0xC6VImLqyZqdTicWbzZ+l2Xg/GWzWdVqNVUqlW+UiF3FisWibt68qXa7rcPDw3AySqWS6vV65GT0+/2QEeGMZDKZOKOGKmY4PJubmw/dq9fr6e7du5GPBFip1Wqq1WrhTHtECYdSUsK5I/EUx9NzWDyvAeDgDB1V2dbX1+MU+OPj49jA+S59zubK53EacbDciJSn5To4kbPZLCrr4ciVSqXI81hbW4uDXFdXL8qOl8tlNZtNHRwcRIS90WioUqmo2+2q3+9rZWUl8oyw8/NznZycJKRQ5IdQuc5ZMpge2qFcLqtcLsd30046OS2wJM1mM5HncfPmTU2n03gG7kEVMEAauVSTySTh2MDQEck9Pz/X3t5e5NY9ijX05xyNRgkgTvECSo+vrKyo2+1GThC5Ny7BcvAPMHfA54B0OByq1Wppd3dX9Xo9DvlEXkahAsYCkWmkZxyG2ev14tren+TkbG5uRlDBJYSZTEb1el1ra2saDAaJoA3tfP/+fQ0GAx0cHMQYm80u8p/W19cT5+asr6+rXq8Hs8u6xDu/8847Wltb0+effx4SPipDsnbQ9zwr4IWzjaiQyDj0UtkOgOgXAhlffPGFut2url+/rnw+nzjP6GnNy66fn59rf38/9gQYNuZJPp+PtXhjYyP68E21brer4+NjHRwcqNlsBgiEmWWeeMBidXVVGxsbkRNJQMfXWIy+pzBFJpNJHEgLa/pNOTj1el1/5s/8GUnSL3/5y+feLm+Ssf+wjxwcHOhnP/uZWq3WS36yV9uWQGdpL82IBLNQSsmzUZxhwXzhvexzHt1PR/Bwjpx+f1QECkfJafp0VNl/nLVweRXfu4zCv4oB8HDieH4/XBUWxiNtaSfMI+7SRRKzR4ZIXm232xoOh7FJEg3FaeV3OMIeYeZP3tWvn247r1jm0Whnkeg7T+wGpKSj0B5tRLqE4+lOsD8L93aw5NfzvmTspPuayDvtLy2q0XnhCK6D0+vXdJDlpY3508eTg0raKC1J8387Y+PafUCUMzeXsV1EhgE6Lsv0tiIZP32tywxgQz6PM48wAOn55QALmQbyM+Y9Bxoy/h0A+g9jk/LnyMl4bvoWo19ms1kUoSDnDDYaidfp6anq9XqAGWdZeBZAoDOBgHYSxDm3C+kgcjmfi15xEKBEexB0YPx7np3LC2G3nPHgmbmms470UVoqSrBCWhQ44HkAs858fxsjOLK6uhr35KBXnplCCbSPJ3C/7pZmMDHGH//H/KYPAbHp/dLzZnwe+GdZa5AR0/++zzHOCFSwtzOueFYCUm8y6Hxelu7z4XCo4+PjqGD7tphX7LyKLYHO0l6asSFtbW1JWkhzWCBx8L0KFk4MDqakS6URaMyJkuKssOgTrWIBx+FhYYdWdzqeDd4XG5wFSjITtff7UW6VaCvG+3yTfh3HmCj9cDiMClLkm5CQiEOIo0US9tdffx1Mwdramo6OjiJSSz8MBoOIBHpuC22Jg+vR+Nlspq2tLeXz+XDOqEzFRoazTrQXJ5ecKvIP3BmnnZEtwjLx/Di79DlgjzM9YAHQkdPXvAO5CLCBl0k3yA+i3QFiMADlclnj8Vj37t0LB6NSqWh7ezuRu1OtVsOZh2Hjug5wXP7G8wDuyHc6PT2NqnHMCXekve1w/pyJgZ3IZrNxPpADOM9PWl1d1Wg0ijFxenqqvb09lUqlhERPUvQT4OVx0kBYSoAGchuXjvE+DiqlBQt2dnYW+QDkYsA6+gGdntBPDhhOn8sZvQgJnz8/P1ev15O02FAbjUYCpJfLZe3u7uqDDz6I9/MAy2WOPetHoVCINQabz+e6fft2jDl+1+l0dHBwoNFoFOwt5wfBek0mEw2HQxUKhXAkDw8Plclk1Ol0EnMGpq7X6ymXy2l3dzeeGwak1Wrp5OQkct1gOR2oSRfnb8F4Sgq2uNVqaTQaRVW2er3+yDHxJJbNXhw5wF4Ag0NbsQ4w1ieTiSqVSvT/627MacYwRi4qzBa5b65sAMg4WCRIsLW1FesBc5CiD178Ji1hPT8/D6a53W7r9PQ0qoD54c9cs1gs6tatW/rss89eSvu9SXZwcKCf/OQnj82HfNMsm81qY2NDKysrV87xWgKdpb0063a7+vrrr7W3txfOCofiefSdczJcF4x0BktHbaVFFDudm+G/I5KZrpSFeeSKPz3K79fFUYU9oRQvJTUvc/6+KcJJu/i74gCzkbmW3yOzvA9RPZwrHFqi5ziHzmy4c+hsBvdPtw2bGNIwot44Hi4p8oRZkstdluYOpzu/9CkRbJcucU9nYTxCTSTSc32oCpUuQuBOAU4hzBVgDYcB9igdYU3nw/h7EG31fAc+76DX83EAApRCTo87Z7PcEfF+8LwevouTwz34N4wdEXln7hzkEyWWlGAdv2lMk6TvZdfTjJADX2e/XJaYZhJ9bCLxYL7wXABw5qkzn/79bDYbh15Wq9UoJc7awxh+WrbgUWCIdgDs+RymmApOrjPTzIvRaBSBCQqBOAvM/CLokMvl4vl9HPu8lpIBJZ+Pznb72GIsps+y+rbGHOQnzcx7G1IYAQDoDMO3NVgMv5bPO571eRnzwdlvDxQQDPEy/b5epN+BwInP3/R64Qyuz7dcLhffY6yyZqX3SlQK3xTgW9o3Wzr38m2wlZUVbW1taX19fQl0lvbq271799TpdLS7u6u/8Bf+gmq1mlZWVkI+xebc6/USThqRJaqrORPDwopmnMgVjhKf8fMjiFYROSV6i9ToUZKz9KY2nU7j5HIi6sViUaVSKZwTt6tI2ciR6ff7CWcZ1oJDECUlJDU4ijgazWYz/h/HJy1hIN8JZ7VQKEQElFPmvR1xiD0KTk4BTiB5Dth4PI6zVur1ejhuOC2cr+IgI5PJBBPHBuoJ5ZwbwrMhm+F9+AzSPJdlIMkC7J2fn0fyOE44Z8XgOOAoz2azKO/J5+fzebAxtJGzgIxPxjd5VZTm3d7eToAMwHKv11O5XNbNmzfDcXMn2CWZnKXUaDSUz+fVaDSUzWbjbCHvc3I8yFVBKnV+fh6/63Q6ajQaUYqZ+XV+fh5gNc26PI7RwQmrVCpqNBqJ93HHazQaRV4Xfcocm8/nAWTcqQWw5XI5Xb9+PcF2ktdUq9U0Ho/VbDZjfqbzj6jGd/36deVyOdXrdeVyuTjzhvFGWz4Pc0ewXq9H/pl0IT1tt9tqtVrq9XrBuFF9EdDZaDQkXUTWh8NhrCO8B21LpTbYWECNywHJ7/E1ljnkBTwYj8zBwWDwTJmUTCYTIBkGHcZJUqznw+FQ7XZbnU5HJycnkd+QZkKexphv7C/p36dlr8/SWEdYB1nzXf65trYWFSmbzWaiXD1rP6CPoEOlUknsZextFCVg7Z1Op8Gcs75ubGwok8mo1WpFcQgqaKb3PdbQZ1l2fGlvj62trenjjz9WqVTSH//xH1/pO0ugs7SXZmysaO09ou+5MWnWxSu0eQSK/8fpwlFgUSfq6QADYOJnvHg+B6CH/+NzLk9jsyPSzz34d7qq3JOYO39eCQ6HTlIAFyLs/l2+jxzB8wH4rqTE+/B7T+J2tsyjeCQkIwsh0g2YdPkN93EwBXhJR309t0la5L2k2QwimvQZBQ0YL0TrXdbnkUWYHtguJG1Ew/m8gxV35JB4ed+4k+95M9lsNiHVIqGb56DduOfq6sUBezgGXiLbwYQDdaRszmTwWd4bII7zub6+HkDLn0FSyBErlUo4Ll7FTrpwbJH5eV9/k62vr6tarUZQgvfmXXh+JJ84aV6MhHHmYxNmg/nhYJe+JTG+VCqFU3x2dqaNjY0A6zjG/m9kWy8jGu1RcZikbDYbLDjSNfoa0Avgz2YXhVzS2nYO8YXlgvH0YgnuIPscZG4wxwhsMJ54rl6vF6z9tzWKR3BAJYwsARmcdy8ewTgpl8tRFllSvJ8zlR4EYu2mDT3glF7TnRl9XmPE9yjWbAI1BCYmk4mq1WqiKicGeOVazHkHNr7vONNDwMCDAj4mfH12sOughnVtmaOztKcxmPZarXbl7yyBztJemrFoojkfDochx0LOM5/Pg4Vgo/G8CZdhuZMGwEB6Q2Is1Z1wiprNpsbjcTgy7rChO/ckczYPKk4R+eR7Dm5wTsvlsiqVylO1Ec6edOHcIEPwyLzn4hApc8Dgsg4qcaVlBdwHR5Fob5qR4LOZTCYq7AA0Go1GOKk8mwMtnKJ6vR5sBdIS+h9gS//zTDhHXmrbmapcLhfafK6Bg0eeVFoSl849wvHnWWFm3KGBxeE8IHLIcChwOtIOAgABxhCgQGU6Bziuf6/VatHunGtCxUAA18bGRowvntdlXpTa5flhtXDckfR4YQAAO6wp8knPeXHZFIxlWtLyOAMYU1mRdiApmjLwhUJB2WxW7XZbk8lE3W43mB00//Q1bTidXpwZk8/nVa/XExKPTOai6iDgbXV1NXJR3nvvvZCopaV0r5LRb+RVDAaDGOP0w3Q61f3797W2tqZbt24FSwbwoF/JdwLoeFBpdXU11hb6n7lJmzjLd3p6qoODA2WzWV27dk3r6+vBIj148CDWjG9jBCUkxXqczWbV7/cj97BQKKhUKmlzc1Pj8ViHh4fqdDq6f/++tre3tbW1Fd8jv4m10MvBZ7PZyEHc3NwMgMX8SZuPy2dRdfMy8yCepKjsyLk0R0dHmkwmun37tsrlcgBzwC1l5QGFrIEEbVjDqa7GGABIUlrdg278jrWQ8cAZT6xj0sU+tru7+0SO6tKWhq2ururGjRuXVo595Hee4/MsbWlXMk/GdmeExdyZEmdT0lpjNisWbafp+T6OO44loMCjWp7v4NF810J7NC0d2cPZxbnles4kXNVcUkX1JaQ7abbGI+oABNoIJ9ZlbZhHrh2o8G8is/RRuj88B8arj+EMo6N3eQv3cEbHz++B4aP/2KwBalzP87ZoZzbvNPsDI+TjiDHi4MPfy1kugMxwOAwH26Ur9DvX4p6e4+CAz9sQ8EhpXh+TrsVHruJMo4N9L5HMvMDR9CpVzsZ41T7aG8kJ/cFzMo4dnMEUOkjzcfko45lgQ/leOppM/6V/+Ey6Uh1rAjIlAg7ORpVKpfg9a8Z0Oo2y8D7XX3UDsEjStWvXVC6XYyweHBwE4OWzPt68f9LOOeOPeeO5PJJC7uf5Squrq1F8Ayd7NrsoXHJwcKDZbKZr165967ZlrjurSUEUl9VJC6kykqpM5kJW3Gg0VCqVJF3MbS/Nz57B7wmK5XI5bW1tPRT8cDUAwOFFGfMIOSgyPti5dG4rwTJyFOkjxglSXtqCQA3gknXE1RZpiR5/97XKn/dlsaJLe/0NVvZJgsdLoLO0l24sqDjSUvJgLDZQ35xx5nGmZrOLE6CJCMNaeK4Em7g7ZFzbQYInmKflcO7wOTBj0QZwnZ6eqlwuJ9inNDNyFSMyPZ/P4ywLSnciQ4GpwEHzfA1nRMiNwLmXFlKynZ0d5XI5lUqlREEF3m1lZUX9fj9K33rlJYAPzhQlbnGKiMyn5Q60y2AwCAkj/UXlLPqQtsTSMkcHp+RvpY02ci07Gz9nE/EutJWkyPUqFArBRK2srKhSqYQDQTTcq5EBXHAk3Dnj94wbGDbkc4xrQB0VBomoU12Jsr6j0SjkOjj/RKdbrZZms1lEUN3BRTaG5A8GlIMmHaC6s0Rb8Zxephmnifn7KGOO40S5pI+xyfd9zGCMJwAykpzT01Otr6+HDI0DCre2tiInLG1UfnxdDSDHuKcd/uAP/iByHj1HkbYkQCEpWDT6HDDO9zirB0kbEikkjdybdQG2qd1uq9frxTj87ne/+62BjhdBgO3MZDJqNBqJdQ+WgvWr1+up2WxqOp3qww8/1N7eXjCIMP8UbiH4w5xotVoqlUqqVqvxjh6EADSk5Z/P21ZWklUHkS8OBgMNBoNESX6X3NI2BBlYm9hPKGQwHo91/fp1bW9va21tLSRyMD7ORmPsm5PJJIJnHujzUtZLW9qTGNK1JaOztNfK0N1Lisih54KwAHu03yVFbMbpf7s+2CPYnv+B48Z3+RxOMM5aOrruZ1jwHc8f8KgzJWGpzvQkmzwO22g0UrfbfcgZ9PsQbeeZPW+EvxeLxYTDiuGYssnh6Hq+TLVaDX27O+k8i+crObOG8w9rAHPgEpF0X7NRcl0fA260PW3D+T8uKwEYcD0cs3Spcd4DR8ffCyeGSDnji+87w8T4gT1cXV2NHBAfKwAGPuOsoR+M6PejhC/5GR6hp40IGjBGkXTRX8hLAH2cA4TTCNhjzDiz5+3vDBBttra2ptPTU52cnASbQinuRxklmlutljqdTrQBSe5e5ltS5GVg7rSvra0FoCE/A9D6NiVAsw7cunUrQNzZ2Znu3Llz6Snq6WT2NHsNU+n5d4wrJMfIMFl3XWaLPO74+Fiff/65arWatra2nhrwMPZZXwhWMD+ciWGOwEDUajUVi0XduHEjcbRBNntR5IWEehg/GGkCHP1+P8EYe1s9aSDrSYy2TCsIRqNRHA+QzgGkrby09vr6+kOFJwgCuQqAtY5gm6QAuVyXvQHzayFBJtDlDLZLoq8ic33bjTnH+uWBtbfNVlYuqq7t7u5e+TtLoLO0l27kO1DqFIfLF1xpQcEzuT0/RFpEkdiwnJpnY3B5EdH/dF4Cm560SIJ33fJ4PI6zVEjWlpQ4RwSwNZ/PE4zAkyZg5nI57ezs6Pj4WMfHxyEtSIMp15R7pIzNGOYKxkBanOSOJAqnulAohOxkfX09dNmcsJ2WYnmkcGVlRZubm9GWLrVC5+3Mjcu5PFcoXToWgOTVoDwPhvMbABQYjhDO18rKSsiWiHjyLA4QXKNOm+IgeH4TDhzmOUDO2qBvp3ADDkO1Wg0QISkx9hzY8f6wFfV6PcayvyeOpr8HOS6MU0CMM1aAPWehvEAH78Hz0Ccuc+H9YQbz+byKxWLkSzzK+atUKqpUKuFMck+YJs+pkxRjmPu6tLBSqejmzZsqlUpRcextNIDOe++9J2nB3OEQE9QA9NDXXmXNCxHA9iEbWV9fj3lMHqOz1vSl5+5w1s3Pf/5z3bp1SxsbGw9Jnq5qaaDT6/WiAuLKykUVOZ7J14nV1VXt7e3p+vXr2t3dVbVaDSaHMvfkwJETxzlBtAtVQKlSlpZgPS82h3dNM6X9fl/Hx8fq9/uRg+VsOPsiAQPWQz+ry9lk+pr1+uzs4nycwWCQYGJY/9LyYQ92Abxg+1nrlozOk9nq6moEGKWL4B2s2ttm2WxWu7u72tvbu/J3liNtaS/dOp2O7ty5o/fee08bGxvh9PZ6vcjJkB4+Fdj/7smRRI88ousOL9cEJHn0PS35SudZOFOCNMijeH442tnZmbrdbkTQnyZyNZlMItLN5uXggkhPWprAvTw6O5/P4yBNNjdkDYCRfr8fh/EVCoVwJtypGAwGiQidJ6BKiuR/Dvl05gwtP+AMVsTzCNL5P9KiCAFOFe2NPA4WBHmfA0DAEm1Fm8IYUHmLz/HeABYAAUBIWjj6KysrcU9/LjYknpdkb8aOAyVAAuyflIywe96Pl0r3AhQeIcWJ8EpsPn/4nAM0z7XwnCukXvwQycYJ/uKLL8K5xAD3ztgNBoNgax9lRJ1xWhmfOEfOmPr439jYULlc1rvvvqtcLqdarfbWMDdXNcbFe++9F07uaDTSgwcPYnx41TLWOXeQGXPkptXr9QA9AApnlP13zCN+ptOpHjx4oFKplNDap9mKx73P6uqq6vV6yGYBx0g3iXqXy+VgFOfzuTY2NrS9vR0BAOYZc6NarcZa63lklGvGkX/RlgamaSM/iAOS2QeY16xvBCc8sCTp0kIUvia4PJt1lfbzPWI2mwWzxprpzA/S4mXVtatbuVzW7du3tbOzE2v3gwcPop3fJmN/fZJiH0ugs7SXbhwcev36dRWLxVgYPUroYMPNJQMecfccC2khs8EJd+eQBZ0N00to4tADCNi007ItNknfFIg0VqvVcO6f1JABEWVMsygOvNIabEnRBjBVgA900siA2Nj97BKP8Pq7Eknygwv5PH02mUz04MEDSYrKXWyKMDUuEXNn3mVrOCA4V6PRKDZVB0IYFaeGw2FCluagkJPePX8JB3s6nUZfe6QTzTsOAu2BNp57MRZwIgaDQZTuRapGfwCUAQRnZ2fqdDrKZrORk+QVtBinLp+k8pvnHklKAE+KWfBsOK1IV2hvgJRHdikB3Wg0VKlU4uwjosYHBweRGyctWFd/Z0lRQOObgE6hUIjoMfIoxoSzVN7+jUZDjUZD77333jLB+RFG/966dSucXap14bD2+311u91EziJr2vr6elQnY/5RdEBSgG5ypaSFBBRwTg4ZUqajoyOdn5/HGoVTflVGxINZgHbPMWJ+cI4Z87LRaCT0/Q52cMLX1taCweL9X7Zjzjr9KCaE4AbyZIJr9CHrGcEl2sOZYC/awZriYJW9BaDjrC/XIrDBGup5lDwnZ3It7WpWKpX03nvvxbgdDoc6ODiINfxtMgfhV7Ul0FnaS7f79+9rPB7rww8/VD6fjwP+kJ1Ji0RoHE+ivZISuRLSQjpwenqayPlhQwPgOANElIvSqWwS/LCg4Gih98axgoVCejedThMVn9x5v4qdn5/HGT9EEQFclUolNOkurZIW58U4KJMUOVDp38Oi4GTu7OyoVqtpe3s73qvX66nf74cTQqTTGR4vDgDI8H5KS00ocQuw4h15NpxknA+uValUEvcH+Lgcy+UY5XI5UV4aGSEOsp9xxA/vMR6PE1WJOp2OJCUipjgWjEGkI37uDFI8KXnqvTt0/H86h8edFdoOR5+2TTs+SE5wVADZ7og6IPbKgfybPtrY2IhqTsjseN5sNqvvfve7unXrlo6OjiKQ4EEKQGar1Qqm7FHROO5NSWjGA6AK6Qbz7/T0VKVSSbdu3YqDI5f2zUYfVCoVffDBB+r3+7p3755WVy8OfnSZKONtPp+HDJECFwQmPHCCEwLD6cbcQi4KK5jL5YLpedI+BKAwnhzgsN5Op1MdHh7G4bDpgBOBGcY2cisCOa/CuHJJGe9KP41Go1iDXUbKoZ6sQ+QDwqjBDrEWedCQNuGHe1GIJp1b6Qz8bDZLBNHm83kiP87zgJZ2Ncvlctrc3IxAWb/f14MHDxKHcS/t0fZEM/hv/a2/pT/35/6cKpWKdnZ29C/+i/+iPvnkk8Rn/vJf/ssJhJ/JZPRv/Vv/VuIzd+7c0W/91m+pWCxqZ2dH/8F/8B8Efbq0t8+azaZ++ctfRt6LJ12yOPqiSgU1AAQgBwfPNzhJCdYGat81wu7csaB7gjh5QePxOHTQvsCzEXq+EM/PMz6pFh3H1CPYXqHKnXNPGHfg40mynt/izwwwIndlZ2dH29vbajQaEdnnhHHOuvGIOg4xz4qTQVUw+gnWxx12tPG+OdNOLgXj3dDM+6GNMHD0tcs0AMX09Ww2i80dpmJjYyMBhAFjHhXm/Vy7D4OFg+f3hUHxZH42fto9LdFJJ4B7fgQRdH7vMkDa1M3PjiLqinPkSfvc350WZ4toK9rdK98xN2/evKlf+ZVf0fb2tmq1WjhCXIdzkvr9fgBbwPVlYx6WhhyftbW1mHv8nh9yf7a2ttRoNJ4q1+NtNRxezqNgPFUqlQCkgFXG3Pr6euRSsRY4w8ocJ+jgcxljXev1ejo5OYl1xdftJzHmIGuNn43EmkEOH4VK/HlY23HmWZsA3LSF22VFUZ6nuQzXK5MC9gnkEKyjXcbjsQaDwUMFBFiTea80yJEWh3k70w9AZe/h2fiey/xYI1lX0nmI7LNLu5rBXsJ+jkYjnZycRABzaY+3J2J0fvd3f1e//du/rT/35/6cptOp/qP/6D/SD3/4Q/3sZz8LpClJ/8a/8W/oP/vP/rP4t5fzPD8/12/91m9pb29P//Af/kM9ePBA/+q/+q9qbW1N/+V/+V8+g1da2utqw+FQrVYrkiQ90dNlDSyUnkiO0+eRRTbrTqcTjqMDAV/gceoADh7dY6NZW1uLkrU4zul8GJ6R/x+NRiEvarfbmk6nkdTv5pWrPHFWUoAInjGXy0UVMddKu5wrfRYI74+mF8068qR6vZ4olkBfNJtNtVot9Xq9cG6RcMEEkHfDxsUmSht0Op1wDtyJ9sRZ+g0mBSZgMBgkJA+ATJ6TDRfnhvd158s3Y1gxP+PID5Q9Pz+PKC7tD2CgX46Pj8MhkhQAxvNkvAqbM06e2+Va9kajoWx2cYAoAB6pIW0LU0PiNA4MoIJ3xZnJZrMxbuhzGDdn82gHB3tUPNvd3f3GQx5xaAGDXBvGyQtYOHhzJ9LHsHSRu0dOCO1Bu6+srOidd94JCcyrEHV/HQ0J2q/92q9FMj8HzDKGmLsEGMhx9GADUfx09S5nxQH3Do663a6++OKLCCARNMBB5jvp8vLSgtGEKWKunZ2daXNzM9YagAzBhMPDw4SDCPNBMODk5CTWmPn8orQ9QHAymej4+DjO03mR4BrGnb3Ji0QQ5EPiStCK92etOT09DSabgg2sY4A+wKgH/ggQeoEYJKqsmR54YXxcFuDj/5bFCK5uxWJR169fV71elyQdHh7qRz/6kY6Ojl7ug70m9kQj7X//3//3xL//+//+v9fOzo7+8A//UH/pL/2l+H2xWHxkRYT/4//4P/Szn/1Mf//v/33t7u7q13/91/Wf/+f/uf7G3/gb+k/+k/8kcSDZ0t4uI3fCK0C5zApH1jc0aREd98RJvjudTkMehVPvCewY8ikOe4NJ8mT2lZVF2WU2eIAQz+GLNxsHP6PRKJHc68ZnPQrq8jIHFu4we6J/Og/GGSnaMx055Xues8RG1+/31W631e/345wV9P3IOyRpY2Mj2hqnhPeg/V2njdYch5h+IQ/D2V3AB9Xs0gnODu48Ykj/el/yGZypdE6RM0t812UWaM4BWwDdyWQSrEmaRQOgAAidRaH4g6Q4lwaQSI4AQJp3AjQ5oGN88vzOMAF0MplMBKMc+DNWmE+0K++H0/RNuS/0hbels1f0SxroeCAj/VnYIKquYbBljUYjUQZ9aU9nuVxOe3t76vf7se4wFhgHROUZo7DAfMbXH/qUceVzQFrIZTOZTORywQQgs2JdY631Qzi9r5nHSFZZE12e5Wz/6elpsFGYnwElXeSMIplcX19P5N5RgQwm8UUZTKxLPlmbXGpK1czV1dWQ+XY6nURgQ1oEBr3ojq/RPk/93shbma+AK37vhWl8XqYZsCdVOLztxjETkAbITZfStavZt4LUaNY3NjYSv/8f/8f/Uf/D//A/aG9vT//Cv/Av6D/+j//j6KB/9I/+kT7++ONEDex/7p/75/TX//pf109/+lP92T/7Zx+6D44VtuzcN9OGw6GazWbkPXjCuBuOmJe7zWQyCTreafvxeBzgAOea6KQ7tlzDAQLAgcghzni/348NJp/Pq1wuh8yLDcLlSF5Q4VELPN8jz8clYTizOAO8hwMFIo5Ue+N9JMVhnxR3gKEAlMBS4ESur69rOBxGKdp6va7JZKLDw8NwqAEOABkca04fZ+PkOXu9XkR82ZgBEGkwi5XL5Xh3opOSgiViPEhKPH+1WlWxWIzvAywATfQ7DjWbO9cBPHF9d8xdkkaSL+Dt/Pw81joi3e4EAoZwDkguBejCzgEKpMWp7y4/dAkeIJVkcZxFdzhXVlaiaAJjgcpmGPKfUqmkwWCgbrer4+NjffLJJ7p+/fpD67zPx93dXZXLZZ2cnMRYoB+Hw6GGw2HMa/7k8EXaiCqLsELIDmFFa7VagLhcLqdGo5GQAy7t21mhUNDu7m6U+m+1WgkpFO2MpNAPmqXwhiS1Wq1gJ5nvzJ3xeBxlpslpzGazGg6HOjw81M7OTuSATKdT7e/vazQaaXt7O8aMAx7mGnsBf3qen6SQcFJ0pNvtxrpDYA2pLLlCvNvR0ZFWV1dVq9USgABm8XkXwJhOpxoMBhG0Yd0dDoc6OTmJkuEAFgIbrK1UhWRPazabkdvjIJa9wAGpy+ZYe33fpf35N2s/bUiA7+joSIVCIRiJpT2Z1et1ffzxx5E7e3R0pB//+MeJyplLe7Q9NdCZzWb6d//df1e/8Ru/oV/7tV+L3//L//K/rHfffVfXr1/Xj370I/2Nv/E39Mknn+h/+V/+F0nS/v7+Qwf98O/9/f1L7/W3/tbf0n/6n/6nT/uoS3tNzM+huSw3h8X6UeYOLwssbIpLwli4fcH2a6QBFBF2jzbjpHsiLoDcZRd+DcwjoP47LyKA40vknXvxLETN0nk6/lzcm7ZNg0aXduFcjsfjcCS9Etr6+no4op4zBGvj74iD4Q7E6enFYZ6eV+VOlD+TR/tw0gETLtV6VNumGSo+4xu2Rzdd9+79w3hA4sa1ADiAD3eoJAW49Pamn7wamrSoiudglnfg3z7+0gwdkVlnkzzy61F2IrguT/N8OBypYrEYc2Y8HqvVaj0S5HAP3iOd2Mz1KexAlJy56RFiB5xnZ2chvSNgUa/X4/pU71oqAJ6drayshHQyLWX0/mROejU/aZE/RjCFSomAAcqSp8c1TAmBLWcSBoNBFELxcs/SYv1xppQgAXOWeefBJj+2wMefB7W8uAugiGvQHi8qV4c54QETns3zHL0fvM/SSggq7ZFv6XuIqxV4Z1+vWHd4b/qPvmfdgKFjzWb+f9MevrTLLZfLaXt7W9VqVdJFjs7x8fFLfqrXx54a6Pz2b/+2fvKTn+j//r//78Tv/81/89+Mv3/88ce6du2a/spf+Sv67LPP9MEHHzzVvf7D//A/1L//7//78e9ut6tbt2493YMv7ZW1w8NDffLJJ/r4448jCoV8ByfVI3Us0khy2HhxmNiUkTVICk0/GxZOFJulVy2TFFE+l12lHVjYF54ViQdFA5D08HwAiVKplNigPbdoOp2q2WyGTMqdPzYOGBKeG2CEnh7Z0mw2iwMb2dCItvJdd7RHo5F6vV68hyekusSLqnZsdp7r4RuppGhT13/z3i7r8jZ0NgzHGDaGAgOUlwUYs+GThHx0dBTJ8BQygBlyxsFzp7gH4JEyuuQr4DSQP0TeTLFYjHHCeOD98/m8SqVSjB/ehzaGeer1etHfk8lEnU4not4uj0tHkc/OziLHimIbHKjroD2bvahcR/s6S8RnkeJtb28rn88nJH2XGWwbpW/JIQK8wK4yJ0kab7fbunfvXoAWz51i3mHuqLlju7Rnb8ytbrcbQRACIJ5vBRgAhANSKC3MuOX3BAUcNHnwAZaYs7xyuZyuXbuWyIXM5/OJvC+A7sbGRpTzd7kwwZvhcPhQhUJy2iqVivL5vDY3N1UoFLS9vR3zwwE4QItcFda9qxhjn73LFQSPugaflxRsC3MDpp+803w+H79jbsAwsw6xblQqlUQFNPoKAOvBE2SEpVJJuVxOg8Eg2o3ndmYN4EWVu5s3bwabnJagLu3qhnRtWZL76eypgM6//W//2/o7f+fv6P/6v/4v3bx587Gf/fN//s9Lkn75y1/qgw8+0N7enn7/938/8ZmDgwNJemReDxvh0t5s44Tn09PTxIbkm6EDHc/VkBbJma4Bx1n1pHCcMCnJpHANvsv/8wz8f5ot4fk8J8OjY87OsDlRgtWfgb/7tdzh8+sT5eP92Ui5JvdGnsDGnT6ZGpYLw8lmw0SqJy207M7YSMkKYciqfIOlrdJMgoPFNAPgfZJuY2es3Ol31gsWCNkTp7R7JSCey+VlgMx0dSaXdDir4s/E/3FNZ094Tnf6vB08L8bBlLM8/r5+hgCfw7HEQUFi5PeSFsUinPn0/sEpgn1yFizN2HBtP0fF54bPEcaOpFjPZ7NZgCn657L55+/5qCTnpT0bY6yTz4iEyyP5BCGcpeTf9I/Pd8wDHlIybwuQJCnWKXI+Op1OQi7rVSd5Zn92n1uXzVNn/1kPK5WKyuVyJOtjnO3GGuJBoicxnsWZ+8eZs2Pepl5kgbWZddz3L/7tRyb43PFgCW3m/ej/T5DFg1eXMfH+jL4/edW1pT250YbPWyb5qhtj+Im/9yQfns/n+nf+nX9H/+v/+r/qH/yDf6D333//G7/z//1//58k6dq1a5KkH/zgB/ov/ov/IvS4kvT3/t7fU7Va1fe+970nfPylvUn22Wef6f79+/pTf+pPqVQqRd4Lmm0KFbjTTjQcZ0taLMBeUMAXWxZiHD2XMfB9onVsTJzLwIaC5Aun0I3fj0ajcACZoLApaM2REnhiOtdrNBqhhSdfBzkIZab5PdFKdxDZENn8vLra5uamcrmcqtWq+v2+7ty5o263q16vl2Al3NHmxHoimjivaMQBmESCAXmUZN7e3g5nl2fzilnuCHBf/g1TValUEhISnGtYP6R2xWIxwSQQTcTR9mgsZ7ZwL66Vzq9K9zGOHxFWdzIAfThmMGue+JzNZsN5qlQq8f8eDWesw854m3pltUwmo1qtljjtvVKphAyF/sDhOT8/V7lc1mQyUbvdTkiAuAfsztnZmT777DMdHBxE5R/YRw6tOzo6imvBTBFpXltbU6PRCJYKp8kTqafTaUgk/Ywon4O9Xi8kHM6GLu352N7enhqNhr788kt9/fXXidL6BI8YI9Ji3eWAYOaG59dKCoad8e6HlFJumjn1q7/6q9rY2IhcLi8+kQYKFKhgDaDIAawUlR4zmUx8bjqdamtrS+VyWbu7u1EUJH1dwIYHpZ7EHEA87vusG7DbBwcHUdyJ9+33+zo4OFCn00kUi4FFJpeNNYUzj6RkGXuq40kXQayTkxPVarXw1Vh3kXkTqIFFYm0h35I1JJfL6datWyoWi1Hu249zWNrSnsZWVlb03e9+V++9916ikvNV7IlG3m//9m/rb//tv63f+Z3fUaVSiZwaNtjPPvtMf/tv/2398//8P6/NzU396Ec/0r/37/17+kt/6S/pT//pPy1J+uEPf6jvfe97+lf+lX9F/9V/9V9pf39ff/Nv/k399m//9pK1ecsNYMBCikPklbacpQE4YB79defUI9SeJP2oiD3/9us6U8Bm5TrtyyKFHvlK52qkn9vZHdevp8tgO4vlLJQzElzDf/z/JCWimEhCvBJRWrr3qHYAPBBpl5Rg3Ngs+R4MAe2DE+t/p/0ua0fPTeKdPFrryfz+vC5FSTNILr1xdsWdKe+jtINFG3mU2vuaZ3Rmj+ul2RaPpuKkAep9DPjvcRL9DBTkJA6OADpUY2J8jUajmDPpPqJNKb7BqfG0E5K19GGhgDnWdH8Glz+lwa2DRdrOQSnXeBLZ0NKezmB0qtWqKpVK9DV95fNNWqxNjCNnfzyXh35z2a9LX10KyTx0hvxRAJf1gWdnTlLYwpkNgkKwQ+wnl+V8+ZrrQZgntTRT8jjJGmvDcDh8KMiCdNnPzkmvHzBrrG9Y+lrpwFj6Wv57X7t8bfS+pW8I5qWDMWn1wNKWdlXLZi8Ka2xtbT0xaH6iT/83/81/I+niUFC3/+6/++/0r//r/7rW19f19//+39d//V//1xoMBrp165b+pX/pX9Lf/Jt/Mz67srKiv/N3/o7++l//6/rBD36gUqmkf+1f+9cS5+4s7e02FmvYhwcPHsRp8yzgkkKG1G63E5Ifot9+dgmLP7/DOSfiTPlS1x7P5wt9trMx6fyTdBI/7JNv+plMJmQRvvEQeZ/P51HVh02l1+tJUuLEcZggz2tx6ZZr3bkvFYOy2YvqasiacIaJlCIbIZcFtojDG6mARY4Hzg65R7BeONuwLUifYIRarVY4weQi0d60GeCWDdKlTtKi8lO5XNbZ2ZkODg6C/UOeSN/wvl7MgCjv2dlZRHEZKyR8Ir0gio1DhpSF61H1DhaJksh+pgQAHqceJw5tPMn8jFPyzsj/KZVKUX6dZ63VanHoKQAEx82BqDuPkrS5uRkOIOybS33c0XTWBbBzeHioarUaByry99PTU927d0+j0UjdbjfkdIwRGCryGxgD5FMwhiqVSnxvPp9HsZp2ux3zw2WRS3u+dv36dW1vb+vTTz/VvXv3IscOhp0zbpgvLo9ibMPQ8D3WLaST5XJZGxsbiYAPldhgOV1O9zgD9DOWt7a2oix0q9UKQA2wymQyiWT+x9lsNotzhp6mEIYX2pEeBjuw/ORJNpvNCJAA9Fjn+/2+Wq1WrJu1Wi3BpNEHBId8TUzLfDkXiHPSJMWcJoeRPc1ltrS3M0Nra2sqlUrhlLJvT6fTh8p6L21pV7WVlRV95zvf0UcffRRj+ar2xNK1x9mtW7f0u7/7u994nXfffVd/9+/+3Se59dLeInOJgZ8H4g4clmYFXCfs7Mll0Xk2iDST45/zDTsdhU5rwNPSMY+QOyhJb27c3xPHPQcFUOI6eZdFpRN80wyTszlcww8hTVc/c/aMa3rULh2B58fb3D/H3z0C6WwPDjiSGG+XtJbdny3NqCE3kxYVoPiO5yKldf18F406G7eDXq7jbFGajUmPOwebPma8n9LmLJm0yMvy8YDzWCqVVKlUVCqVAgADCn1Mp9lDZ7am06lKpVIi94rPkRPHc+B8OnsHKCEinj7riPdGRkR0l2fA2WI80AecXzKbLQ6ipDJg+lmX9nyNfq1UKqrX63GoqDvSaYbNo/5+vlU6TwaZJuDI12YPGFHY5ap9nh7/a2trGo/HEaxJz9ur6v7Ta+OT2lW+5xJXX0+ZL54T6dJiz4/xfBtn2riO7xGsa6wb/v/OzHulSmdzCKxh6TXO29rf42kkgG+7vc1tR/B7c3Pz+TI6S1vai7B79+7ppz/9qd59991IECVKj8NPojlsC5si7AxyITZaNgQccmh4FgzXbhOdnM/noQnHGex0OgkJB841Ey999olXXyNS97hFyp0BHIhqtapOpxPOL9WtYGmQVLhzAZuCY0n+Dxvl8fFx5A4RSYeZIB+CNuM8DTbffr8f0dj5fB7vWSqVwpkF2GCwXtJCytLv9wPUpkGbn1sEI3J+fh4OPawD2vtms6nV1VWVy+UAVGyuvCPMQblcDubHD2P1RHkH1jwLbcy1PIcJ4If8JS2vczmJlCwsMZ/PY/yheaf6Gm2BLK1cLmtzc1M7Ozva3NxUo9F4SHb5KGcqHYF2B7XX62l/fz8SwLk/70C1JQALfV6tVoMBm0wmOjo6CiYU4MqhhVSey+Vy0U+wREjd0nlOnrvDvOx2uxGVXtqLs3feeUc3btzQ119/rXa7HbLHRqMRjOv5+XmwJh6QOT09jYMr3fGtVCra3t5+yJHG7t27p9XVVb333nuq1WpP9dw4+eQ1djodnZycBKtI2d6rMDQwvs9TNnl2dhbz6ObNm8E4s04jW2N/GwwGiapy7F1IV/lcOhiGRLXf7yufz0elU/bSbrcb74usvNPpJPI0ad9arRYBItaT8/NzPXjwQI1GQ1tbW6GI8OqZS3sym0wmkSv3thmMzscff6xCoRD79VVsCXSW9srZ0dGRvvjiC127di2ifc6Y4GA7i+POpDuXHsVik8WJSst1XFeOsTl47oFHoX3DY3MnOul5Fufni4MzXed8mfHs6MwpecznibTn8/kox0z0O802sHl79A6QMBqN4uBNpENeGYx2c0kc176M1brsBwaBazi74pXaiLqm34HnSOfmpKOFzvR5/7t80HMFfJO9jB3inrR/ehwAcD1q7WxOmqVIR75xMjz3wNlFP4/C2R/PXUMG9rRGmyAtIsEf9pK2debNpXfk5gDw0lLRdFSYNvTqg7SpR/kBsIy5dBVFH4uvs9Gvzk4x719Fo38p0U7peZ973tdpxzqd28K8dfmipAQzyxhjnH3bPmcs+3Mznx6V+5M2Z4K/DbtzmdFOHOZL8CjNgHslvMvYGp7TmWxvO1/vXFLL//F+6fWMvB/Pc/K1lh8/7sCDFzyr5+kt7dHGwciNRkOZzIWMen9/P2Ttb5NlMhlVq1XV6/Xwp65qS6CztFfK5vO5fv/3f18//elP9fHHH2tzczPYCKQLsBtpWh5HDWeQMtIs5myWaQddWhxMSZUyFnHADWzGeDwOeZC0cNI8Z4OcBRZ/2BLK6O7s7EROUBooSYty6uRpSIqTr3mPWq2mSqUS/9/pdIJhwBElRwKnU1qUHe12uxoOhyFBAWSkGYe0k+3yOv/sfD4P7T1n2MBSkAdEYi0FJshrInG1WCwGGHTGZHV1VRsbG+FY+aYMwGg0GuFkZbPZxIndvrFSLcoPJ4Q58vd18OsV+NCqS4rx5hLJtAafvCRYCZ6r3+9rNpupWCzGqeS0sYP0arWaADbc41mVGiWfgfN3yAPiUEHOIOl2uxH5XVtbi0pOh4eHOj091c7OjjKZTKKsNX04nU5VKBQSUelerxdzlPFKlLjZbMa/mfMwhczptKTxdTQcwaOjowB9uVxO169fv7LT/TKMM10ePHgQ84K1y6uGMW8AK75u4NBLi/N4pAV4YD1uNpuRx1MsFr8V0IFJYHwXi8VgS8lvuYqxb1D6+VkBU0AvlehyuZw2NjZiLJDj0ul0gvHJZDIqlUoR4SbokM1mY85xrhl9ADvNGs+exmHd6b0J4EmVTq+4l5YKs9cA1FhTnXX2c+aW9njb29vTX/trf01//s//eWUyGd27d0//4B/8A33++ecv+9FeuK2srOjGjRt655131Gq11Gq1rvzdJdBZ2itnJCly+Jg74s6quBaYzVFK6of5u+cF+I/n4Pg5A1zXDydlEwZweDTPmQOuCYshKZgSnGuPWmKPixCurq6qWCxqNBo9VBknHTl1fbY/C+3g70CSK/d3DT2f4f6eb+P34+84LnwHCZdXD0uzMvw//eV94u9Gf7OZO3vj7BgV+xwAO8PAv3lm2op3TN+LPwG9fAdJmwOidJvwHRg47uvPx73S7Ab3ANR4jpg7kc/CaCeKXszn8yim4KXX06AcGZkDYRhGP+eD+Upb8Hfah++en58HkKW/PLrt/S4psTa8jkabepEMT+x+VQ1nmAIlzt55H7GeScmcRUCwS4eZo5RC93nPOsXcv6ql2YrhcBhS1/l8HlI2Z2yfxNKS0W9rs9nFUQMUeXCGVFKioIsXG8A8VzINPpFC+5xjrLFOA9zS67S02Ge8gIjvwWlJYrqN/DldAbC0xxuBDxid8Xis4+PjCP6+bcYe3Ww2dXR0dPXvPa8HWtrSntbY9JvNpg4ODqLa2MnJSVD2vqGi4ydCxCLa7/cTEWbPycChQhqHDhqniRO2ibyzSZNzMBgMwtmVFA47YElanExdLpeDAULyw6Go7gi4Y8DvpMXJ07u7u2q1WrFRQ926PA4QBKOE/A3Ald7U2aDSckB+RxUdIu/OhnW73UQ1IJebrK2taXNzMzZWgB5t6I4d0chOp5OQgEkK5gKHmXNaiCrjmDMOYI2oznTZwYKFQkG5XE4nJyfq9/sBImibXq8XOWDOrpDbBcNQq9WCAcFoN56Ns3w8j0tasGPOvvEuPj7IaSGaTYIyeV/PypCwUUVtPl/kDcHCeGU6AOCdO3cSicx8njwaP0WdfvTxTr8gwxwOh6pWq5ETd3p6GnOQ+bW5ualsNhtzlHOQXjdj3vqYeRJH/mUZ84e8Cz8gGAaWHD1nRFmT8vl8OOycqQNogmlOyxwHg0Hi3JerGOs31SOPjo50eHgY+Q3kBz3psRYeQHmWdnZ2prt372owGGg0GimXy0XAQbpg9e/evatut5t4DoAguTuANwo5sO6Su0ogkb2PIMNwOAzmlJw4DzjRl/SDS+g8YOFyNvrV7fz8PIKYS3u8VatVffzxx3r33XclXSg3fvGLX+j4+PglP9nLs7OzM/34xz/W119/feXvLIHO0l5Jm8/nunfvnj799FPt7e0pl8vFQg4Vz0bDokrE3Z12nH8cev4kEux5DjjTkiJihXTGwQGgJq1N55oe0eR6HrH2TSydRO7Ax/NXAC04e2xMADdnb+bzeWxgzt5Ii1LV7iR7hN0BgaSQQrjumuu6fCF9rzS4cEACUCiVSvEcHuXjOTyq77/nmZ1F8v6jrWgvHEkfBzhBg8EgJGhpJ4q2d2fAAS3X5u+Uy/aNHEDtzAbt6uyZ5+QAOryEJk4GgN7H2bO2TCYTh8rSD+loOmOS8usEAzy3h/eRktUMvQ25n797etwgdUoHBd6EqLDLYJnHj7L0OvQyLZPJxOG9BwcHDzEuPtcZ+xScYH2iAIBXGCTp3dcPAiPIIB0gf5P5OuYyZAprvEq5XtPpVL1eLwoOIPmjLQAWPD9ziMAI6yrr4mWMD+u2zxsYIFdGIOnzdYvrwthexuIwh5Hpsk65ysADakt7vK2urkaJ7kwmE4d7t9vtl/1oL83Oz891//593b1798rfWQKdpb2SNpvN9I//8T/WgwcP9Bf/4l/U7u6uut2uJpNJVO9hAyDaTUTR805YkF2mhfMKm0DuB+CBzTqbzcYCAxgYj8cP5QOxGfiZEf5DNJ/o9snJiabTqSqVSsLZ842JiJlLCdbX1yNXg/waj6a500DEH+eA6HylUomNiPchGsjmxIbn0g803L1eT5VKJaSE5PXAxDjTJC0cYpgmNkuq/LgU0aWGlCx2mQYMmgNWKHzyP05PT+MgVBwBJCs4CbR1q9XSaDTSjRs34vu0PdekrC6bDG1LNJRnLZfLqlar6na7arfbarfbOjk5if6jTbzaG2f8SAqAxtgkWu4OQalU0tbWljY2Np67vr1er6tarca70Me9Xi+q/q2vrwfLigTGHSvAnwciJpNJ5CQ5Y8n4BtQAnGCvaCOXXDpQfF0tm82qWq1GVbJHsQTO+L4K7FUmk9G1a9fifJp+vx+5iAC2Uqmk+Xwevx8Oh9HHxWJRm5ubwXhKF+9IdT+XuRUKhVjTCexcRbrpASnGDQxsr9eLe70qLNr5+bmOjo40GAxi7Wc/IudxPB7H+TrsB6PRSJPJJNgfP9iTvcfZaPalwWAQASeMNRaVQ6/XU7fbjWCEszgebGAvZj+tVqsqFotRGp613YHY6z53X4SVy2Xdvn07KlseHBzo937v997qtjs9PdWf/Mmf6NNPP73yd5ZAZ2mvpM3n80g2IypOhB0wwULOYopT6FElv16aBWCx8AgVlaXShy6mdc3p6LYzL55rwiLPJnNZLgfmDgxlnGENXHuNdI37eYQO/TsOAUn3PA/3xlmkzCesAyWgeT7AAk6Dgy3awSsWzefzKKbgUgkHQJ5Xw4YKIGADRPbi/c7/0+a0Ge/GNQG7bPKACM+3oS+5t6Rw2L0qHo4a44Tf0z/0DW0F6IFt8zHC8wKUHSwyZgEGzvzgSHB9l8A9T3PgxfM5EHFpEiAmPaYB+pS6Za44e+Esjrcn/UZbIENkPhH5f1xu26tusKv86euHy0hdWvkq2crKijY2NsIphzlmzkoL9pW/E2zgvRk/DmDS44gzo1iLrtIOgGfycsgbw5mHpb5szPJu32Ssm99GyoZMlEOoAQqNRiNYXQdlrB+MifTzsj54ro2PK0kBOnx+8T32QhgvX9vT+1a6v3gWAmvpKqV8hgDb0h5vyILTyoe3mQ0jIPDgwYMrf2cJdJb2ytq9e/ci8c7lZjiI0PpEDn0DRUvsyazOBKAl59R7B0GwREiWXPrFGTFsDg620rI134hGo1EioVxKOrFSEuicnp7G2TBIvHK5nM7PzyOvBMeIKK+foQDbc3JyovPz86jexaZE1LDX60WbAfi4HlF62hwWiGpFbMJsfum8G6L2bJxs5Lzf+fm5NjY21Gg0IiqJRp/ywt4PgDjfpF3e4lFHzvnwAypxsrBSqRTRS28zHARApld3wXkiMsznGE9cE+ADcKZfVldXg83ivAzalORoHAKAqEdD/fyKF2E+tnEQkcvwzjBWzEmP5nNIY71ej/ngbecyNoAoEWfmJQBza2srcZBov9+PceeO9etkyATX1tYiyABA5j2p2Pi058g8T8tms7p165a2trZ0cHAQ7wCAcbkS7DdnNDHWc7lcMM/ko/n5VrPZTLVaLZjMqwJ95irBCuSk5G1ms9kIorn5+WDfBKhY25m7T2Oz2UztdjtK/efzeb3//vuqVCqqVCoaDoexD56dnWl9fV0bGxuJPLe1tbVoK4J0DmAIODkrmD56AODpVdoINtCHDnRgcFZWVhJjlbwqghGoHzzvCmb4bXbYr2IEiV7VkvMvw6bTqb788kt99tlnV/7OEugs7ZU2AAzUPFE41/xibKw4ZUSxPKrEok/+Bs4t5lEuHDeeg3/zJ6yRO2T8v2+e6fwdNgDkCdzTHTVnUNgokJZtbGwEKOF7bFL9fj8BfiqViubzeRy6ysZCCVEAWT6fjzKuOJwOpngmZGgAIimZy0IECuc4k8moXq+HhGw8Hqvb7SbyaNbX1xP5Uefn5wGwvD9w+D0/CEBGe+PApCsHOXvkjB8sEdFkgG0mk3nI0aFNYSV4h0KhoEqlomKxqF6vF44C90fe5+OP/vOcKc8D8ucGoOHEvChGR1ocMrq1taVMJqN2ux0MF6ARJ9Z1/jw/7c0cTOdY0PbeZ8wjxh99ACjl0F3mD4nqlOh+HY3gipQMpjBOX+XKcsx9AArPTQDBQT7jH+fYmRBnNr18OuwhJc+fxAi0AK5KpVKUY0474NiTVL5jvXtakE0e38nJSZyNsr6+rkajoWKxGOubV57DWBORL3uAADZFWlSipE8IxPhe5cEtghjck6CUs9L8DkmhM0U8W1pF4O/c6XRiHVna0r7Jvi2jvQQ6S3vljSIEjUYjcS4Ai7rnqBCBlh4GJzi7LncD6OBU+qaFM8XfodyJduGk+328LKg74JICbAASqC7kDr0nbLMpoc+VFpWCvGIYjq87EmwwRNJhX4i4o+dm8yuXy1GhiPdIRym5LlF32oofoq3Isrh+uVyOymrtdjtO3IYV4nwhT0omyu15Ge5EOxuGA+3OopSUREmK5yXHhuhkt9vVdDoNUIcDT7Qy7bzjtOEsMS4LhUIUOPBIKPemIt/KykqAd4CAn9VD/2M4FLPZLBiVF2WM8b29PdVqNd27d0/NZjNRWMFlfvyOjcmdKv8s4MfnHr9HQkrFJoAl44s5SD91u91wYF81addVLZvNJoCaA50nrQr2oo01pFKpJPrFJWkEL1jXACAO4jw30tlnmE2XEF/FGI+j0SgY3kqloq2tLdXr9Yecc+xJ5te3lazBbu7v74fjn8vltLOzk8gbZE2eTCaJ3FAvsuJsCmyqpGCB6/V6HEA5n8+juqSfSQezzX7HOuUSZvZF5inrIaCPfkoH+5zNPzk5SVSrXNrSHmesMUugs7Q30mazmfb39yOBuVQqxULsGxWLvy/UnjDpG6ov6kTi044bAModMyJXLiXypGl3ivkuUgQ2TxxgIpeuzfdJvLq6qnq9Hs/sGwV0NlKOXC4XTAJskbePgwLainfB4Wi1WuHsE1Elgo6+3a/hPzwfbcu/cWKRxeG0VavVSN6v1WpRHhjWh88CXIgAe7vyju4ceV6WA0bahKizRzv5TDrXKb2gsrm7JAcpYbPZjOsMBoMAwkgzHLQAElm46R+AHFFtDoNlvBHRPjk5iXZ7kUYeFgDZJaTn5+fqdDoxfiQlDh+kvwCZJIXz3pgDdlhBgE4aTDlD4FHlFwl0XOrKOKNE9nA4DBnjk8itMB9nr4OtrKzo+vXrUZrcmW8YutFoFIENnGSfd4wTr+hFH3t+11UNJpQ+ITmetYK182UBSWSxFNnJZDK6efNmtCHP3e/3ow3dWL892OR7GIVbnA3KZrMBqFjPPejDuuQqCK7nIJOAjReS8YATgQ6ey2XGXOtVKQKxtFfbstmsfvVXf1W3bt1KBDKfxJZAZ2mvtM3ncz148EDz+VzvvfdeRHSdbsdhZOGFpZlMJrHgwlqwwLKxwrJwSJuXyyXCjkTJ5XDu5PN3InvONkmLA/akRQlZlz8BotxWV1cTzqxvNJ6r4WDESxr795D74ag6qPKIqV8PgEIyr5+H4u/NNdzJxBlFMsEBcwARQE4a6KysrKjb7ca90PG79MmLFQAyvf1d6uTP4Tp68gHSgM1BzmVAh3MmcEDJ1Wm32xFJ5T1wep1lo629/xkvSNMA5QAdH0/9fj/ytl400AHY4KjyXvRHp9PR6emptre3lc1mYw42Go3oL+SMtCV944EHZ10BVTjDLouRFgzdcDhMyGdelAHmWRcmk4l6vZ5OTk50cnKi7e1tNRqNaKsnMdrhdbFsNqu9vT1NJhP1+/3EwZcOAD2njTXB5zUghzm6tbUV/fykoA8wxZEEgBx+yIF5WQbQ6XQ6mkwmyuVyunHjRlS+PD09VbfbDaCTziVifYPVd+moBwXI0ySoQFv6/sna5AEYLxjCtfi377kASt/3mMOXgVT6e5mf82Tmwc63ybLZrD788EN997vffer5ugQ6S3ulbTab6d69exoOh/qN3/iNcLi8shn5JkR+kY2xqVKUwM+dYSFGEpEul+lgJi0nY0H3TZONhd+7A05pVa+YhWPtzMvjjFOxB4OBut1ufJf7A35cPuIMDM42ziXt4GWPyQHCmR+NRlHtB5mVHwyJ4wFz4RIGnP1sNqtarRaAqdlsqtVqxaYNy5LP57W5uRn9xsaJfCwtMeTeXpSAa00mk7gHEruNjY1EdThAkpQ8Udwj6TgHaOdx1D05n3HlYAkpCf9G3kG/p0Ex7eBMH5/HSeaevV4votC024s0CkwgbXQmJy29Q8svKaqueTl0z29g/nDt09PTKIWOk0eZW5dMcs/xeKx79+5F2eIXwewwVrgXh94yZx48eKCjoyPt7u6qVqslqmi9qZbNZkOmenBwEEwFgSby3nxdheHM5/MhS2aMs34i2UKqdRXg6Ey/V0QE4DzPBG/G8uPAmQdnWJ8Y2xySenx8rOFwGDktDhaYPy5flvQQiKCMP99jTjr7AsBh7wB4snfQ7n6AswMcnp3/R3VQLpcfisIjYV7m6DzaYPU52oBCPW+j3C+bzer27dv6+OOPVSwWn6pa3xLoLO2Vtvl8rqOjo9gAcQSlxULt0i9nenAcSXaH8Tg9PQ09f5qiB3jwu3S0GXaCiJbT8XzPE/Nx2omW+cbnQOebZDdnZ2caDAbxIyUPkSQfAsbCo+WueUc6xcZFThH5EMg5KE6AlOT09FS1Wi0SZF2Hzb2IbntuC3ksaMTJr/BN1OVQnJhOVTjACQ4TlgZwDmpPT08jcRmGJM2AOAik3y4DOjiv7pTT5rwjzA2OgpcS9/ZxUEs70kewJF7JTlKCBYPN7Pf7Ae5ftOOMo8oY5PR15oMnITNGGBPSQkYpLeSGzjC63AkpDb8n56DRaCSYOxyuo6Mj1Wo1bW5uvpC2cMAqLeYo6wySJOlirULK9iYbY3k8HkdgiXnKHMFYCwkyIHGdz+dRdIM55ECHMfVNYNaDIjDLhUJBhUIhAjrP075pXfdgXLVaTRRwYe3tdDoajUbB6vA9KXmgbrrqpAduSqVSVL309Ym2pu1diursAfPYz3VjvWevoTJYuVyOdZH9KF3U5ezsLJj7pV1uKysrqlar4aewD3nl0rfFMpmMbty4odu3b4df8qS2BDpLey1sPp/HQW/SovJL+nwa13575R404vyeHBRkUizYLOTp08qRqfi5M1KShveqODix0iIRnT+R2XBuDQCuUqkktMz+7tJi8aMc83A4VLVaTTiRvqHgdBDRhHFy6VcabM1ms2CNOAuhVCpFRB7NO4nxa2tr2tjYUCaTUa/Xi+Rf2iyfz0ckslqtajQaqdFoqFKpRAnidrutarWq1dVVNRoNZbNZffrpp5dG/XCMKc98cnISUgnK8HKeB4URkMm5rM9zsHh/gIxXOKMNcZZms1lCStLpdNTr9VSr1VStVsPZSAM3lxU6m4SjQfEFxg2Hk3pyPiW4Pc8LgPS0CdFParQR+WOAUaSTaRDjzp6zXunAQjabjbmQyVwUHmBewgJRjtij0rQDjpuflfKi2gQjoJDL5QL0r6+vazQa6eDgQGdnZyoUCg85+Jubm8E2vAgm6nkaDC5gRUqWfsdpZsz7mkrfk0cnLea75+gRaPqmtuKaxWJRN27c0N7enq5fvx5BneeZ/wTAuuwZZ7OZ7t69q16vp+PjY81mM21tbcV6wfohKZj1fr+fKDTgeS5eWAaZJ/vRfD6PICHAHIaFYA7sPlJvV0x4/g/n17HH8TvmHvvY+vq66vV6nHuUboN2u60/+ZM/0Z07d55b+7/uVq/X9Zf/8l/Wr//6ryubzer4+Fj/8B/+wycqqfymWDab1Y0bN/TBBx+oUCiED/gktgQ6S3stDKcHLTObHo6qtIg8QbHjoLIpILchAsbGQHSKSCHOk0txfHNxUCAtotAwJFRiI4qPxMjP0WEjkhROuW/w/t7chwh+sVhUt9sN55zTsV0uh6Wr5bjELh25417o6fk87QUzRdED2pDD67xiG+0ymUyiEADPTnlX2mk0GqlQKGg2m4UjiFyCzTqt0wcUpHOqYKBgaLyymzvI3ocOJMnXOj8/T+QqObPn51FwPgeA0/PBisViVMdbWVmJAza9jDbXBQTzPDBY9BuOCGxOqVSK9sYRfBHGWMJZolIYYJvy5J535mAnnZQMyPH5QmTbfyctqip64jN95qziyzpXx5lFz2EABDKmYBKYQySDv85V4zBAKiwn1dZ8/rhcE+fdnXb63plBLF3N63HGvdbW1tRoNLSxsaGNjY3n8t6X2aNAznR6cb5Zq9VKnCfFeuZ5MswNJGKsh/yOtW42mz0EKtiTOIcuXXof872N9uY6Pmfn83mcb8b/8ZwEl1gDWXMvA3uDwUB37txJnE+2tKQVCgV99NFHeu+995TNZtXr9fTpp5/q8PDwZT/aC7dM5uKIiq2trae+xhLoLO21sNlspqOjI+VyOd2+fTvh4LijIy2igICJ8XgcThGyI4/+zmazOFOBTRiHGGfUmR2ce0+i9bNgcGQcOEiKiJmkoO3z+by2t7cjMp+OMg4GAx0eHibkS2xylDFmsywUClGxBwkDnyeXgvchbwkQQgSRXBgHj7ARGBXCvIoPbMfKykrkVuDw3b17V9VqVTdv3tTGxoY++uijiFIOh8M4Q8LlG9VqVXt7e+r1ehFxBGRJF06SHx5KRSU23lqtplwuF7KNw8PDhF4foORSj1arlQAZgC7Gkx8+CMMym82iilw+n9fW1pa2t7cThStICB4MBom2TksjGas4MwBbT/ynMAOsG4dxvqjEdRyfcrms9fX1OPCU/0P+4gECZGUwHgAY+gHWjTkJCCZSzXiqVqtaWVmJgxORNw4Gg4TM5v79+6pUKtrd3X0hbYJ5ud109T/6zvPbeM9GoxFj7ezsTMfHx1pZWVGtVovx+roYY4C552yjB1i8OAHr8XA4DAcZJg+pDoxyr9cLJuGbGBnGmRc64ODmF92mvPunn36qk5MTHR0dxTqzsrKik5MTFQqFWDuGw6G63a4ODg5CieDBsEwmo1arlSg845Jagj6wm7DerEnO2FDYplgs6vz8PBh5AA7BBaTIuVwuAA/rXKlUSjwDgZvBYBD7o+d+3rt3T+12+4X2wetkm5ub+uEPf6i9vT1ls1nt7+/r7/29v6f9/f2X/WgvzebzuVqtlo6OjhLVOq9ir88KurS32ubzi/NOOGzSF/Q0i5GOHrrMhR8/gV1a5EJ49SvuKykRRWTBdpmO5wb5swB2AEoUQoDJcA35Zd+dTqfqdrsh7XKngcl+dnYWICifz8eG7lpeHAeXI7DJ8gMzBRvCe+OAex6UvxffIQIL4AAY0GeSAgw0m0212+1gJXgfZ0ZKpVK0G86K38sLQiBHdEnX+vp6yIgobesAx3+y2WwC5OEQuGPKhu9V+5A5cv9yuazNzc2EHA4pCDIvnFdAmqRwyABdjKu0LA0AjwwOEO99/bxZAZxZn2f83nNzsPPz8wDjfsaV525QacrlL/wACGq1WuL9kboRNIAh6PV6Efx4nvKky4w5wPjxfAp3HAGx5DEAjM7Pz9XtdrW6uhrzwMfo62AeUCDAxHrhwI9KaA5+JQWbKynmuwdmkBt/k7lU1/cC5tWLNJQGR0dHevDgQQRJYLsIgrEuM74B9azVno/qATVnTAHULqOezRaHFvvchLH2Pc0rCabXEvqVOcfY9PWMz/AMBGkYv+ToELRa2sOWz+d1+/btyC3t9/v6/PPP38piBNh8fnH2E0HbJ7El0Fnaa2FnZ2f6gz/4A929e1c/+MEPtLm5GTku5IAg/fANAEfcHTBnLJxFwcmSlGA4JCWczfl8cRibO104aDjD6cR1NrdyuZyQ6/R6PZ2dnYUETFJIDiaTScigyFGQFM41z14sFlUsFtXv9zWfz3VychIbpG+CgLhWq6VcLqfNzc1wwMj9YLMkD8gdN3f+XcIFCGATI8GWvut2u/ryyy9VqVS0s7MTjqgncKNXJ3Lo0f6Tk5M4UwKHJZPJREW37e3tuBfAp1gsqlarJYDK6urirB7GAEnK9CUOAePEywPjCNAWSPEajYZ2d3cjz4qEelhDz9GiCtzq6mokrDebzXBAVldXI5ovKYozuBQH54Ycp36/H04jxR+epw0Gg5DTwE5QLQpGw5kL8jGYT152djqdqt1uR99XKhXV6/Vg5Ri/JDozxyqVSowtL/iAjPPw8FDFYjFx4O7ztHq9nig2cHBwoGazGc4g60C5XA7WkvUBm0wmkbvQbDZjrjUaDb3zzjuPzPt4lQw5GvlpXlAC1htJ72y2KDXOmutz2Mf66empHjx4oH6/r729vW+smnZ6eqpOpyPp4qBl1j3Gw4s4p4j19sGDB2q329rf34+5DmtXLpf1/vvvR9I+Vcna7bZ6vV6Mf9ZXnzeSopw3lT3JP/KDsgHWgChpUViE9kWSzPckJYJQ7I+rq6uJvJ5sNqtGo6FcLhdzluIEkkJBwDg4PT1Vs9lMqASW9nij6urb3GZnZ2f6f//f/1c///nPnxjwLYHO0l4Lm81mIelAuoATBNDx/BoWZ89B8dwALJ0z4I6+pHDgMZyMdDECB1P8nk3Bk0TT0XckS2nHFBAAK+CFFXhunABJAbJ8o/McFGe9kHHAusDS8IwANGQROKzeDjy7M11s6ji7AC3+BAB5W3r+FNH56XSqjY0NlcvlkEjRlmn9uL+3O/9IMahuBmPF97wAAwCIP2kTwJCDnHThBiRuVHKiH12m48wfP3wWAANowDngGRjH3hceoUaHz2fJc3re5vkCPufoa2d8PAoMQPWIMVIm/g2Y4Qe5pY8B2h6njDaCfSSfCZldmvV9HsbzYjifvBdAhzLtHtjAAGrMR94VlsNB96tqjEOcZtYNfu/9yf87K0Df+nuyphBQehJGh/HE+HSW53kboK/f70cFNeaNtDhjrVqtRuEK1lBYeEmJAJirDVjXCOj5753Bv8x8fEnJ9Y3+4/cue+YerG/ef6zFjHUvJuEBRYDS0q5m7ANXzU97E202m+nrr7/Wl19+mZBsXsVe7RVzaUszY5PEya/ValpbW4uILk4Uzo/nz7gTgnPGJuCbLP/PNdh4cMDI5SGqReI693LZGxKq9POjO+da6aguz0fEzZkbrsFn0lWJiCrv7+8nDrNkU4OJQupGoqrLHHAK2Wh5b54Np7xSqWhtbS3kJGxsLr/COaGARCaTibNuPJeHAwbZZL38KHI8tPo487PZRdWz8XisXq+nQqGg3d1d1et13bx5M8ALyeD37t3T/fv3ExIyZ19wLmEGKcfK75Ea8r1s9qJi1ocffhiJzjgVPkZgbXyjotTwyclJsEeMCYAdrB5gl3tLiipMrVYr8pOkBXh83sZcoj8AOL1eL5wixrC0SCLHyfHcMMY/bUKEv91ua2VlJXJYfF7h2JHrgyOA8zQajeKH56Bwwouyra0t1et1SUk5ISzyZQ7o6uqqNjY2grlD4tXtdvXVV1+p0WhoZ2fnRb3CUxnzDkbTI/v0M2PBc9b4rrMMHrySFOfgXKXQRC6X09bWViL4gSTQ19Tnaffu3dP+/n6sAeTUwWBubGxoa2srKjZS0RHWmbEOwIOFms1mqlQqqlarAeylhcLA83D4DgEsDmgGcNFfuVwuiruglJAUewvXWF9ff0j6TX9yz0qlEow6fUpw5GnKAy9taaenp/rd3/1d/cEf/IG63e4TfXcJdJb2WhkghMpqOPEuq5IW0SmPBDtz43kF6et7Xo47uoAjj0ymI2x+3UdFkNPMAL9Lf58N6DKJhudv8GxIBCTFps6zslnxLnwXp9Qr6fCnvzvflxaHXAJsODDUS7562wBAARyAQ8Aa0T/ewfX0Xv7bI4do9mln/o5Du7W1Fc8CKOA63gZs+LAi/kOfOoh1tgI5CJu6szn8OIii3wB3l2nscfwZqzjunt9B+5JszDWQyfGuz5PBSOec4IQx3jw3w+dsOlrs7+TMIQ5RNpsNJ/Cy+6f7C/OcKMD8ZUGF52lphudRa44bxTxY1xg/kl6rKLiDbc/NYf4+am3x9VtKOtnMDz9D6XGG887c5buXFX15XjYajdRutxNMjqsIOGeGuXJ2dha5dz63WZ/Io/F5x1rqTJUH3TwPELBDcEVSou19HabfYMUw+oNAjt/L5zkBBu9Tz8lc2tK+yWA8AffNZlOHh4cPqUu+yZZAZ2mvlU2nU33++ec6Pz/X7du3Y3EmCra+vq5qtZpwZtMbo8vaWOz50+VNbB5E+SVFlN+pU+7DpsD3Kf/JxuGO1srKigaDQeTmIJViI8aJThvXoqIRRv4Hmx9yKjZRosO0F8nfOP6SQu5Dm+GkwiyxYaWr93Q6HZ2fn0f0DkfC5VVsmpQIZzPmXjzn2dlZQu5zfHwc7T6dTkP241WqMplMgA3+36Vcw+FQh4eHCbYBIOBgEmcDXTxg0eVRtGWhUFCj0dD29raq1Wr0FaxCt9tVr9fT4eGhDg4OYsFGkgcooD24FzlFADtPaCf6zT3oS6qwwdhxGOLzjlr7nGI8uhPL/5NbgeSUqkzSBSApFAoBTB0oM14rlYo2NjYeOmyyWq0GqzWfzyOHjYpQOJckr56dnel73/uePvzww+fWJo+zqzjnhUJB3/nOd2J8MnZZO2i3V9Vms4tzpkajker1uobDYYL1lhRADrbN10UAbpo1Zp42Go2ovvdN5ky9S6keF4B6nNTraYzqmMjWeI96va5CoaB6va5SqRRSta+++krtdltffvml5vOLXDfWMGTJHghKy2h9bSdIxNoqLQIk7BOAEwdJFG0pFArBynkQTlKcgTYYDCKHkLWbIhuU9GYNmkwmunv3rk5OTp5Z+y7tzbVMJqPbt2/r5s2bkVfH/v2ktgQ6S3utbDqd6v79+1pbW9Pt27fDqU5Hlz0i6FV/HmfpnBaPwKKFdoreHeV0dSxpsXF6bonne3gOC5Ewl2lcxjZhaekLm4nnRPCnb95E7HyD5PsuGXENO46CO7Dpa9Bmzpo5C8K74ODwf56kmnZCiDoCMtKRSn8fZ9xIUOc5er1eJEY7c+VAl3ZzEMy/HRi5lIaDB11OQo7IYDCI8sdIz2CbSKr3d/GoNe3k4/VRTKHnHQB2RqNRMAkeoX2WoIc+SYMuIs6MbZ7VgY/n9PBvZ3/SPy4N9Vwx3gcmy5k3vsvGiBSHceBnWr1KBiuJIWf0QMmrbM4s5nK5RD4G84ix7awpY8HXFL4Dm5H+7FWehSqX6fy+523OlAOyncUsFAoql8sRoAIQ8YNM1s9jSq+P7EW8K+b5fRQQYK5ks9lEkRVvd88z9bUuvfdNJpMIojg7RXsTvHEmW1pU2nvS/Iq3yRgbfgj4aDQKGffbZkh1v22AZwl0lvZaWb/f1//0P/1Peu+99/RP/9P/dOQ0sEC4rMnlSCSF+sIsKcGKEGF3uZDLCviB4clms8EEUNqWjdk3Z68wJSlyUXCUy+VyVPCiyEJ6Q/bcHCkpH0u/k7SgfIvFYkgFcKJms1miaonLx2AMXIO9unpR6pkD7gAnXj0pm72ovJPNZtXtdhPMFlXAONMG/TnsA0nWSCrSEiMiu+6sHx0dRfSSZxmPx+p2uzo5OdFPf/rTcKgHg4F6vV4kh8P+EPH3stSAUTZ+oqouWWOswSyVy2WdnZ2p3+/r5OREJycn6nQ6IZna3NzUO++8EwxQLpdTt9uNSLfLTYhkn5ycJMAmzweoculWv9/XeDyOcr1EjJkbMEiM2WdhgMeTk5Mo3Z3JXFTBg0Vh7OdyOTUajZg/XnochxeQ5rIYgCs5Djdu3FAul1O73dZ8fpEHMB6P9cUXXyTOCyEIQaU3nO1er6e7d+9Kkm7duhWV+l5ly+fzunbtWkL69SrbbHZRNGY8HqtWq6lUKqlYLGo8HqvVaoX8hHLhBAXSwQzWoG63G0zg+vq6jo+PNRqNdOPGjW+sujYej3V4eKhGo3Gl/KxnGQhAYtNsNiP4wTzJ5/OxHlA++Be/+IU6nY6Ojo6CJUGpsLq6qmvXrj0U3OKaDngk6fr166pUKnHODgArn89rPr+o0sgcoR9wqNMSbfZA5hGBAorEUCjH12EPLLK28y7NZlP9fv+ZtfObZsViUd///vf1a7/2a1pZWVG73daPf/xj/fKXv3zr5H6ZTEbf/e539U/9U/+UGo3Gt7rWEugs7bWy+fziXJZ2u63BYJBI7MXx8w3To3hpQODfS8sWnIFh4XfmAofM5XGSAuCw2Pu1MGeFiIAh28IBvAzopM+QAYR4dRvuh8zF9dGwS2xg3g48M5sVkXMiorQ9uVFcM81GuEPm0TyuzzX9/T3Smdbu+3lJ3pa8z+npaTBXkoJF6ff78Vw4BOkqfP53zxVwxsgjm15JzKOYONJEYsfjsTKZTAAhgAeVlbLZbByYSvTbmQ9n2VwC6JJJf/Y0uPb3pV1glL5tVNuBgwN7r5r2OPaI9qSaGtfg0FmCB0TDme/j8ViVSkW5XE69Xi8ht+RZfLw5W+YljYmcD4dDDYfDxPrxKoIIZ4FfByORnTLi5AfCgCIxdikZ7+fMXTrvhAg3DMk3sfPYkzBAz8IYcxz4yVlPkmJsI0HzowgYk7wXATTmUzrQRts58GF/8DWFfUVSgt33dnG5KM/p6yygiHs5u+Nrv++HBPH8+ZD0ps94W9rC1tfXdevWLV27dk0rKxdHUnz11Vc6Ojp669osk8loa2tLt27dulTG/yT2+qygS1ua2enpqb744gttbm7GhoDT0+/3YwNlU8jn86rVaolNAGfU8zAkhaSKhRtHzCPs0sLpJirW6XQ0GAxCKsGGgLOKo+2Vas7OztRsNjUej9VoNB4pTSG6SXIyDiIbpkctM5lMVNZ58OBBQrJFbovLvHgmpFVp6YiULC7ApoYcg2sCkJAvkUTtkgjACZskUjacUSplETHkzCFyidg4YWPIX6CEMEwPzFQ64nnZWR2eE+U5HhhOB4wP7UceSLvdVrfbjRObp9Optre3A9zQPjAXvV5Pn332WUR9p9NpSOBgEyk9zCG2nU5Hw+FQR0dHUcoaRyct2aJaUqvV0nQ61a1bt1SpVOLwzcsYw6saTJUnIZ+fn0d1OPrBgwOej8EBmVtbWxqNRur1enFmCG3AdXu9XqJ4RK/XUz6fjz4FeBIYcCYWh9rPM8pkMnG2yr1799TpdCJXgvOYlvbtDMYGhk5aMNo7Ozvq9Xrxf+QSUmaduT+ZTOI7Lokl/+uqVbsYby9S7kee44MHD/TVV19pNBpFMKZYLGpjY0P1ej3y6GBEDw4OorJatVpVtVoNluX09FTHx8dRkpx3Yi1mXSYf6s6dO8rn86rX64kCIbSpn0HnewGSNHLdstmshsNhVM1cX1+PXDxAUDqIyJlv5FX4sQiDwUC//OUv9eDBgxfWH6+bVatV/eZv/qY++OADra2t6eDgQL/zO7+jL7744q0EOn/6T/9p/ZW/8ldUKBSeuNKa2xLoLO21tOn04pTpbDarnZ0dzWazRKTII00s9ETPXLcMAHHHTVqUw/XNls2Za6dzc1xy45pyBwZp8CApnLF08rkbAAJH2qsxpQGYtNB0pzXUbjwXbYPEwh1V//HP8x7pnBnM85i8eIEnzXqUkD4CfCDx8IMn+S6fhX3y6zkD4+/pUfE02+Djw5ks/g0wcDkeEqzRaBSSPhwHkvLRtnsOyXA4DA07Mg/uAWjxvvJoOG3A55zBc00+Ek3AOY7+ZDJJOCa02TdJdjyyzrswlmHdSHwGOAMsYLC8tDtRf5dUujOLE0VSOmOR8e4lcLPZizOJ6GO+xxwhgo2TTL4OieGUJ8cRfVFlh99Eo6qir2eSQobqQQY+4ywFP84c8HeXsn6Tw8eaLimA1PM21g9ywZCMsZ4zz8m/YC7TZgTpnG1Jr6/OdDFXPKeJ3wFimJ++/rlq4bIDU136yXX9/5jv9IEX9fH1l/7ygMrp6akODg5Cerq0h211dVW7u7va3NxUJnORb8lhs29jm+ELEEB42qqTS6CztNfSxuOx/uRP/kTvvPOOfuVXfkWz2SwQv59Aj9Pqzub5+XmcI8OGwCSCQSCHBQeTxdtBAxEuIsXkBbi8jGj9+vp6RMd8suIMs6g9iqLlGicnJ2q32yG9oWLXxsZG4vNsenyPyLgnjuOIUs4Up52Trb1SEuCtUCgEk0E+SRr88T2vgodcyyPxaTA1nU7jkE/OlnBHCGd3NpupXC5rNpupVqslJHTp/Bo07l5q20EozgOOM9+ZTCbq9XpxTUqpSgr5HlHQVqsVTlqtVlO9XtfKykpicT48PFS/3w+nC9aFDY0IrecrSIvkdPLOkLmQu+B97XkNsCU8Yy6XU6vVShzohxP4uANGvS/H43HkAzGOuRaghMp2yJOogre5uRnt0e121el0QvK5uroaeVM8E04gkjU/u8MrPK2trenGjRvK5/MqlUoaj8dqt9sB2nO5nCqVSrAEZ2dnOjk5ifF0cHCg8/Nzvfvuu6rX67p27dq3lkm8jXZ+fq6jo6PI4WAtmc/nsZ7kcrnoU+YsDvFsNovoPwwhc5d1Np/PXykpmxyxXC6nnZ2dFyJJJAes3W6r2Wyq2+0G00I+5vr6uur1euIcm3a7rX6/r16vF+w47y8tCrL4fgLggIXudrs6PT2NHFXkuwStkAkCkhx4ei4rayQHcDNH+D1SWAIKfJZ1kXxTrs++gLXbbf3xH/+x7t+//1Y67VexYrGojz76SNevX1c2m1W/39dPfvKT2C/eJnP1xP3793Xv3r2nLsqwBDpLey1tOp3qwYMHicMSASUsoki8iHB73kw6VyStNcY8h8fzR4gQO6uSLkLgn8Mx9NyPNGPiTBDmuUE8O0DBWQhnIKSLTefs7EzFYjEifJJCtkdb+Y+zYX4/NlXe3SOEyPzYDL3SULrCG9F7NnPAiEclXYbomnVkZbQT0i5nMhxocVCnAx3GQzqXCADr+n///WWV6tJ5BC4jwxEBaKdzR6TFwYjp/ARnzPg7TAp9yzV4nnQOh+v7YTp5V4AnVfT4EyDP5/jBYRoMBonoM+W+aUfe13OIPP/KD2R1fT9A2pmcdB94Wd3pdBpOIfdFhgiQ8XnkEWYHuYwR5haBDfKqrlrCeGkKeVWr1QqAQiDI50s630RKnlWWZmf5jM+Dq5TYviz/5HmZBx9OTk7U7/cTbcD7EMAplUpRttmLFLA/eHVLaTEvmBs+HxnnrJvMew9qsf7RLj73vJ2cvfG2l5JnZjFfnIVl/+FZnEXms+yBJycn6vV6z7VPXnfzKnnO7L2Nxlh88OCBPv/88yXQWdrbZePxWD/+8Y81m82iuhTnNxAN7vV6saEQYeJPnBycKq+sJS2cvUKhoPl8HtciUthoNBLJ9O7gwaIQSSMplU3AE5/TACYts3BJnifmAgJwFE9PTzUajSL5ncTt7e1tra2tRRJ2u90Ohxfn3Ktg8e5+f2RkSOf4fyLsyH08sZbvea7SfH5x1snGxoZ6vV5EqXBgAUcrKytRuYyNtF6vJxLrb968qVKpFJXe3AB2d+7c0WAwCOkSbcQGzVhg/Dx48CBxGjjOLmPDpY+MG/7Nc5dKpWA60NkTCc3n8wGEYFFwbI6OjhKSSndWYClg6BwEcS+AH3kJmUxGvV4vrk2+Vi6XC9YCVpNCBbVaLQHsGLsnJycaj8fhZJ2dnQXYQKJXqVRiXtLX5NnAKhGdJ0pNcQWeQVKi9CzztdFoaH19Xb1eT6PRSPfv39fZ2Zl2d3cDpLg0z9k2l5dy8juyKhihtbU1HR8f6/j4WJ1OR6VSSR999NGVKnW97TabzaLdPv/88wDEmUxGGxsbmk4X5xylASjzlnFwenoa7KXn9wBgM5lM5CI+rkCDBzKep8EcDwaDcMT8HCACHplMRpubm1F1sVgs6ssvv4yiOv1+PyrSEcTywA1tB+NL4RPPLQSgSwqAAeOOLNZl2VLySAIkn95H5PX4EQ6AWvYZn7cuq6bqItWyJpOJut2uvvjii7eSnVja09v5+bn+8T/+x/qjP/qjp87TWQKdpb22hszo8PAwktdhCTxvhEWaRTvNiOCoemTKzyLwqGM6twe5k7MdONF8zvN5pEWUzqNmbGJpoONMiOc54KARtXM2R0rmKGUymWArPMKNQ41MyvXcMFFssM4K0BY442mnw5142gCnHH03oNKBANHadETL5Rvkt7DgXQZ0ZrPFoYXpw/BcTkH7DIfD+Cz9433pxSOIhqaNsQYYoCBGJpOJss7OMuEI0abISLg37ZHON/I+dWCMnA7wRt6PPzvgDOkX4x4dNOMKTTTvgRyN8e5sCr+nrygA4LlXPnZ5Bhy0QqEQTBFzFrmbR4t9LgOsGV8wdWdnZ5GEjeyD6LL3nc9tn188H0GS+Xz+TEtyv2nGXGw2m+p0Og+1M2sOaxTjzhlgxrmzmKxBrG9IEOn7byqm4Wu2g91nZYwp3pl5wjjkmTnkc319PUows/cAhvz8NM+PYd577pqznF6Ig+9LSrSdA8n0Zxxwcj/mqH/uUWPfZXk8n3+PsuL+/XTOz9KWdhWbz+e6e/eufvnLX4Yf8qS2BDpLe61tNBrpF7/4hXZ2dnTr1q1EojSLqjtzDhCQT7nzChio1WoRBWPzQo6Bs0yknTwHNgw06bBJbNBevUtSOGKAB06MxhGUFtS1b5Srq6tRYYdzCTzxVEoeKApQoaoPlXWItkqLykhskl76lGpy6OSRJYxGo2AhPO+m0WgkooKAKUoIe5I/kVwkHeVyWYVCIRxqnCf+bDabymazqlQqOj091fXr1x8aE5yf0Wq14p5ERolAOhBsNpvhEOGAu5RqOBzGhu7fdWOckBsDY7C3t6dqtRrnYHz22WfqdDoJhzCbzcZp5uncKhgcxpYXLFhbW1OxWAydPHku9AVJ+pS4RXbGGO71enG/fD6vXq+ncrmsRqOhw8ND3bt3LyFVoW3Pzs6CMWy32zo9PVW73Y62kpQACLBMMEH0faPR0N7enr7++mudnJzEfCmXy6pUKgHiyLkhf4dzp2CuyGfIZrPa3NzU1taW2u12VMEDfJ2dnalarca5R75G4KhRffHg4ED9fl/vvvvushrbI6zb7arf7+vOnTsRwYcZwACO8/lczWZTkhKVHVdXV+PfrJXSojriaDSKXD/GTdqBThvXZs0gd+RZGMCi2+3q008/jb2BYAnzutfrqdlsRiDGq6BlMpkoLw9DwnpDQQIvwiIpUYjBE7S9pDTghrYi6MdnWO+8CAlBLyrgEWxwqRp7lKsPXLKNFNqvu7W1pUqlEtdY2tKe1s7Pz/Wzn/1Mv/d7v/fU11gCnaW91jYYDPSHf/iHev/99/Xuu+8mqqSxWWQymXAcJYWD6Xkmrsf3HAVnaHBE2TD8EEbXKxNJxJniml4ylXuy2QCqoP2RFrG5p/MFyDEAsLlu2s03tpWVlZAc4MCSqwMbhlYc9oUIP9F+12GnK+uwGSJd8Y0SKRgHlqadepLJcdaReQ2Hw3DSacvz83M1m02dnp5GOVOkhCS67+/vRzEBZ63YlN15dQ26AzR/Tjf6AhmctznlzcfjceL+gCfkbZ6TIylREICDT706EsDbzx+BUUlLDgG+ABwHsQA5osoAX9hRvku5aMAmRQfSleRoP8a6zxEvNsC7pOcJuT5eyYkxRlsD9rl/vV4PFpBxz33Pzs7UarXU7XajUAdAHOaAd5OUYFwZW+fn5zo8PAw5H7kVL0IO9boY8xzNvI8Bz82QFAwMB9pWq9UoaOLBJmdi+Df9VCgUVKlUIiDyuL5gjDGmAfsw1U9rMMMEejwYQ9l4WGmATa1Wi5Lrk8kkAHy1Wo0AFGDMK2ki6fTCGLSN72WuOoBJdUZIkiqVykNrhe9prI+sd7ynr/X0BXI4z12VFIG8UqkUJaidUUJ2urTLbW1tTe+++64++OCDKJJ0//593b9//60Eint7e9rc3AxJtKtfnsaWQGdpr7V1u139n//n/6k/82f+jH7zN38zoWnGqWeDW11dDQDCQuyaaEmxMeKMYYAmkqJPT09jcSdqicNEhI/NA0fP2Rg2CwAT3yNhk00dcJI2HAU//yftkDu4wvGEbaGyTqlUCjDT6XR0fHwcbcbJ5mmGiY0VAOYyvPl8Hs6PJ8ESwXcnlufLZi/OX6hWq6HB93wfJDJ8bzabJRibWq2m7373uxqPx/ryyy/V7/d1fHwc95hMJpHjAuPmhQKQ6QFafFOXFo6aO+KAAaKv9D05IPQ9Z+BQupjT0LlGugSrdAFUiH5zfxwez2diLLtTKSmq8vF7QBagFskg7wd4ociAO5nkdtFuRI0doKysrATw8aguAI1n43loKyrEkc/lgIPcIwARDh6/8znmAYnT01Pt7+9rNBppMBioUChEDkMul4u8CNhdL8TA2Dw/P9f+/r5WV1fj3BPWkqUtjLOKmMewEzi0gHHYY6oObm5uxryWlAhEMCcd7GQyGVUqFTUajQiEPI7RYSwgD/O8uKcFOsw7AkwUEUB2CYNTr9dVLpdj7eRPgJ504dRubW1pfX092F3AG4EGH//OrsB+w6CsrKwkzuqBjfHgxsbGhlZXV+PaMGewxb7WwEyz1nneDe/v+Ze0N4qDWq320Pk5KAeQgy7tYcvn8/r+97+v733ve1pfX9dgMNCPfvQjffbZZ29lm7333nv6zne+Ezle39aWQGdpr7URFRsMBqEBR3blFHzaGccRw1FMl8J0aZu0iJZ7FBtLM0BsFh5F9+iYy6bc8fcSvc5M8cw4npSWJiIKUCPy7tFzkuPr9XqwImmtNNFAypHiFPMM/B+ghI0Pp9ffjbbDwZYWybGz2Syifh5pRb7mhzsSRQTwURXL5RrI6qbTqe7evRtsGAcPYrABXvLUI8k48em2YewAcmkrZ4fSP/QVnxsOhwEMaLtcLhcODg4PbTUajbS+vh5lcb2sNH/i4JMITa4LYwjwxdgAMKPVJ4rMe3iBBiK6DmjpUwAn90Vm5gwhP4xHwCDRY5g6ihaQJ+bnjiAjJJkbh5JDRb2NpYuSrLPZLMFGMtaIfvucoC1ms4vy5DBc7lSSR0Lek0t/3nbj3KjDw8PEoa6ed4P8l3WS9TSbzca6hbPsOTqMcfoAZx/JoRcrmM/nMXYZ/4AB5Jyj0Shyvr6Nkds2Ho91fHwcJdy9AqHnyzF2mQusrfv7+1pfX9fh4WHsWQTJ1tfXtbGxkWgLzntyRpFS1ozh9D7HeGeN6Ha7ymazEYCi3WGFfG3wa7Jukj/IPkFQUFrkDA4GA02n0ygQwvPT31988cWyrPRjLJfL6Xvf+56+853vaHV1Ve12Wz/5yU/0+eefv5WMzq/8yq/on/1n/1ltbW09k+stgc7SXmtjMxgOhwn9vyd5phMipcVBjF45DObDdc047TicVP9xzbQDFyKJHnlzJ9DzMtx55p4k2l92yB33HwwGAXQ8WR+dtjt1yCbQh3O+g29WLkXCEfSKaTgMXnnNQRULMe/oIM8dZpyhWq0WzgFAB7CTTtTf2NiIXBCcfGlROpzIKs/gUdZ0sj9gB5bAZVlE0dK5XQDitLQpLW1Ms0G0MQwJ7Q275VWJYLQAsmtra9rd3Q3nB3CC44JTD5BIAx2+B5jkFHYKIXBvJGuAMPrS+5Px7wnWjNG09M1/eE76vFQqRX4BLBEOLRIoLzjh74LTSj4cuUcO5M7OztTv93V+fh7MAdX0vPgA/cq8J5cAZ51+dmeQcZyWuL6tdnJyoqOjIx0eHibmnsumYI9XVlZibrMewwLBELpsFoebtYa5CVPA7yXF/CIHht8RYODcJnL6vo3DCNDpdDq6c+dOotQ6QRjPjfG8QIIE5LCRr0SZaC+SgVQHsLa/vx9nQbGPUdTkUWsQY5a8tG63G8/B+kvbEvTztcWDKlR05NrI9ViPeCYAm4MkCqSMx2N99tlnS6DzGMvlcvpTf+pP6cMPP9Ta2pr6/b5+9KMf6YsvvnjrgE4mk9GHH36ov/gX/+IS6CxtaW7dbld/8Ad/oO3tbb3//vvhmLEJuO7Ydc2SEhFeotk4XSzMfBbnDOc1nY+Cc8mG7JWpfFNJy3V4Jhw6HFzXOjsz5eWKAXscCOkgCUcCJ5//A0ywIcHYEP1E1uO5GDy3b6au/YYdS+cd8X/dbjciuTi3bK6AjMlkEiWoSdCfTqdqt9s6OTmJzRUAybNRbpsKdOmqXw4qeU4/RFZasHVIY9rttiQF24NEpNPpxLggukw/ueRGWrA+AJ9WqxWAk34sFArB4nnhAZ6ZfvOcBvJMADjOuNHmvO/x8XE8A//HMyM1hHVx9g6gS9/zXuRbwZogT/L54kygjzuATLfbDTkYAIjxwrv7HCQAwf0zmUzkM/j8pU+QY/J/LmNinHDYKwAf55v3nc/narfb0Sd+QPDbaBQ06XQ64WgDqD2YQ24hTAVjEvaxUqk8xH46OIeVlxSHyLpMFiMnD+d9Pp/HgbSUa2ae+2G9T2qs87z3YDBIFB1xKS7rz+bmpjY3N1WtVgNwZ7NZ3b17N/IIye1hTWUtYP1kfiMNZZ7wTKyxaWMu876ssaz3nU4nCuggQaZAzdnZWQQWHKBxPV/XYdLYn2Cktre3gxGVLtbmn/3sZ/rqq6/eOqf9qra6uqrr169rb28vWOxf/OIX2t/ffyvBYa1W0+7ubvhByxydpS1NihOEP/zwQ3388ccRJcPYjF0uxgbL351y983LI/Ue7WZTWF1dDfkM7BBAIC0HymQy4VB7boe0ABL9fl/lcjnBSKWlUTBXbKwksG9sbMQ7s3ki2XKZiG/QnmNULBYjIosELK2f57rOgHnOh+vHva14736/nyivCqvABkshhkqlEtf23Ao2ckmJfA1n4B4HdGhn1517tJ++87OWcKKGw2EAB1g+wA5t63JHB9pITrLZReljoqY4d+QReF4WBRt4BpfEUSjCSz6nAToV0TwfiHHHczl4oMoVUXLYE89nAaRjzii5xGU4HAYo5XkAapJCmgTT5hIxT1L3OZiWkPkcpR+QPfHsXvIbx5E5w5jj/xyweg7RfH5RbvptNQps9Hq9OMcFhtfXUJxfDrkFBDNm0yCHNQV5o+cdwigQrPF+n8/n8RysExQF6HQ6wUwzX56m8hrXpWIg539RNS3NzgNI9vb2VK/Xtb29nTh89osvvlCn04ly1DA8rDusXYAJ5g57AcEHz/HkOTFn1QD1DnT6/X60mecYIm9zxh/Wm7XAWX36h3Ukl8tFTpuXtx6NRvryyy+XjM5jbGVlRdvb29ra2gq535dffhns+9tm5XJZm5ub4Vcsgc7SlqYL6vyf/JN/Eg4hIASHxbX7zjrwJ9FeFnjfVIkK4hSmHWqXbEiK3IA00GHzwUlHqkWUmGviJDSbzTgUk3uORqMo9wwIceYlvSDgfOdyOY1Go6hWls418jwiHBCPBPLj7wx4oQ15DyJ//D+SFElRECIt+WBDRfZxfHys0WgU7IrnBNEnPCcONRFm2ltagD0iQ14VCmceGR2bNkmz5PnwjoAQWAf62Fkz7s1zzmazhMyFMVcsFqN/eL+zszOdnJxoZeXiJHUqVDkrBGNYrVYTjJRHePk8UeNCoZAoi04/Mqb5d9rhaTabCYCMHGY2m6lcLiekgABNWDhKOvt9cbokRZEAAATPcX5+Hu1OqXHYPp6Na9AWfm9kTj6GeU/Pw6tUKiqXy3H+FvMGB442AZBdu3btmZ7F8joZ8+bg4EAHBwfBzFLu2w+Jpe9ZryQlysiPRqNgyZBpAnZZ+2BQfU7TX6yx7uyfn5/rwYMH4cRT9dClY0hUGXOsB+ShXFbcgP/vdDoxnsk9gmUiYONMpqRgsKvVqiQl5iprKHPDAbUH05gTjGOe3z+DTJh1BZAOW+7zGwaHZxmNRvEdnod1kev6ug8b60Eq+o42oViEA1Kka0dHR0tG5xHG2L9sHL6tNp/P9cknn+ju3buhrnhaeztX7qW9cTaZTHTv3j3dvHkzNkE/rZkflwP5Jsfm4ZF9l7gR/cchJWkc7bSzBulzZZw9cjYCh4DoOJFNNmf05fV6Pd6JKGC6uldaMoUBrnDy0FKnD3hjkXXw4KyPtAAXtA2yBqLhLlmTlGgvruMFBzg/AsmUg9JerxcadpwZd+LTrAXvVCgUEnk0vBcJtTwX+vH19fXI5+H5AcgAHE+OXllZiT53xoP3pr35PX2fbgcvfe5yxlarpUzmokITkXGP6PLjkV6XXHI9Z8nI0fEqeB45T495gE6/308k83tuE+/M2HU2zxkmAAZOk78/4NDHrUsLycnAkU5L2nCkYSM5Hb5WqwVrxNhIR94BqZw75IEOH1skg7/NTghrabvd1uHhYfShB23ScxFwISlRJMIPDh0MBhFsYJwRKHFWgXHmLCfzCuYDqRpBBZ9rnrdDf1Ksg8DKZX3L/1M0gPHmsmTGps8jvovUlPlAkExaBJi88pmPO5/PgBOfR8hcXabrczTN2tOu9Ie0kOo6Qz+fz2OtcOkx88Zlyrz3bDYLkOPFIrDJZKKDg4O3lp24qrkPsbSLNeXevXv65JNPIuD0tLYEOkt7o2wwGOiXv/xlMCGDwSAikCRPu/PocjKYD6L1SIUAICz87vSyQePgeT6BHxhaLBZVLpejzKefNcEGOhgMEs72/fv34/5eYphN1hmnRwEdzB1T6eGcCw6ALBaL4ZC4o+ERSKKbOLmu05YWjtHx8XHch/LSbILIMNDPcx/AHBv4bDaLcwXcKQHscD9nqOgH2gTmweWLOCnO7qRzqXZ3dxPAAYlVuVxOgFf+3xP2PdEap8GBNe9AX+J4ueYewDccDuOgVJeu8P5Evd3R8zNxqtWqisViIg+KtqL9yA1DNjiZTKKQBX3D3HEARwQXbTnRXKLcjK9cLhel07vdrg4PD8Nh8nkwHo/jvXDI0vdnzFNxixLpsH8AVdppOp3GWTgAQ4Ac0lNyH3DKaVdYiF/84heqVCq6efNmJIe/LdbpdHR4eBiFTFh7KCQCW4kBSGDfKPVO1TTOkXJ2gvmD0+1VGC9jqj2nZT6f6/j4OFhiihAwRovFour1emJu5vP56P/Nzc2H2LrZbKZut6vhcKh79+4lDshst9sx52B2kEIC4Hd2diKR+vj4ON6D8UURAsr3s54RXPAiMgAmnsn3G9ZoAgrkWa6urgaj62sNElbWM9YMAk/IUplPrImet+lBRF+nYdY5J2g6nero6EhHR0dLydoVjbXJ17631ebzuf7oj/5Iv/u7vxuHqT+tLYHO0t4oG4/H2t/f1+7urnZ2dhKVYVhsnbnA2GxxOlm8WcCdHcDhdAbDc1/YtNiocXbTZT89Ykckm/t6tRw2Qy8Hmo4wpxkpN54dZ5Fn4DvkgLBJ81zu5JKTQvQbKRnvlWaD0M+zAUuLSlb+TGyOLv3CGYV1kRYsjAMcruN9m85l8sgov/N7I68ibwP2Bgca0IljwPjBKZcWp5bzzABqQI1Lpvw5vT9ol3SfUr621WolclnSLJLLdLxk93w+D8cOCSHjHtaR7zJWkRPBjhE5B5T5MxcKhZCAUemM9uHatPdwOIzrIF8ErDB2mK8AHy9w4aDT5wttDMsFQ+fjBJAPs4a8B0fNgTfBCWmRl4azyrkODgLfZANQdrvdRHt5gAaHl7nHesCfzCkvioJT7lJS7wtfSx6XjMyYZCyR/8cP+WfMdZdrAjwuk1PhcA4Gg6j4SGXE8XgccxEQD2goFArBKjYajZAZw757fiDBImkx771iG0Ek5h/P7MEQ38eYH+w36fPB0nMI8xxMaZFj6dUKfY1K39tli7wX/dnpdJbn53yD0ba0NUDnbWwz3zPm87nu37+vTz/9NGTuT2tLoLO0N8ru37+v//l//p/1G7/xG/r+97+fYCFIhsURc3kBn4OpwUlnkSeCRtSrUqnE99Mliz0xHJbEK+C480jUGFbBN7xms6npdBrOY71ejw22Wq0ql8vF2Q68U1rOIF1sotvb21pbW9OXX36ZcP7T1cJ4B3cAWHyQ7xG5RxIlLQoOzOfzeNft7W1JybKrnhQrLdgENk0Hkq4Rx3BCcTRxora3txNMHMm2OM6dTiex6WOZTEaNRkP5fF4HBwfxXPwfbcSfvCfSwnK5HEB0Op0Gy+Xyj2w2G/1Em8IIeJ5PJpN5KHcHmRoJzQ4uMWcpcJ489wjAQJ81Gg0VCgXduHEjHFYqoeEIFQoFbW5uRttznhHtwzXIcfE8NQejtDUOcLFY1I0bN/Qrv/Ir4eQ+ePAgEpUBZl4hi/EKm0Vbu0zSnSwqbJHjU6vVEvly8/n8IXaUQw5hIWCBCUQgeSJq/zZYv99Xr9eLc3MACbS9yxD5NwEC1pSVlYuKfKenp1EVkuAKgQ3P54NN8ANdPcdPugAhsJWw6/V6XSsrKzGGPe8GgOUSxGw2G4GMdDU2SlMfHByo1+vFWOA5WLP6/X6MaQ4DbjQaUYCgXC4Hi9Tv92MMVSqVyI8BxAOgPc+O/Qhpr6SEtMxLqHtgjmtubW0FUHVwh+QYIEOVPK/C6BXgpMUhyWlZMMwO7U87w+p+9tlnunPnTqL/lrawfD6vH/zgB/rOd76jYrGofr+v3/u939OPfvSjAKBvk12/fl27u7tRVKnZbOr+/fsJxvhpbAl0lvZG2XA41JdffhkHb1F1B0cax8uTn2EOXFKEMystKlRJCkCCXMGdZpwoZ4eI1iCd8hwAjxpLC6aG3xFhh0GRkhI0HAGPrLqkimfDcfVkeGcU+BwOJI6xmzNPzmJ5gjrvxbU57RvnxpNx07p2bzv+7X/6Z7yt+Df34ne5XC5xr3TxBa4HU0UU098Xo72ceXCpoOeZAFgcUAHIaFvaOj22cG54P4/E4pDDtrjsxxkUaQGSYDD8Gu4g7e3thUM6Go0iX8DlLjxXuVwOWRrgkGT0qzIbRIkx2gT2EqDmzKfnik2n02AB0rljfi2eP812Mf/TbBp/96qE6XntLAH39/HyphlBHRx0cphoR9pU0kPzhrHL/GP+I81F1ugsJkEJ5J0+vy+T5NIPHhjxcui+rjHufe3j9+nn5zmQLCIDdWaJNvD3gAGtVCqq1+sRwGD9QdrmhT18L/BDUB1cIKklgMPvWSv8esjiPG+GtUFa7DPMqfT4B1DBgjko9PbhGX1eOQvlwbr9/f1lEYLH2MrKit599129++674SPcuXNHDx48eCvbrFKp6MaNG1HUhkDBt7Ul0FnaG2mj0UgHBwcqFAqqVqtRHtT/HybEo+/IlPgT3X+6VGq329VkMlGv1wuttjtTDprcyfU8DRaytJPORsXm2Ov1Iiq6tramjY2N2OyojoWMqtfrqVQqJdgWoqwADeQ5s9ksItu889HRUWx2ONR8x6Vb0kVOkUvN2KgBgL55u6SNqOJgMFChUNC1a9dio6SN3CHxtkMW5owUbUU03wEufUw/c1YH7AvfJ9rqzjwbPqCi1+tFcjJ5TfQ7bY4kixKyOFM4+A4yMdgYd3w4A4poN20Ca1Uul8NhoZxrv98PMCspIuYffvihqtWqtra2ElFt+p5ngIFLO/rSAljiTAG0v418i2u+++672t3d1eHhoZrNpr7++ms1m81wMjmUMs08DgaDRKW7VquV6HMHSGmQQvCg1+slqnLBLsIY8nkqgLGGlMvlYGvfNEMueXh4qHv37sUBxc6g0PcwBsx7D3Ywft99911NJhMdHh4Ge+jMH+ONYBOMj8uI0842cxi5YbPZDNna2tqaNjc3Y2wCQGq1mmq1mjKZTBRCYH2XFpXcOOML+SeBEuZ9rVaLOca6VSgUtL29rc3NTW1sbKjVauno6Chy0jh/h3NxYFM4D8tZUdZ1AIy0qJ5JcIi9A9ke5/HQH6yJkhIl6ZkT/s7MF97F89XSBUtgy7xS3urqxRkwtVot3ku6yE36b//b/1Z3796NZ1la0nK5nL7//e/re9/7nvL5vB48eKC/+3f/rr744ouHgo1vg/36r/+6/tpf+2t6//33n+l1l0BnaW+kTSYTnZycxKFtvrDzp0e1nNVgM/AN2wGMgwVkHGnJU3pjdic2bR45AzhgbLxEFV1u5vkVXN8T6t1c5uORev7ONZBXTafTcCL9O3zW28nbFCcSrbh/3t8JMFUsFpXP5x9iQNL3Sred35dn96IEfN4dLwdPtIkn93tfeEQa58udbAezgCgOm3RHwp/ZJTVpBot2dwYKR9DHo48vZwI5ANBlOLRpvV5XpVJRo9F4bIlknv8y43m9WMNVQM5l48ctk8lEXgOR4Xa7HWWCAbjOynnuhrcln/MxzZ8ebEhfE6kmfS4t8pfoR74zmUzitHeXGr5JxjqA5Ap5kpRkRKVkv3pgwucyY7JYLD4y/4B+dLbCmZjLLM3keeETGFqCHhyE7Hkn6YADY4NghefUOMvtay8FR6gKx/U5M4sKmVyTtvDA1mX7AvfyIAfvnN5PWPdYS/zfzFnPTUv3l+91kh6SwfkPz0FwkB8kvOylBI++/PJLHRwcXNp/S1vIynd2drSycpEnev/+/beWBWs0Gnr//fefebGXJdBZ2htpd+/e1e/8zu/o+9//vv7qX/2rkhQOKxIjSZFjgDQDEIHTh/yADYINAzqVzY3cHt+QAEQwHGyC6N395HauxT2I8s3nFxWFOLWaa7ujX6lUEqe+X1alSFLkIvj7sSmxmXIgIs4AzwVzAdvgye4ccnb37t1w2JFMECkkZ4IEX0kqlUrBqvT7/Tg53Z1MHA02Wc6V6Xa7khZlwtHmpyONXgGKc4Q8md5ZCs854N44CdwbAAhzQEUnPk9/e+EFSuqOx2Ntbm4mrs3p6vv7+8pkMtre3g52AgfC5SgemfVKSSsrK7px40bo8rPZrHZ2dqLcK2DsaQ1HzR2ibzKYECLe32T1ej0qE+7u7uqP//iPNRqN4nwm8uYAFw5iV1ZWEod5ujwIJ31tbU3dblfNZjPyKmgf5i9sIeOrWq1GAn0mk4m8jfl8ro2NjUQQ5U0xKj4eHx9HhTFpkc/U6/V0enoaBSiI1l+/fl3ZbDYKN8AEkUAPgzgajRJBFw8mUBBla2tLlUolUbwAg6GGyZlMJioWizEnAB21Wk07OzsJBuJR8tT5fK7Dw0MNBgMdHR1pPB6r0+kEuzObzUKOxrqYy+VUq9X0zjvvRCBhMpnoq6++0sHBgQ4PD9VqtYI1BMizfsCoz+fzOHy0VCoF683a70zz2tqaRqNRMGxpplJSyFTJlWL99Dw11kcPJLFe+3WYP87Gz2azODen0WioXC7r2rVrqlar8fzNZlPNZvOtdNafxFZXV3X79m198MEHIVM8PDzUycnJpXv4m243b97Un/2zfzbmxbOyJdBZ2htplAW9fft2LLYe6XWNM5ufL8rpCCPf8yiX0/04175ReJRZWkSJ09EyzyVx8+i+Aya/BlImzzki2TRtAAHYIX/O9EaZ1l+z6aXZHK7rEVzawqukpWV5bMZpdiPN2vAnv2ez5h0dNHhfPM5w+tMRVXe6PNrpgA2n2sEXgDQ9NshNcA172mCIkI/wecZEmhHx/6cPAMrr6+vhaADMXJ72bSzdRk8iWbvqhkUbVSoVzefzcCxpd/qL/uXztHvaefZoP33q7eWFNNLvlGbi6GPYiE6no7W1tUTFwG8j43sVjLVmNBpFmWYYMsYi49zzzXw99THOGIbJoOyxs6Ce6+aGzNQLdWA4/DAXXqmP+QnYQcKbLjjg5usmhQi8QmGakWbdgS3i/JhMJhNySA50poQ916FAg69nsOA8iz+Ts5g+hn0d8HXc9xPPyXGVQHoe+xxKs6LcJ8388zkvse25dScnJzo5OVkCnW+wTCYTxWOYY54f/LYZsmwky8+qiMUS6CztjbRut6tPP/1UH374YWxaJM67U8ymjeabTQInnYUaKRHmEWo2bq7loAOtNJtCWj7BvXlmNul0fk+/39f+/r5KpVLkD+AU5vN5HR4e6vT0VEdHR+r1epc6uSsrK1EdB+kNCyuVhUh8LRaL8ay8G1IMwAQOKJp7qnSxAXc6ndCAA9SQPuXzedXr9WDHXPvOe9Oe/MmmgIOKzGQ2W1RxIorJc6GrJ6rLWR65XC6cGiL6UlJ2CIgiuuYSOwcznDtDqWR3zHH+CoVCsDE43oA9+savC7PlVeQARF6VyqPge3t7cfYPjuezMtrMx/w3mTOET2JI7fb29kJCNZ1OI4eIMcbYJN/GnV2MsZPP5yMfg6qFtVpN3W43ckxc9kklKvpwY2MjovUcnMnvNjY2dP369WAfXlfr9Xq6c+eOWq2W9vf3JV2wri7lYlwjQ5vNLs6pIVckHcjBKWY94RpU9UOmiOHYU92OohdpJob1FykcjiFVBW/evJlwIB9ngLper5c444wcw3a7HUwGQAUmd2trS7u7uxqPx2q1Wmo2m7EGs6aSF+hOG2OYyDXrMQoC34Ng/f37PBvXR/bkBTyYDzChACxYYJhSQF63200EZjwvCPa93W7HfJxMJnFeHeuRdLGP/W//2/+mn//858vzYL7BmE9vGiv8bY1qfd/2/BxsCXSW9kYaUTLOQUhT99KCmk8zE+6sPsrSkXb+dNbDo8fugLkDmtZAc910lG46nYbTjmMBQ4MEB6f/UdI1ItNESh2UeZsAYhzM4PB5NaP0O6TPwfH7SovzGjy3BceIa6fbwf9+2T29H5AeORPiUWicXJwm/276O/RB2sHg88j/qLaETCvtZABWiPbymTQLmG4jniOdm0OyMI6ejwFydJ7HpvmkLE7aXPKGA/yoDZ62qdVq2tzcjHEKsHGnUFrkhPg4xQgkMN7pE/6P+/FsONo414B/+sCDI+PxWO12O0A6AYqr5i+9agaQRhoFq+IOvr/fYDDQ2dlZMG+0p49pl/KSI5KeU4B7Hx/p4FN6nfL+pk8AsAQzPDH+MvO1FSmuP6OzRawZjC/YU+THBHS4jkt70+sZ48eZIl9zHGAzb5xZ4/0xb29ni/yagJc0a8k8uqzv0my35yR5ro6vObTDnTt39PXXXz+ziPybaOxX2KPya99GOzk50d27d59JxTVpCXSW9obb/fv39f/8P/+P3n//fX3wwQcR/YOlgO0plUpRjhNnKJPJBBvjAAjQ4Qmzl20MbJZEu9hoPA+GZ3BpAnIGns0ZnfPz85BlUFEO/XahUIicj8ssm704f4ZIInk9OM9srjAGkhLRPM5RaTabwUz4tdnwu91u4qBHWAnakc86Rc8zuKbfnR9naaiA5E6BH0IoJc+b4BrcA4YpDXBwTPi8y+NWV///7L1pjKV3dt733Htru/tWe1dvbJLDJofk7JmZSJY1kTyR4i2xEiUIbAlwAltI/CEKgsRAYGSB48BAYueDoiCIYkuAFnhsKAokzYxmRpkZzUizkCLZXLrZC3uv6truvtR6bz6Uf/967tvVzV7YbA7rHqDR3VX3vst/ed/znOc554yECCjHTqVSmpub0/z8vFZWVkIn8nq9HsDk5OSkyuVyiBqvr6/flsPjErz19fWBKB+V3wBMVE+bnp4OvW1w6gDyHxTDuWVMGUei5/ThOchisZhOnz6tEydO6Jvf/KZu3boVqhO22+1QcYtoOM7j2NhYyJli/LrdbjhuPB5XNptVq9VSpVJRLpcLx+VYu7t7lezGxsZUr9cVi8VUr9cVj+/1JgHk7O7u6uLFi6pWq9re3laxWNT09LSSyeR7Jhl8P40gCXkqmUwm9HxptVqhsAufXVxcVKPRCAEO5pm1yLOTZ+2tW7dCRUJJ4RkEWIfx2d3dDSxbLBZTp9MJhTR4XsAu5nK5UJ0ykUjo5MmToQKig6yD5ImeJ0fxC3Lp2Ku9Xk/ZbDYwLFR0y+VyOnr0aKhK2e12Va1WQ3W1ZrMZQLCfEyDVbDZDEIPnmrSfX8O+cCYXtp/rhi0GrKFS4HwAeoJ7FIfhOUzluXw+H55pSKGRfHqODu8CWD5JQaXg+2d9fV3f+MY3dOXKlSGjcweLxWKhqSxzXK1WVa1WD73cr9/v680339TXv/71wCw/rA2BztA+1NZsNnX16lWVy+UQQfEXM5E6j/RLuu0l6REr/57rpYlC8kLl59J+cj8vfswjaP4iJorq5/KXDoDIwRENRD1J1R19AJzLazx/CIDj1+FSNH8x+/j477lGXqYuyeLlHGXWDvrbNfz+u2hFp4MYNQdHHpF2DXs0So/DEGXVuBaP6uK8e3TWpXabm5vhPpGF8Ifx5r44r7NZ0bnFxsbGQsQaZ46k+nuRqUVZyPfLHFD6WADo78QWARa8nHH0uEhDfT64z2jlLtYjoNKLdzC37J3o84HgiPceYV1RLr3T6QQnnij3jwKzs7u7O1BhzTvbO7Ph0X/f387GSoOVK51JYG+SNwNoccDv80hxgXg8Hsq/R+ePIAAAIZvNhsIDmAcz3Hhee48kb/aMo0/JaoIKSCsJMFFZDQaKe0FeSX8a7j+63gGHrgTwZyHX4N9h/KOMAGuW+/dj81nvaybptsAPz3fyoNhjzDVj7s/36DsMSeHQDjaCJjMzM6E4xfXr13Xz5s1DyYIRvCWAUa1WdePGjYEg1cPYEOgM7UNt169f19LSkkqlkj772c8qk8kokUiEnguwLTj/MAUuZen39/TayOFgT3gB8ZIgiTdafY2XtjvAOGnuTPF5nC3XUuMQc5x8Ph8YBhqLZjIZ1et19ft7VaP6/b3KUB7Zo4qXO2hEudPptEqlktrtdjhOv7/XX4eeOVyD5zvhLJA8yEuZSLmkEDlNp9MqFAoDYxMFUv1+P+jrcYioXEQ/E89z4pqIKLrMrlgsqtVqqdFohJc11wt7RPUoB3c4GgAcgDDOF2OYTCa1vLwc+iohlUyn08pmsyGyS08KIq9UnuM+UqlUYPGojicNOn+5XE6nTp0Kx0aydq/m1cfeazsIROGI4fzQp8lL9cJUHXRNJHtnMhllMpmwXthf/L/ZbIb1Ju057pTbdhkQ0Wpv+FutVlWv15XNZjUyMhLWBHlnMKCdTifkpnDdzv5tbW1pbW1Na2trWlhYCNH3+5mfx2XNZlPnz59Xq9XS6upq6E/U6/VC5TQkJOPj40HeBsgulUoaHx8PTC77gzFjjcdie41nPTeP4Eq32w05il7yvdPpaGtrS0tLS2o2m5qbm9PY2JhardYA0KE6HuxC1A5iOjc2NgKLg1yv0+mEtQBTtL29rVwup4985CPK5XLhGshZuXr1agCK0bLwVGNjXAHf5PyQXwP48PwXaV9O2Gg0guqAgAql+Rkrmrum0+nA0CcSiVAVs9PpqNfrhbW/srKifn+/sAbzxP6hCtj29rbq9brq9boKhYJSqVSQyXoAgfeos2lDO9hGR0f1mc98RqdPn1Ymk1Gz2dTv/d7v6e233z6UAPHIkSN68sknNTc3J0m6fPmyXnrppWExgqEN7V6MaBQOKA6IS6v6/X4oCIDj7aWcMWcyPLfHzaNhUabD9f0eZXTWg2MgjXK9tAMgnAleaDhUvHxc/+3XhpNHpTY/fhRwSIMluaMMCi9Sd2Y5B2PnDBAvZi9riiOKzIl7cAbNdfouIYyyToBHzH/ukfkoM8V9+D0zL9wr3/X14EAXEMHPcKIYg263G+7Dj8n9wP4cVJ0N0Eak2qUs9+pIs378vpwZfBjr9/ebqXa73QCcyWWIMmzcI0zU3VgPWEgkhzhh3I8zbax91ow3SGTeuVefd8/ZcikQAQycVg90sIZxhnFOkG4S+Xew90G1Xm+v5wnOtbMk0n4FNBg5xukgeS/j744u/3aGi2esj6W0v2ddFtvv91Wr1bS5uampqSmNjo4GdhTG7qDGone7XwAyjUYd1LBPCNRE8yw92AEQZPy8V43nInrAxNcvpbbJCXPm08cq+nz29eSSNQ8e8bxinF2KC4sJ2PLvcu08jyiYQJCBn9FawYNE0VLXQzvY4vG45ufndezYsfBsu3r1qq5evXooK67l83mdPHlS+Xxe0u2VAh/WhkBnaIfC1tbWdOHCBZ04cULz8/OS9nurABxgKtAvS7fLo8bGxrS9va1WqxUAjTv8RPo8GgYLQ2QZqY0n90r7oArHNpVKBZDGsYhaplKpwI7wkvc8moNe+PH4fh+acrkcyuMCIugdwYsqqi/nuvg9DyMqVBGlBETh6OPgIh/0aj4OIuiHQU8TQCqOpCdEc88uf4oeE9DR7/dDJJTqUTi9sAb0A+GzLk3kGESovSQskWAcM/I3VlZW1Gw2lUqlNDo6GnLDXPoUdYJd6uhgeHx8XFNTU8pkMmFcq9WqZmdn77lhJWCLeez3+yHC/LDN2XZ3d3XlyhVVq1Vdv35diURCH/3oR5XP5zU7Oxv20Pj4eOjzw/q6F3aJgg8kv+OAsqf4P+tQ2isvX6lUAqBy0Is8gu86WGL/8FwgVwIQsLGxofHxcRUKBe3u7qpWq6nX26+eJSlEy0+ePKmFhYXAfHxQjYg96xP5Ks8c8jLIz6NsM9+tVCrq9/f6acEweGVB9izPSvY07Dng0gMerH9KUi8vL2tkZETHjx/XxMSEms2mJGlubi4EbvxZdTeZJsGJa9eu6dq1a2HP1Wq18OwcHR0NPa1WV1eD4091MtYIATTymByU8Bzr9/d7oqXTaY2Ojmp1dVWdTke1Wi3cl8vy6vV6eKazbjk/zy7AEgEkWHieewQyAKedTieMM/tgZGQkVITknSftPztHR0c1PT0d3o/Ii+ghRe8rntetViv0mRranW1kZESf+MQn9OlPf1rpdFrValXf/e539c477xxKkHjy5El94Qtf0LFjxx7J8YdAZ2iHwlZXV/X6668rm81qYWFhQIvMy9gZGwc4OLdIo4gk+vf9854r4L/jRYZDSzQyqifnb2c1iMThCPBi9RKqrqEmIn3QQzORSAQQxbk8T8XZKs8BkgYZEf5N5NvZK5fkMX6AEeRlLj1z1iXKqHG9Dr68spyPNU4sP3Pq26P0HJvjE9XEqXAGCwCcy+UCmPWiAx6t9RLiRG9xch1Icx+eIIxz5Hk6yLr6/b6uX7+uV155JVR1ev755/XMM89ocnIySN3uZH4u7gmJUnQtRxmlg4w5lvYc+1u3bmltbU3VajWsR5y26L4C1N5Jsha97lQqpXw+H8qV+7ESib0S6/F4PDiFXlSCe+J6GVci8TBGzrpG14evK2lf+sd3JQWGDWZua2srMMgzMzPK5XK3BQ4etwHWcYSlfYDgzDVGDgtzyDPCGWfMc0Jwktvt9gAo4dnH80Haf77AzrlTHs23kvaDLweNa/RnPJeRmJKbEjWem4Bc5o5x4jlH0QKkjpSX5zkCM4Nc1hlhgDGspjf1JbjB+gWMMHYElLgnnmvsAc7PWDHXLgn0uWasosULDsoX8p5AUQZte3tb586d04ULFw4c16HtGyCehsM+3ofRcrmcTpw48a7vsQe1IdAZ2qGw8+fP68KFC5qentaLL744AD6kwYc7/3eHl4RBoshUTnPn2x1jHCSig56Az8scZzga/XInTVKIjvJZNNuSQrlomAP+EPk76ME5MrLXF8QZEPIbGAMv2YtMgWRlHAtesEQOaaYo7TvW7tBw7zRZBLx4FFXar4bEtZFkDNPmCdL+IifSuLm5GUqKcwwim81mc4Bxw9mg0zqOskeacRJOnDgRWLN6va533nknaNUxXl6cA4bKHW3Wl98zzjmRX5iLmZmZUAnrhz/8oX7jN34jsEh//a//df3sz/6sPve5z90T0MFxYw0RTcYpvVNBioMMx415P3v2rBYXF5VIJELOBoAQx5Vzb25uqlqthopn73bd09PTSqVSqlarA3ul398raY5EjP1Jxa4o68c8jo+Pq9vtanV1VcViUel0Wt1ud0Da5uV/yW3jeeCSLp4DjUYj7EHWL/mB7AWvtPdBsJ2dHdVqtZDP5wEaZ1SlfYBKEOIguRhrwlkMmAL6zMAC4Lzj6BNsAHh53x1pT9qSTCaVTqcHig3cCSwftHZ5bjQajdC/B4mYP29brZZGRkY0NTUV1jGSROlgKRnMDvMMS07ejAeHRkdHlc/nQ94ZOXmwrs7ksP6y2WzYmzCbXAuAi/dJq9UK53H2HMBPxbetra2gDuA5zzOJ4hqAItY0z/icsJhlAAEAAElEQVSFhYUBgAlj9OUvf1lnz54d6I00tNstHo+rXC5rcnLycV/KB8KOHDmiT33qU+EZ+17bB+epO7ShPULjRV6pVHT16tWBl4A02LdGGuz5EovFgvPE//3z/MFwAjAHUTiV/rKMfh+nwptL4rBJCjkgIyMjISqJJATpANdwUDKfs0sAM74TbZwJA0WXb7qcE613IAUQ47ie/+QVsJALeXSU+3PJBWMnaYCdchbN84OolISzywsetgqHwoHn7u5uAIvIlTAHgpSVTSQS4XM4OqwNwCvn8ByGqGSNfADmFyeD9eAR1mq1qrfeekuXLl1Su90O4HJ9fV2XL1/WRz/60fvaCxybohCsBYotNBqNIBe7kznYjcVimp+fVzKZVD6fD804k8nkbYwlEWpvMCgN9tmJXmsymQxRboCEM4d+bAoO+Dgyrjhv7ihybtYEBTVYXw64uXYi6rC7HhyIyjF3dna0srKinZ2d4KSXy+UBhuhxGY54u90ekOLyOySi3A/PS99zDs7d6cXY9zCfyDijn2esorJbzkXxFdYTWv57ycnByJujsSeSQwfjgFLfq7CsXCuMXavVCuuCz3O/PNO9iAlrlmcM4+t70RknaR9wexCOPecSwagCIfqH78FOMt7MNffGnFKwxVlz6XYAGX0vvfPOO7p48eKQ0bkHiypADiubg8VisVBi+70GykOgM7RDZYuLizpz5oyefvppzc7OStpPvPSXCg9qZxmiEi6+GwU7OLueRO/SDl6ozu5gvDipOCTtyzOIkHris0sw4vG4arWaqtVqiCze6eHJizkej4fcnFQqpVKpFO6Lz/DSoxcMTfJgVYiwOrhzCUen09H4+HioboYm3KOSON/o3ilBi7NBlSV07973h5c3zidV43h5AGSo3sULBokfspCtrS11Op3AaDgLMzExoSNHjmhra0sXLlwI4AaHB4cCR9avu9vthogsDi5V2kiuJ2eKiDjO1fb2thYXF/XVr35Vi4uLAzr6W7du6cyZM/r85z9/3/tgbGxMs7OzAVATLW61WlpbW1OhULgr0AHIkbf0zDPPaGdnR8eOHQs9nqJOEWCUstHO8jgTGDXyGpLJZKjCxfoBxDD+sHMu58HRRDqFDMnPDfgGSLIGiPiTr0C1RaLm7G8i6DiT8Xg87I8rV65ocXFRmUxGqVRKH/vYxz4QQGdnZ0eVSkWVSkW1Wk3j4+MqFovhd/F4fCAHjDXtzzTGjrGKBoBGRkZCn5ZCoRByXDx3ivxAgjfOevF/5JkAh+npaUn3Vyq90+loZWVFt27d0vr6uhKJRFgr29vbIZfKwYvncXkBCvYJVcb8GU3Qgmca+X8wSi7Ny2QySiaTmp6eDgGPdrut69evB/CMI8yzWNovEkNRAWfW/Lno4JX3Ct9Dhshcsp/4HfMZDRhg3AdreWtrS6+99preeOON+16Lh9nYT0OTbt68qfPnz2t9ff09Pe4Q6AztUNmNGzfU7/c1PT2tEydOhNKiXhUrqkvGPDfAXySUP8aRgwHxztoAG4/MSfsVjiQNRNBcNiPtR4e5Rs6FU0/0nJcSIKLdbgeHhWPxgt/Y2FA+nx8AEnzeo4T88agsTj3/dpaI60Dml8lkAkNAQi3OBQAI80aAOPuwJi4V4SXtun2XJxUKheDsTkxMhJKo7oAzPzBksEkk6QJmcGCi+R4AK8p7A2Dd+fJ8Dq8cB6vHfJI7BKtWLpe1tbWlSqWijY0N3bhxI0hnsHK5rFOnTj1QMQFn7Dw/bWJiQsVi8a6OuO8J7nlqakq9Xi9I+XwMkC+x7jxq7se8k9PKOLmk0feoswueg+D5Uy7RIUeMAhXdbjfMOWDc7w+pH/cXlTq5o+lMUZS1paDGrVu3AkBMJBIql8vvq6SNdQv7S74UQI89HI0yE5DwKD/3xdrlPqIBFvax53ZEpbz+fCQIUCwWlUwmVSgUguRNuj+Aw/qhSWy9Xg8Na5FpdbvdEKjJZDKhaAxsLEw064XnBgw2z3nWOs8pnoWAYd4bzP/o6F6vmpWVlRB08mcRY+SBJx/Tg5gc/k1AgPcIIMurP/JZPuPMJWxXt9u9TYpbKpVULBbDXqGM/mGsGHY/Fo/H9eSTT+rIkSNKp9Pa3t7WxYsXdeHChYFnx2G1y5cv6zvf+c571igUGwKdoR0qu3r1qq5du6bPf/7zSqVSWl5eDv0TnM53WZc777wIiMiNjIyo2WwGsJFKpQacV5x5XtDO7PBS8gpGbrz4AS44W+4st1otJRIJFQqFkOAaj8eDowAAI8rIeTOZTOjm3u/3QxdwZEs4hS5xgxnxnCSXDzEmMEk4QACdVCqljY2NUIqYHjYzMzNBQoOkqVQqKZVKBcBIHx3OR2QTEOKgCIYECUg6nVY+nx/oFSHtRXg9GRvHYGJiIiT9cjz+EGH1OaYSF0AKqRWyHir1uaODg4AOHvaB709NTandbqtararT6ej69eu3Rf2mpqZ0+vTpwMLdr3nUmr8pbnEni7KPjMvd2J+dnZ1QpCBa5UwalH8e5MCyL6mW5QCCNeoyM2fFOFcymQyOHk4tPWE2NzdD9cJsNqtkMhmANTKfWq02AGRdfsoaRKbockxnfAmo3Lx5M1SEm5iYUD6ff9+BDr2l6vV6CAawz5LJpLLZ7ED+HfMUBT6Mt6Tg+EZlgfxxp9ulo5zHz8GzY3JyUul0WuVyOci77te8Wtrq6qqq1aoajUaYS1hkCgAQwCH4I2kgr4+16KWUATpeYt3BH0CHMaOnzcjISABa0n6jUta5P7N5bjggdAbNx9XPwfc9x8rzSD3o4eubYI83waUP0uTkpMrlcmA+6/W6KpXKEOi8i8XjcT333HN69tlnlcvltL29rTfffFPnzp0LLPNhtgsXLugb3/iGFhcX39PjDoHO0A6lra2t6erVq0ok9hqq1Wq14GxGo9HSvmwjGo0kiZwSn0ioHCBFq9Pw8oHNwWEbGRkJThX/hxnhpYcDxwvPI268sGB3POp8kHE/vNQALLwUid7iAHPfAALuzXsQeYTRI8AO/DCYmmq1OpCETJUtoucbGxsBZGDuWEfv0eVEniOAlM4jyP6ChwEAzHgS8ebmZijBS2STkrvutBB1pQEr3ycKjEPLXDKuACUS7ylgkM1mNTc3p+eff161Wk1ra2uan5/X0aNH9eM//uP69Kc/HWQ8D2Kc+27rRNqT2rVarVBKd2pqKhRecAYk6ljV63VVq1W9+eabKhQKYS054GGd3ilK7zJHZ+7YV7VabYBtozGsO9kAf0AI6xJ2kappXLsXCSGQ4BF1v09kkeT/OAuK8+sgbnFxMYDEZDKpXC6nXC6ncrn8rvPwMOZS15WVFTUajdtykngWeIUu9qZX8ZIUHGaeTTjRlFnmnJQ1Zv17Pgr5bpubm9ra2grPUQA0pa3vpRJg1AgOIWf18s/SfnEC7pv5ZX2QBwRwZhxoAMxedhXA2NhYYK4BSV5MxkEwa7/X66lWq0naZ7QBR8iUeT45aMJ4hvte4rq9lQHPNw/WERRjrD1vUlIovuLflRTukz136dIlXbx4ceisv4vF43GdOHFCzzzzTAj8ffOb39Rbb70VpOqHySYnJ7WwsBAahTYajcB4v5c2BDpDO3TW7+8VJbh+/bpOnDgRZE4bGxvB8SRihnxJGpT7uFMlKeR5TExMBImKS7pwvKT9vB4YEH4O0EHDjrYd0MHLyRu8+cvcI3r3CnR4QboD7lFaSQPlkrlvXv4uyboT0HGHiePyMyKZHKNUKqlQKCifzw+UUvUXOSwREXSXLjFvODCeIMw5icbicDAWsFXcn+ccEBHmc1RFQ/oIa8Zcj42NhV4rjIPnhbhUzKVVVHrCKcrlcpqZmdHp06d18+ZNbW5u6tSpU/rsZz+rz3zmM/rYxz72UHuB+3k3Ixp+7do11Wo1PfPMM5qamgqVxjCcO4BOpVLR0tKSzp49q5mZGc3NzQVpEOs/KveLGkCHzwF0mFNYS4IMnjsT/b+zCpIC+MZx9UqB3E8sFgtV7VzSxTziJBKZByiwt71Hl7RXXAK5YyqV0vT0tLa3twMQfFTGWNFjqNVqBUea+2b9MrawCy714rnmDKuzPQB/mEH2OOfJ5XIDbOjm5qY2NjYC0+bsMYGPBwE6yNXa7baazWYAdcwbLIy0Lz/081D6Fyad+YVdjjI1kkLOjjPFzsBHc3R4bjUaDfV6vbA3nKH294O/L6T9YBXjxv7g/jifB6uctWF9JhJ7JfwBV4zL+Ph4yC/kerkWz825cuWKLly4MAQ672KxWEzHjh3T008/HXIiv/vd7+rNN988lNK1yclJPf/885qZmZG0V/FwZWXlPT/PEOgM7VDam2++qUqlop//+Z/X8ePHQxM4aV/a4TINwA2RdiRH/nByCZA7BPztJYQdlPBS8ZclchtehByn1+uFF5NLx6Ladpz2ZrOpra2t0CjUX+RjY2Oanp7WyMh+X5FGoxEkCjijDqa4Hn7O9fT7/YFEb4oXkPfAtXFeXv4uN6PcNNeMTh5Q5S9zABPOiueFME84pFtbW2o0GkF24gwbjrA7/EhSvPv6xsZGoNMpCDA+Pq58Pq8jR44MzDPSm2azGdaBO+dcp0dmuTZYRQBtqVTSCy+8oFQqpVarpXq9rnK5rNnZWR05cuQ92w/vZlRUA3xTyAFnKiqFkfbW8fT0tEZHR9VoNAYi3XdjcNxgXSjfy/pk/cFgSgf3TfF8EpeMJpPJIPXEKd3d3Q3OPHljlCln7eHEs49c1kj0GxDEmCCJ4tg0dkTSuri4GBjlVCoVcna8PPbDGIxBu93WhQsXQvNa739FhUfkoxsbG6EhcTRg4vlInlvj8kPPKcHBpyEn+YLOjrNPYFQkhTwRzy+8H9vY2NDq6mooJ40MlnVKwIMCFuQDZTKZgfL3SG15BsJQeVADRsr3BIVOxsbGgkzW8/uc0c1mswOSTMCT977q9/uq1+u3AX4fbwIqFMdgT8CMSwpBAwI2NKQGXPl1cJ308EGy5kUqtre39Z3vfEevvPJKaOQ6tIMtHo9rdnY2tCrAzzisxQgWFhb0hS98QU888cQjPc8Q6AztUNq1a9e0vLysv/bX/lqotkMEDkfVK4pJ+wwITruXQga4uGQtWlqXlxdAxtkhdxJ4eXq3b87BC1U6uLEbL1h3zHihR6UnVETq9XrK5XLq9XqqVCqhchAOHFE+zpNMJoNkzfOaXMpGkr6DsN3d3fDS9OggDiaOBaWsPbLuLBVj6c6kswKMoUf0u91ucAC8U70zWF5pizFz2R2VYEhepwcLUVGKLwCuiGTTh4PIqs+3jw3AmUgrVZkymYxOnDgxsEbuBSS8l4YzSG4X98l1R0G8pMBKJhKJcP0+X/dqjGWr1VKn0wlV7Vh33pDVo90HjbE7p8lkMlw3iec4lOxdWAvWK4ENDxo4a0iui+cScb9IW/17iURC6+vroUpgtDpftArZgxiMQb1e1/Xr17W1tRUAGb9nz7ZarbCvKRQSzTn0Zxfj5eYBB8Ya9hbpq5echhXz/BJnsGE979do2lqv17W+vj4AfF2Cm06nlUqlQn4Wzz72Lc+/aJNRrp2+Oexx3guweBMTE6FaIOZgwtkUgDSOb/SZ1+l0BhhqB5YevOHcjCfjwR5kzfo5aaDr7xVpX2I9Pj6uXC6nUqk0wALv7Ozo7NmzevXVV+97jg6bxWIxlUqlIDeO5lodNpucnNTHPvaxUAH3UdkQ6AztUBpR/PX1dS0tLQUnjXwRnFKn7Hlx9Pv7VZgAOiSR8lLA4Ud25S9WXjSeGC/ta71hU4jaoY8nTwXZC8UIcE54WWcymYFO3WNjY4GFcPA1MjISIomTk5PhZR6Px0NRhc3NzeBgEmGGHeFF70nfzrbgkDqzRZ4AEceohAnAwLjQZJEkeWSBSPoAIzigrkcnYupSO0nhpQ2QpNLW+vr6QGEKfo9chDEkMusyEuYdh9EdB//D96L9gnAkm81mAH6MHc7TW2+9pW9/+9v65Cc/qR/7sR971FtkwFgLSAsBi8xPNG/GbWJiQgsLC+H/zPm9gJ1er6erV6/q1q1bAcATBSeyLmlAuuZg28EVLClz79JS5FGsYxgGac+hpTohILxarYZ7oYFvv99XNpsNORwAGZ41tVotnIvrkBS+C9vBtfIsOHLkSMjleRCAu7Ozoxs3bqhSqYSIezabDedAhpTNZlUulwNQ29ra0traWsijYV9hXCvSPBgDz4FxoMOedBaV+SKAUygUlM1mNTU1FZLec7ncfQMdngmUxN/d3Q35PlRdo7pZoVDQ9PS0FhYWQt4E1dMYB0CGAx1naePxeACFNCLd3NwMzwieP74/mEvePYlEIigFuObNzU2l02lNT08PgGfPM+QeKfriIJF3FNfKc2lsbEzlclmxWCwUngGIOtvKNadSKRWLxdDkkrVbr9e1trY2LELwAObvr8Nq2WxWx48fVyy2V9DiUUkfh0BnaIfScCxIliYvgv4BkgZYGY+i86LxfAR/oUj75YSd7eD/REHdkfUImrNILrmBpfCoOA4CLAC5LbykcRBIOI3qu4lsk3DK8b0Kkp8DGRf5TLA+rjfneikl7A4jbAf/9z/SvkONcf3IV/xlz/hwTI+y44BMTEwEhsZzEByQZrNZjY2NaXV1NUSt3YHh2ngpcX9o2VkfOL0AZRxAHFgvd3yntYKD7EwhTtvNmzf1p3/6pyqXy+870HFHnHwtosI+BgdJ0jwB/UHOW6vVtLq6Go5FEMDPw9z6vvE/0n7hAL8HggesZQC8r2dJwYH03BKXUbK/YAXd8eMaCDzQF0jaT4h36SJSvG63q0KhoEwmo36/H/aAA+d3M0ByrVZTpVIJzjff96AH+6LT6YRSwfQLIjBB5bGDZJie08a5XeLlgSKuCxkZzzkY31wup0KhEJqs3u+a8Tw7zoMkkYAO15hKpQKoSqfTqlQqoUgKoIdjeYU4QBp7lR44XlzBn7vRYBnrKyr55NlBBTcCMzCNXA/f9eqefi5/drF2GXcYcsbFf+7BKf49Pj4egCLFIfr9fqg4OgQ6727R5yPr4zADHdoZsI4eVaPZIdAZ2qG1Xq+nH/7wh1pdXdVf/at/VUePHg35ABQhwPEhXwSd+cjIiFKpVEiKjzp4nqzP/3nRef6OR5x54XFtREhxingIIH9BRpZOp1UqlUJDQuRmJPmOjIyoVCqp1+uFhoBuiUQi6PFnZmYC04KjEs0xQNKBpATwQWRaUhgfT/COx+OhlKvLiqLMhv/Ocyek/fwZjkmehUsGndEZGxsLkhzkgD420n6vIbTyOJZIPbifXC4X2DyfY66z2+0GiZDPhcu5cARhfQqFwsBnAJQbGxuBtcMJLJVK+sQnPjHAjrxfRoUwz8mJxWKanp4ecES5Py+XG2UC7tVwiDOZjIrFYlgjXvrZzfMfpMFGu3x3cnIy9ADJZDKqVCqBeZP2ItRUDsPR7Pf3yq/DLDozCMtCoIPGotlsNoyTNFgZEIfUixJ4nybyglwaSECCohXFYlGlUikcy8e/3++HnJCrV6+GRPyRkb3Gm3yOfYDjDXMBEACcOOsKo9vtdsPzjeeCX6szruSdTU9PB4DFcxZnmnEF4MzPz2tmZuauZcujBjAjoXltbU3Ly8tKJpMql8uhShhra2ZmRiMjI+F3PF+RrVYqlVCtDXCBrI45ZdyYO8rDcx3SfpDJpXouUXNQjZKAAjlIRnnn5HK5ULgB1ojricremDMKaVDevFKphObIksK52AcexGBtFotFTU9PB/YbGedXvvIVnTlz5pEkkH+YLBaL6dSpU1pYWFA+n9fW1pb++I//WOfPnw/luw+zvf322/re976nS5cuPZLjD4HO0A6t9ft9LS0tBeeZsrdEbT3qzguElwMvLmct+Axsj0fvPbeCn7vEQ9qXMjgzwovHX2A4WkT4kPPgLLjjRUQTBuYgLTCR7omJiZCE7ZFXBw5+/fzxEr0uw+Dl75IgXsyeOxEFex5J94g899Xr9QbAgzsI7pDhVBCt5AXN/zk31+SsS/T8MEBcqzuhREtxPOirQ5EFd2TcgeW4RFY5BnInn+der6d0Oq0jR46Esrfvp7EOWdsANZwxz3+IzueDmgNtosi93l5ifZRRIArOWMFWUrYbI8eAvizIOplLAgyNRmNgjZCXBWODg37QmuDczuhivneQRUVL93qVsqgkkOCKtF+KOBpgQXbV7Xa1tLQU5JDsD+l2UMi1c03cO/vKDdab+/DS9s5CEgBhLZOHAgBEQgWgJ3DBPd5vI1xYs1arpdXV1VCAwCtGeo4O+W8EJHjmAtC63W6Qq3mFOc978T3NHqFaJIwv4M/n2INozJs/x7wCJDlaBMpcfuxzCCMTZfo4ln8WlhgW0iXDzKGXGmdeonvu0qVLev311wNDNrQ72/T0tI4dOxbk1JcvX9b58+cPZaW6aHB4bW1N586dC3mw77UNgc7QDrWtra2FhoxEWpCeADSk/Qg8jgoVk3Ag/AWKI83LkZdYNJ8BtoJSpZOTk7fJ4Fz25c4RIIJIOUUHOp3OgK4a2UalUtH29rZmZmZCjgBGda/t7W0tLCwM0MjudOEQ4uB5jg1OhrTXWRzHjOgy+RH87fpkABvOhuvxYYWiTh0PSXcwuW/GFGclkUioWCwGh4ekYpxYl+31evsV91yWBWPlMjnYI2drqDLn7Jc7B15ylrGB5cJZKhaLAcwQQc3lcjp16pRmZ2eVTqff413w7kZvHyroXbp0SY1GI5QKxkGC/WNvUNL3QQwnE7aS/XHt2jW1221VKpUw7+TNEFWnGl4ulwt9stbX13XkyBHNzs6GuS0Wi8pms8HRPn78uHZ29hqcUqWM+dna2gpNJCmywJyxN2GCyBnzZHIcTnf0CRIQTABoxePxACAAmDAtq6urunHjhkZHRzU5OTnA0pIfxN5YXFwMeR44rDjfsVgsgCaeeTjgACIPuESddoBMVMIJu8N32YM8J5B1cYyNjQ1ls9nAKC8sLDzQGt/Y2NDNmzcHEv+TyaQ2Nzd17dq10PtqdHRU8/Pzmp6eVi6XC9XW2OfkMwF22b+tVisEcmBACLoQyAAQwBIz91T1BFzDBHrvLt4l0n7zaM4Ni+k5HYA3L/wi7QfC6vV6CJ5wTth/1qbPWS6XCw1Ckcp5wMrXsrT3vLtw4YJeffXVwF4N7WCLx+P6iZ/4Cf3ET/yE5ufntbOzozNnzujP//zPDyVIzOVympubC2Wl3377bf0//8//88jYrSHQGdqhNhzPSqWi9fX1gfyYgyLUzmjADkgaiIQBZLxzNuaRfQdB0v6Li2gq5ueVbmeA/EXpDIyX4UU+QWEDnCiOgzQpnU4PJNu6Ewv4c8bEJXhcs+cp+MvZo+Z+/W6uLfcIps+H5yd4Hob/jIhqu92+remgX5Mf03MHcDr8XMyLa9mj1w4oi/b9cfbOz4tDQxIyTIWvG45LBPpBze/1fs31/h7V9ih0NO/jbufy+brTZwCrRP1LpVKQTiUSicDseK4brEU6nQ6J7DMzM8Gpdmki8reD5IUAjbW1tbBvHOR6RUS/Jw9wuHPImBFZ97wcfgazF2ULOLbnfDmT4DLZ1dXVwJRIe8EJ8jcAP85MOKBhLphrl5D6PvP14GxWNP+E41Jl7aAcQ99PMHcUY7kfA1QRxIBJY+547iEBzOVyYS34fqWAQaPRCHPOWHLvHkzh3/7M8n8zpjBZPn48p/2dEV1T/sxnPzijyLqOrhdpv6ErICmfz4dnv79T/A/BN/Y2ayRaQZTy+ZVKJTQ7HdqdLRaLaXZ2VqdOnQoByPX1da2urh7K/KZkMqm5ubkgq2w2m7p58+YjO98Q6Azt0NvOzo6+9rWv6Y033tC//+//+5qdnQ0R3Khj6o6LJ+DyQnQZDz/PZrMDJZWJCudyuQH9O1WMPHqKTIifeY4KlaKIqiJzGBkZ0fz8vMbHx0PklOuEuSLC64Yz2evtV0Yjquf35iDP5Q68tHlRes4OUUki0hMTE8rlckEyt7u7G/qLIFdKJPb6AEWjpu5kU3mLn3N+otAkmcdiseDwEFHGyaUhKo4B0WQHtZLCGHvVO0mh6EM2mw3f8YRkd068ASpjwpzjUNA/6b005iMWiz1w9S4sFotpYWFhQLOPuTNE7lf0XDAfjBWSsKiRnI9TJ+3tv2PHjqnZbOr69esBrCBTS6fTOnbsmDKZjMrlcriedDo9wIa5o8j//R7YI8lkUp1OR++8845qtZouX76s0dFRzc3NhX0HqIZd4nhRQI5MMVqiWNoHGqwjJJV8j+R4GAOCDfR3wmkgwAATSn4HjOjq6mp4ppHz4SAfpwvppVcz5JmzurqqVqulTCYzUAmRZwHPAdaFH5PnJv9n7GCnyuVy6CN0r7a7uxtyaQhuVCqVkCdIzxeq7bF2vdfYzs5OKJhBDgzPHp53fl8EgWCGR0dHgwPL3Hqe48TExAATxHO0XC4PFBCAcaPqJcwQclbKz/Pe4ByMAwxqIpEIgJ78QY7FXuMY+Xw+MG+Uy+e5ODk5qenpaRWLxRAE3Nra0le/+lW9+eabj9Q5/bDZ5OSkjh49Gqpzrq+va3l5+VACnSeeeEJ/82/+TT377LPvy/mGQGdoh976/X5wAHBKXWLmEUp3XKLRV37mMjZAgjew9GhZtDgB5k5S1DwCJ+1Hvr15qbMBAANenNEeDBhONn88WTgaofRzYF58IRoB5jqpSgb44PjcgzNERL6jCd0eefdIKJ93cAWI9M9Ho5nOujiQ87Hx6LbPt38uyvbBtDG20cgtwIfjOdjhOt8rI+LNODgbFV0H92I4bs6EHGTR8+DYdjqdwKTcyRiraEQZB5kxJBo/MTERSiSzhv0YyWTyNgf6TvcNkGBOCoVCcABZU77nnc3i74MYSL+vg0ARzwvAOsfxvcRYAHQIiCAPAww5qIJ5cLbSn19exYsxja51/rjcFqctms8oaSB/icBIdP1zDAIeMNH3ahyfRHsCC4ydn4MeOTCjDjK9HDnf9/Hx50WUqfU8GgxZMYENPxfPYubC14HflwfM/Fnga4E1w/z6+BIcAozR/sDnNgrIAXLsbRqoIrvmnq9fv65z584NJWv3aMwHAQfW3KOqMvZBt2w2q5MnT4biKI/ahkBnaIfekK7RNyKfz4cHOxEyXg6eY8ELgkpILhWLx+OBscEZpFwp3yMaTIWjdDo94Oh7Ei8vIl6QvBzJm+GlCWhqNBqhH4w7GPQ86HQ6A708pD3nlSpaSF2azWY4l79QJQVdO1r9aFTco5JEfPv9figbi7PhDoSX+SVi6pXvvOIPDp605/ySWzExMRGis+4gcAwcAC8By3gzTjANMCGwSkTct7a2tLq6qvHxcRUKBW1tbYU8GyLoo6OjwfnC+WbOkdJ1u90AQMbGxpTP55VMJgfG/WENxw12hLUL8MIRvh8Hk8/eD0BqtVqhYeXm5qZmZmY0NTX1rscoFAohv8uNCHMul1MymQy5Fvl8fgAUEyl3ueb93OfIyIieeeYZtdttpVIpNRoNLS0thcphACzuC4aKHCMHuTCsFEno9/f7MwFOYCBo5utAIZ1OK5vNhig9f7MHFxYWNDo6GlgRcqnofcU9TU9PB8d1c3NT9Xo9OM04t61WK8ieeEaS98E90iBVur2cNHuSIIw/M3G46aMxNTWlYrF4ILN3J+v19nrCtNttXbp0Sc1mU6urq4ExhfEtl8uam5vT1NSUpqenbysQAki5ePGiOp2O1tfXQ18aSWFso01x/Vq9wh9FYTKZTKh459cMY06e1u7ubigUwzyRlM1z0XO+yMVk/J0h9gCc50nyHGZeYP76/X6QgHpS/MTEhMrlsmZmZjQ/Px+aqvK9l156Sf/v//v/Hsr8koc19vRhtlKppE9+8pP3td8fxoZAZ2hD037DxkqlEhrIeeTWJQLSYLM3/s+LJqp75uWDwxmVv7lzzIvNo3geDfRoMd/lheXn9KpKznjgjNEczkvvEv1EAhQFQn7Pft/RKG00gh39+UFRUcbA75l7ih5LGmRnOB6RZhwq/xzHdz09Y+eAydkJj6j6+bkmEpX98x7tZu1wXP7v93nQGEkKeRY4yvcDQg4ynMs7MYUezb/X4z3MdRyk+8ccmBHx9og5lQQB9ciTMpnMgKTQz+nrwR1uzk/kPdrEl8/xQi6VSur3+7px48ZAyWEPUPhxcfzpf+L7JMp2RSP1B7GFfuyDrjPayJWgBevO82N8TbAvouwC98ba9cIEXLczodHAgj/PqNQGixeL7ZWmLpVKQSoYXef+nIkagadOp6N6vR7ykaT9oAw5KTzPmEd/7rDWOp1OkBXfae1I+42dPY8pysr5vudcB80rQRx3+HhOwwL48xX2O/psj7KDXK+DH9huPst98BmfWxgw/gDuAJa1Wm1YFvkezZ9N0l5hCooDHVZDVru9va1qtRqq2T6y8z3Sow9taD9Ctr29rT/5kz/R5cuX9Xf+zt9RqVQKUivAQ1SCsrOzE6Kb6LNheSgCwAsFmUn0xQnoICro4AG2xiUMOCz+wqMqFi8lnBKidESJW62Wms2mrly5omKxqKeffvo2J4JjtVqtwAx5l3kcUF6wlK/FyUyn0+F+JYUHPLk4XtlO2ndYGGcAGCDLy7AyBowr18BnXVqDVM77ghCxHh8fH+gvgpPokh6YHpJ3ozIlXwPcH2wbkWykRDiezAl9clwWCBu4u7sb5knaYzQepncOoGJqakrSIJiDXWC9uLP7Xlsmk9GTTz4pSQMOV9SWl5dDc8t+v6/p6enwu52dHV27dk2dTidIak6dOnWg/DNqRPiZc9bCyMiIbt26pU6no6NHj4a9ErXx8XGdPHlSo6OjOnPmTJhHpKCsN5eJUlEsm81qaWlJ7XZ7IF+v3++H5wLHwnmG/RkbGxtoUgoYYD9xbZTHh4lEIjo+Ph4AQaPRCI61M5/SfqADEE9QhMR9+uzwWcAhfWZg0qhGBqtQKBTU6XR069atALyYj8nJSX30ox8NYDUKfNkPXiTG18LKyorW19d14cKFwGhLe/syl8vp5MmTyufzmpycVCKRCM8B8h23t7e1uroakup9X9LvC+DM8xYH9aBgl+fTOIhywNrv929rY0BuDsVgnH1hbyK7QxLLOwm5H9cHmIWB4n3R6XQGgmBUy0PiSd4iRRpg2cg9lKQ33nhD58+f161bt+64z4Y2aC+88IJOnz6tubk57ezs6E/+5E/09ttvP7JSyj9KdvnyZf3gBz/QuXPnHul5hkBnaEP719br9VSpVEJfgV6vN+BsHxRdxfF1B/tODhwvaq/Ixc85JoCH43nUEIO9iP6c6wRAucQOB4Zra7VaGhkZUbPZvC0ZnOtJpVIDPVtwdFz2dDcNOT/zyk4uF/GoKuf1im4e/YyyDR7V9uM4C+Ng0aUc0Yi4R6i5T5wQSQE4kggcvT+uJxq9jUb33RFiDfBzHHUcGByxVqsVEsPfzZG/m92JyWG+GLMHPf69XsPdmCnmjgav0t64wkBK+9WnJIVoM0DubhadsyhT57lyd7t+GAKS+AHzzi66xDKa48Ex+L2zmc4S4CR7SV9nT3wdEQgBaHmuBuWrmf9osMTP63vDny2cK8p2RteLf973A3va/zAGsMdUPuM4B7G4bs5Md7vdMAYA1/Hx8dB8lPLx0bXQbDYD+KP4jMtXnfEAmDuL4kUJ7rRu/Dj9fj/Mu4+Ny2kBrlHWDxaUuXBg78EWHyveEcydM1UOiHxv+DMt+nzu9/dyc954440hm3MfNj09rSeeeCIUerh69arefvvt8Ow4bObPmUqlojfffFOrq6uP9JxDoDO0of1r6/X2eih0u11dv3496P+3t7dDbgsvNW9MKSnkXhDRd4YG54UXOdE7IqJEeEnGzefz2t7eDtppl1VFdde8mPg3lDjsindTB1zt7OxoeXk56MHL5bKeeuqp25yLUqmkiYkJraysqFKpaG1tTY1GI4C/KKtCDgR5NQADIuREQvk/90AkFolJJpMJP48yKTgKsEY4JjBvUZka80UkGVkPZWf7/X7Qn/d6ewm7KysrA1W0iHqTywJbxLwAfmKxWHCWmP8oiCWvifwtXwPSnmRtZGRExWJRvV5Pt27dUr/f1+zs7ADL9V4ZkV53vh6XsWZqtZrW1tZCtcLl5WVVq9VwbTjIuVxuoNjEux2bCDaSCZdxzczM3JVlcstkMnrxxRcDCxSPx9XtdsO6JCK+ubkZ2E5YiVgspnK5rLGxsVAljAR678vk8iVyXaR9JwFnHpnf5uamarVaqOKEBAuWhP0l7YMYrxrGnoxK1g6SGPq+xFFn7bPGXW4IQPAqi+z1QqGgTCYTzstYcc6DilXs7u5qbW0tVFdrNpvh2cDx5ufnVSqVdPTo0dtAfqvVUqfT0aVLlwLLRmCLcWdsPMCRy+W0s7OjWq0W+tI4c+ZSZ8aSdUdvH6qXMS7kg05PT4f3D88Snt8UBeD9Mjo6qmw2G551MIcAOuYWNp+eT81mM7yHoqXNOR/BFgIKlJFm3XzlK1/Rb/3Wbx3KJpcPYrFYTC+88IJ+9md/NjA6X//61/X1r399IHfrsJj7OZJ05swZ/cqv/MojL8owBDpDG5oZL66rV69Kkp555pkQPfMoJU5uNELsETA+E81hcV00/3YnMxpR9OPyQgVcRNmOqLzK2QFpvwqWN6LD2XKWQdpnMlyn7YyGNNg/yCOHUfYlyrTgCPn3XDYVjXT7eXhRc/0emScyzLw4a8L9AWjQwPu88/LHufToE4ZzwPj7fDlr5dWYcKTuNPd8jrFxpgqnizl5L8DIzs5OKP/NWoKlu1OfoEdtSAyRSnojT64ZphGwc69liH2Ncl8+t3djcjDkPb3eXsn4ZrM5wFb6mDmTitPp7ADrx+8vKkn1/BlJASjhiAMMJQXZJ8CHIio439EgCT8HaDt77EBGUmi860URkNRKGhhT5sn3LMdinSNTo4hElBWKstRuBEdqtZparVYAKS7Fg8mhcXHUKABDSeqDmJx+vx8kgC6xZXzYpxyfIIS/KwBflOBmHfozyc/na5L1yO9owhotwsK6JP/JQSL72Zk0nxd/fzA3gKpUKqVsNhskt81mM/QXetT5FB82o9Q9MkSA793W+YfVMpmMTp48qdnZWUn7e/FR2xDoDG1oEet2u/ryl7+sEydO6OMf/7jS6bSWl5eDHnp7e1vNZjNEHN0p4MXhGm1/ccZisRCZ8+7X7iTx8uYBQGSQxn8wJ2irXcbmL153BtvtdnAyxsbGQof7drutsbExNRqNUG0Jo8QoThpV3Og74TIzad8Ri8p1PGdlc3NTrVYrlAF2gOOME38YJ5JhqUxFFJteF1Q6KhaLwRmR9puXSvuglGpVqVQqjCEv8d3dXU1OTmp0dDREdAF3RHYnJiYGIqJcIw5oPB4PzhPjU6vVBgohAKr4PxX3vC8RzggR7Hw+H9i/hwUh7XZbr776qsbGxnT8+PFwXO6FfID306rVqhYXF7W6uqpms6mpqamQd4KzJUnlcvm+K6gRIMDuVtb6Tra9va3l5WWNjY1pbm5O29vbSiaTYa+TaL+zs6N6va5er6dkMhmq+zHXsL+wezikPCtg9WBZ6H9SKBS0sbGher0e1h+Mbb1eD5XT4vF4qOYFi4kTyx5jblnzsJckv7ukjb2BxIy13uv1AvvoMi6vUujBHWlvP87MzCiXy6lcLg/kCEl3l1hKCqD/0qVLqtfrgaWbnJxUPp/Xk08+qUwmo8nJyTuC10ajocXFRa2trQ1IsKK9yIrFoqQ9eQ3MDrlSLlEsFAphfHnmMha5XC70MyPPhnmHPeJZBjDiOdHr7eU/8Z4hEMBzkQCU5yJKe1IpWDWuE1DtwRUP8iQSidBYuVQqaWFhQadOnQqA7cqVKzp//rwqlcp975vDbqVSSceOHVMsFhvo+3YYbWFhQf/Rf/Qf6YUXXnhf1QNDoDO0oUWs1+upVqupUqloY2NDExMTIU/CG2nyMouyJ7wcvAeMMznONsTj8YEeBZJuqxzm0beog3sQ4+Eaf+8rg/FS6/f7oedGNEro5j0oKFLAS9qjwQABxsDZCEqiup7d5WU8/Imeej6FSwBxKpH74LwiB+Ee/CXv18gLnn87a4Zj5nPBsaK5BjiBvV4vsEquc4/mA3EP/JzkaubZX344VcwpTgwA66B1cD/moBNgiTPsuQM4UEiokCXBBt6L+Xzcybh3ci7YN1wnQNglQQ5evRcRc/4oXqKeU4GsK51Oh+aS0XlxBpTrZK8CKgHDrGNnI72ogAMhl2byfX6HVI3oP5IpOpDj3MIAOcPpElnWAc8tlzexB7gWjpVMJsM4cX3+TOK5Q3EDr0TlY3yQhNLP1e12Q1EJaW9tcUzAhj9jtra2wrpChgyT42x49Fnqa4x950GNTqcTgjvRfCfmknkFWPLs4dlIsIMCGQAdilJICrl6BF6iKgKYaWe2oyy/f96fUc4s0xC2UCgEqTN25coV/emf/umwCMF9mq9nAK3P02GzbDarZ555RvPz80OgM7ShPU7b3d3VrVu3lEgkVKvVQsJsv9/XzZs3B15mOPdEjMkFGRkZUa1WCw6QMz38n1yRbDYbnHgcPgcELj3xnB2PMuKA8js0r0TrUqnUAHAolUoD0cetra07JooTHWw2m9ra2lKj0QjOnXdv9zwYB1vVajV8b3R0NGjyndnCuUK2xBgiU8K5JKeAZHVeyGjYq9VquC8cSKKm3uciFosF56HVaqnX62l+fj7k6njSO04H18E4JRKJUM3KxxLQ5E6cJ82jU5Y0AJpd2scfnGG+j3P3oOWme71e6PxOBBjpD04b63pjY0MrKytaXV0N172wsKBCoRDA37udq9vthvm52+d2dnZCmWCvQtbpdDQ5OTnANDhwZy3wO3InHkW+USKx10OJex8bG9Pk5KTW1ta0trY2cA2scyqmbW1thX0AGGu1WgNg3dlReszAIDjL6SAHZx1AQ7NHngsrKysD+4GgBsVI0um0pD02DYBETp+PMdcJO9Hv90PQh3UOk8BzaW1tTf1+P/y+39+rsjY3N6d0Oq18Pn/bHB0kFZX2S4o3m021Wq1QTprKe+xdB+KAnLW1NS0tLQXWi/lwCZaDBw/U8MxmrPne7u6u1tfXQ64R4JXcQcYKlmZmZkalUimAXRhdntO8Z9hb5HYRUCJXz8E+1wZD75JBD8DxHHMwxvrd3d0Nz5RUKqVisaijR48GYIx9+9vf1j/9p//0UDvp92tRdrLT6QQ1xGG16elp/dRP/dT71j8HGwKdoQ3tDtbtdvWDH/xAR44c0cmTJ5VIJMILxUt5ErWR9vtxIENyzbTrrV0zj1PrzADSNGn/JewOcFSOEM11ibIhfN6vAaeIUrg4VXweAzDwEu719pKXAR0e5WUMog9zmBTPkyFC6ffmunYHOJ6jhAwQJ8pBIE4VUX5+700CnTnDUeDnroP3iC4R0CgzMTo6GuR0rs/nvjgvkkF3wB3YcL0OfKISIhwbiiJEc6ruxRjfVCqlI0eODDQpJXrvzQkZS9Y6siSc+LsBHs71boCDiDs5E6xPyhZns9kwTw4GOAdr2NnTezXm9l5ydDgXnx0bG1OhUNDm5uYAm4G8kaAFkkbALCAF55059IR4gJ87r6x9b9DpaxVWM9rfSdovYR9lHTAYM5dQUa6ePcF1EYDwEsebm5uqVCphLzkY9SapY2NjQQIXZU08tzA6R+12W91uV+vr6wHYEjCgOiRy04O+7+wxIJDnAvPgVSVh6/g3z2lkpl7pj3XnARuYNZe7wdL6/XpOF2vCnz3sdRQFsKvsg6iSwJUGgCjWN3PvwTZJIScH0OjPKIrQrK2t3ZHxH9rBdvz4cS0sLIRCJ5cuXdKVK1fUaDQe96U9VkskEup0OlpdXX3fSmzfF9D51V/9Vf3qr/6qrly5Ikl67rnn9A/+wT/Qz/zMz0ja07j+l//lf6nf+Z3f0ebmpr74xS/qf//f/3fNzMyEY1y7dk2/9Eu/pP/v//v/lMlk9Au/8Av6R//oH91zUunQhvZ+WbPZ1L/8l/9SJ06c0N/9u383dF/3akoul8HxoHEdLxacde8JgWMM+CDBFjnZ+Pi48vl8iOiREOxRY5x9aT8J3yux8XPv1o1DyDnGxsZCiVZJAWR4fgYO/tzcnGZnZ5XL5UJ0dHd3V5lMJjjdJFtGZSFEqD3vhKgy+n4cCc5PnxBPkOZFLCnk5ngOgGvW+T9OljtAvPg3NjaCjh6WiYgtTi0v+Oj/mVd6W3DtyWQydJXnPlymg+PmSeWYR0xh2lhDsVhsQC5IBPt+DId4ZGREzz77bHDKMNav5xIQgZaklZUV1Wo1HTlyJCQr3w3o3EuVuI2NjdDHhFyx8fFxra+vh7yGjY2NELX3dwWSKe9hdS+gBWOP3gsgc3mWtJdvMjs7q16vF6oykkeXzWZVq9XUbrcH5JGcEznSzs5O2D+NRmOAiahWq5L2ew4BnlzCBKOazWYDm7K1tRVYJQeonNulkuxRmE/2DQ491f+ciYA14VrYM9evX5e0L9kql8uSFCRe2Ww2SKOiY40DfhB47/f7qtVqqlarunHjRuhvA9Oey+U0MzOjnZ29Hku9Xk+lUmng++wzxpaeRUhxkcLx+7GxMZXL5QBanSlxRs8lyhwfUIgkDdZ4c3NT6XR6QM7oZcZhixx4c52pVCq8A5CYucyUYzLHPNv423Ny/HkDG5TL5VQqlZTNZgfG/tq1a3rppZd08+bNu+6Nod1un/zkJ/XTP/3TevLJJ9Xr9fS9731P3/ve97S8vPy4L+2xGQB7fX1dL730kq5du/a+nPe+0MXCwoL+5//5f9ZTTz2lfr+vX//1X9df+2t/Ta+88oqee+45/Rf/xX+hP/iDP9CXvvQl5fN5/ef/+X+uf+/f+/f03e9+V9LeBvx3/p1/R7Ozs/rTP/1TLS0t6W/9rb+l0dFR/U//0//0SG5waEN7UMNBqNVqoYws7AbyLEkB6BB95cXqn+dF5JE4XkCud+eF5BWRPNmZhGG+AwAgKkkUWNpv6icNVgqjoMDu7m6QrkxOTmpra0vr6+tBo31QdDUWi4XE+cnJyQGHCWfOq0YxPg7QKIRARBgHnjHjxU8eicswKKrAfdGckEi0V3JjnnBePI9G0sB4SPsyPwAk18R1kT+BM+MNGX18PGpLGWC/Rs4lKUiJOC6Awvtt8F3mHoki6yFa/vdejHUWnV8cO86PQ876JPG82WwGp57Su/fLLrEmkNIB6FhLADBAnssGo/cCywCz50zN3cYmyrBVq9Wwl2KxmIrF4m0yRP8uAQOPuCOJIsLP9SMj4nr4GffMObhH2B6cf99XXDffxfnG6Wb+3BGOzjP7SdKAAy7t7Sv+SApFO3x/+/NO2pfeEYSh6lk+n1cqldL8/PyBIIexiLJ10n4+WavVCmDBC6F4Ps3o6GiQ5MKOdDqdUK6c4APlmClPzn4n4MLctlqtADIkhf3rJZwJDBFccVaYSpbpdDrI/Lh+D0j5vcM0EdDpdDph7vkD8HRw6yCH5x/yIC9MQICJa2QdIZujIAEg7e2339bXvva1ENwe2r3b9PS0nn322VBw49q1a3rzzTdDUZXDZM7oStLNmzf15S9/WW+99db7cv77Ajp/5a/8lYH//8N/+A/1q7/6q/re976nhYUF/dqv/Zp+67d+S1/4whckSf/sn/0znT59Wt/73vf02c9+Vn/0R3+kt956S1//+tc1MzOjj33sY/of/8f/Uf/1f/1f67/77/67B6qEM7ShPSrr9XqqVqvKZDKhlCxOrUui+Ky0X1p1Z2cnOEAAHRz4qFQFEMJLCnaBaK07EuSZwPK4HAO9NdIVckgAUESuyWXp9/uan58PTsnKyoquXr2qWGyvh86dZEnZbFbpdFozMzNKJBKhiz0RzWh5XGk/qorj4z8jkktSM9dK5TnuByfIGR0coXq9rvHxcU1PTwewQqlUwBKyLwc8AFRA5cjISMgdwOHlXtDfe54LTorr5zGcp2QyqU6nE/K5YKBisVjohwEL5s6UtF/EAofT84Tc2btfoOPncENmh21ubqrRaIT5ReZSr9fVaDQCCMCZuh9zkAPzgeO+s7MTJE447cgto/eKw5/JZAYccI530H16kMKvZ3l5WfV6Xc1mUyMjI3rmmWeCVCm6F8g9glkjes99IdUkz4zgRbSwAnPIuHvuBI62syouUcWp9WcKa5br5bkRBQU7OzuhL9b09PTAeWH1AI+pVGogoZ894+wAktdUKhWaqsJm5nI5PfHEE3dcp3cCpLAvBJy63e4A88M9sp5nZmYGChBUq1Wtrq5qcXExXC/sTSaTCfuT8wNUdnd31Ww2lUgkNDU1pXh8r3KigzzYFXomueyQvQyju7q6Gr7DfuV94Mzn5uZmYL8JbLA2fex5vxD0cKDDHqFaYT6fD885rqvf74drkfaY/6mpqZCfhWrhlVde0b/4F//iwDkb2t3t6NGj+uQnPxnyCy9evKiXX375cV/WYzHen4Dvd955R7/zO7/zyPvnYA+sF9vd3dWXvvQltdttfe5zn9PLL7+s7e1t/dRP/VT4zDPPPKNjx47pz/7sz/TZz35Wf/Znf6bnn39+QMr2xS9+Ub/0S7+kN998Ux//+Mcf7m6GNrRHYI1GQ1//+td1/PhxfeELXwiRNlgHnBCi/O7oO8MQjYjikOD0Un3NHeZobgbmWmy/Dpweb9rnVcvcMbhTZHVjY0Nra2uhN4Xr0f1zs7OzQZLVbrdDQnZUo+9RQ5fTuDaea+UzOKuePyMpOPxeGhUn0RkXoqtEcQGq3lcE54H74fzusOOIwhAxFz4HDvD4v5fklvZzbxgXcl1w7vx+AWo+b96gD3DBfdyJ6XgYY2wAYIwzkWvkfcViUclkUu12W71e77a+KO92DhK8HeAhh4JlyOfzoedKlIVijFnzFFCo1+taWlrSzMyMTp06dVuzVWfWer1eSFa/ePGims2mut1ucP6QCbIHWAPM+fj4uI4cOSJJQc4J04j8EsCFw+k5I6w9pKqMDawq4x/9HkyfO/6sbwzJKgVAmCcfB1g89ij7B+aXsYch4/vIW2lSzBjxvKNfDkwBoN2ZqoPWhMvAKpVK2EMUVOn1emF/0KDVn2uwFvRk6nQ6arfbAWA4aOX5g1yNser3+4G1JL8J1tHzmRKJxIC8lGvnubG5uTlQsMUluPw/ev88LylUwz0zrgBRD7CwjqPvFz7D+nOZJkE1Gjjys0QioVarpatXrwb55NDu33w/HuYCBJKUy+X06U9/Wk8//fRt77z3w+4b6Lz++uv63Oc+p42NDWUyGf3u7/6unn322dCTIVoXf2ZmJpQkvHXr1gDI4ff87k7GwwI77MlcQ3t/rdls6o//+I/17LPP6i/9pb804CTxQuFlyUtP2o+kRpPUnYWRFF4u6XR6INrrEhekVAAeT0TFAY06ADgS7qxjdwI60t5+W19fDy9ZnCG3WGyvm3y5XFavt1eOu16vD5RFjn6Xl7ekEN0kog9LRBnrjY2NIA3Deen3+8GBRBbDGOA44ISSa8BLu1arhS7k5IDgCHG9AAjmyaU/gD3mweVvrvtnLeCAwORwrawNKti5DMlZCyRRuVwudL1nzZAkjnNLP4330gBrrGnGGfADi8d84oxGgeK9nIPz4Gh3u90A6hOJhAqFQgBU0WM70JmYmNDm5qaWlpZ08+ZNvfbaazp9+rQmJydDHlbUcAxv3LihpaUlXbp0KVRGSqVSOnnypOLxuKampgbm0PMyqPrV6XR08+bNsK7Y/6xdnElyTKJAh8qOsEKsexhJghnOSFJqmedLqVQaYORY07CUHAfHKypzolEugKzVagVWwkFYr9cLEqf19fWBYIUDnUwmE+SOfj8HBU84Nntpa2sr9FQiEMT8kydE1bMoM+d5NwAdd+pZt/7c5hlJnsqNGzfCeo/FYmGcGV+kaaxVpH8EBUZGRkLPLths3g+MkRcf4dq5F5gdB7mMAe+AKMhxkAiI2tnZ0ejoqPL5fAB6LoM9COg0m01dvnx5CHQewnhvDG1PBfKpT31KH/nIR4JvwHPx/bD7Bjof+chH9Oqrr6per+tf/st/qV/4hV/Qt771rUdxbcH+0T/6R/rv//v//pGeY2hDu5Mho+p0OlpeXg6RZV7KLjtzBsITTT2PhpcaZXB5MfJZwAIvPB4GyWQyvFQ9EZXInLQvqeMlyDEoNkCOy+bmptrtdpArIMHBYaP6FcfwalMYbML09LSy2WzQ0i8vLwd9PGyLS8cIVPAS4D48Ui3tvyhcF8/4Mc5ET8k14rv8Dqez2+2GQgU40jhPfj+clygojoQXl3A5obNq7txFGR6kdDB7zgagzZcUHGOYKMDTxsZGiJDjcFNSmDGNOrkPY6yhQqGgo0ePhusHyKysrKjZbGppaSlUvXKQfi8G+0IJaqLOsIRzc3MqFouhKpyvP0/evnHjRkjQxknHgQOcRSPnOKBvvfWWlpaW9PLLL+vWrVuq1WqSpFOnTgVnPMqGsh9YG+RyIYPiMwQd2ONekZFEegADn/WcPAAgDURp1onjitNLjgiBDgoLAPphoJ1ZRhIq7QU2aIiMU5/L5QLgaLfbgamjshkyRhznZDKpfD4fGk6mUilNT08PFBrxAM6dJGxI1Jgzcg6RmZFXQslxnHQPInW73dADje8zLnxOUljPPOv8ucDneC55KW6eS+l0OqyDaOAIyaeDEFgfaf+54bI5ab/BMY1M/f1BIHl3dzcwc+wdAJGDTe4B9pVAAdfL906cOKFisRiCR71eT2fPntVv/MZv6OLFi/e8n4e2Z5OTk5qcnAzFOGiCfJgD9OVyWV/4whd05MiR+5ZZvxd230BnbGxMTz75pKS9qhI//OEP9b/9b/+bfv7nf15bW1uq1WoDrM7y8rJmZ2clSbOzs/rBD34wcDwqUPCZg+zv//2/r1/+5V8O/280Gjp69Oj9XvrQhvbAhra7Uqkon88HKQh6ZxwYlxRFHTOXnxEZ5OXtzgDOO442L1xvisnxPYLqic8uN5IU5BNEipHe0Y8GlmNycjL0TuHlTaTZdf9YPB5XsVhUNpsNDhHJlh7l9/LJlArmpc4xARLR/AVpv5Giyy84Ho4jziHf5f8ATAcrrk13cMTf7hR6eWX+5jv+fwBZJpMJ53NGx5sLes4S18r5cbokBbkMzq3n5QBWAZgAoffCmJdMJhOALFF97hcHiqp93qvpXgxmyMcbp3l8fFylUklTU1MBCLgBdNrttlZXV5VKpQZy3rx3jJeexshBOHv2rN588039+Z//eXgXjY+P6/jx42GsHeQwNs5GOHNJYnuURSXQsbu7X6UQORiBE+SUzC/sICAdAA3Qd6AB+ACcwQoBqCl8wvMCcCbt7SGP3OPE93q90Guq0+kMVFtz5pq9mM1mlcvlNDs7GyRrzLNLVKLj6cYzlmvnPIASgGepVArSWt83MEb0+GIcPFDD37CnHmCKPucAC/1+f2ANMn8OHFgb0mD+pedgMrcuwYOhYe0x7j62/I4GtczD5uZmKEoASGOOAWFU9+P+uC/Y4NnZ2YHCBru7u7py5Yq+8pWvHDhHQ7u7FQoFnTx5MuSirq2t6fLly6Hwx2E0GB3kru8Xk4M9dE1nnIlPfvKTGh0d1Te+8Q39jb/xNyRJb7/9tq5du6bPfe5zkqTPfe5z+of/8B9qZWVF09PTkqSvfe1ryuVyevbZZ+94DpK6hza0x2lra2v60pe+pKefflo///M/H14eOGoHMRKeMIujQSSWjve85Ih+IonwnhTOCCFhSKfTwWnmxVmv14OcJh6Pq9lsBvAQj8eDPA7ZQqPRCM5WLLbXuyeXy4WKap5/FNX/u5G0m0wmtbS0FAAgCc/SftlZ2AEaGwJGPKIKQMDhZsy4H78OdPUuCXHHFpBBEQccJnKiGFPuF2kRoAkZIM0ScRhwUJ2xoky4J84DbpCbkLvhQI6f01SV6lRcB44sDA73FYvFVKlUJO0FoWiw+bAN2WAKSqVSiPTiIG9vb+vEiRMh94IcFdbWvVivt1eAoV6vDzAnOI+7u7vK5XKhP8pB36e7/eTkpBKJRABNXBeFNpBCLi4uhvVEDsfy8nKQuq2urkrai6qvr68rl8uF8sV3i0KOjo4ql8uFfj4epOD/AELP32D+kKQShADMe8EBDwhwXM/9kPYT0Qk2bG1thdw0juO9eTxA4+dxRoA974EYnkus6cnJSY2Ojurpp58emDOvRgYYO4gdBjgRKK1Wq2GPHjlyRKlUKqwtWBIccjcapK6trWllZUWdTkeNRiPsSw8Q8Jzh5/QFY1wB+bAriURCxWJRksKzjXniee05Gf7chx1mTyJPlBRkdwRBAD3dbjd8BwaHtQHrBhDmWe6BLp/HdDo9UGGN53+5XFaxWBwIJKyurur8+fO6fPnyPe3jod1ux48f10/+5E9qYWFBknTmzBl95zvfuWt6xmGx9fV1nT17VufOnXtfz3tfQOfv//2/r5/5mZ/RsWPH1Gw29Vu/9Vv65je/qa9+9avK5/P623/7b+uXf/mXVSqVlMvl9Pf+3t/T5z73OX32s5+VJP2lv/SX9Oyzz+pv/s2/qX/8j/+xbt26pf/2v/1v9Z/9Z//ZEMgM7QNvrVZLL7/8cnhR8GKRFBwWXr7OSmC8ZIji4XR4QnxUusb3eVkDpHAKvdoZUgZevB7ZBWSQEA3zRHRwe3s7vDABO17210u6HmSx2F4FMRwZr0bHi5mfE9HmRex5LzA03AfXiSQjGjllnHA6YMdcFse5mBfmgUIL8Xh8oCKbs18uQaJIAtfIOCJjlBTkNplMZuCZBhDyaHJUIgNLQp4SjphX1+O6cGpxIDc2NkJuQTabva1v0P0a40YEznMPYrGYJicnlUqlwrkow3wv5gDao/3cD+NFPtWdjoFjSi4ZeRjk46TT6dDDxKvkxWKxMGatVkvNZlPNZlP1ej04kMgCAUp3G0tfn4wdgQ+XogFoogVCyJfBGWW9Rde4mx/Xx4TrcDBEBTuCKw50AFee7+YSTk/cJ6Di+5aCFJlMRjMzM8rn8wNr76BckSgb5tJg8ml4xhaLxZDjwzG8iAv7mecm/b0ajYba7bYqlcpAniTn8gIYADryZQhwuIwsHo+HCD1g1cuA+z72fcC9O7PHHuZvXwswkN4WgHnkec56AWi6HNrXCesSeaz/fGxsTLlcLuwV1kyj0dD58+e1srJyx/U+tLvb5OSknnnmGZXLZfX7fd28eVOvv/56kMUeRuN50Gq1dO7cOS0tLb2vrM59AZ2VlRX9rb/1t7S0tKR8Pq8XXnhBX/3qV/XTP/3TkqR/8k/+ieLxuP7G3/gbAw1DsUQiod///d/XL/3SL+lzn/uc0um0fuEXfkH/w//wP7y3dzW0oT1C63Q6unbtmrLZrMrlsprNptbX1wNrwqbGiQNc4CzicNH4D2eeiB+fp1GdV13i57A/yEqiDUo9gbXX22so6OCD3wN0eLkjiZmamgrlVbe3t1WpVFQqlQYcsKjxUj169KhSqZSq1eptEg4YD2epNjY2Bpw/IuS7u7sBFFL9yEvcIr2IxWJaWVkZkD9RJMHlOzhGXv0Nh8dlc3yH4xG9ZoyReLgkCAfGyz8D7qR9oEq0m6i4s23ODPAdyi57Lo/LGcfGxkJZ7rW1NdXrdXU6HWWzWc3Ozg6ApocxxpA1Mj09HcCIA8l7sY2NDVWrVW1vb6tQKIQINnM3NTWlXC53W/NCabDZKvc9MzMzAFbJucjlcuG+kbjxf2RxL774ovL5vNbX10NuSjab1RNPPKGjR4+GHjD3Mn4TExMqFArBWY+aM3y+71nL5DkBSJAlRhkcCgVsbGwMJJLHYrHQpJQqbs4EAS7dkZb2S4rzeyyTySibzWpjYyNI0mHber2epqenlclk9MQTTyibzapQKNxWhps9xjMtGihptVqqVquq1Wrh2mOxmMrlsqampkJuDo56Pp8f2J8wNDdu3FCz2dSNGzcG+lshu5X296Dvz2jwyKtesv+Zf8/vcVYOJg8WxoMoDug3NzdDqWdkn/QG4vkMg8MzAHDMuLEekKXB0nn+IKAJkOU5Qv1+X4VCQblcTnNzc8rlciG3jSaOv/qrvzpkHx7CZmdn9clPflK5XE6StLS0pHPnzgU54mEynkMECa5evap/9s/+mRYXFz+4QOfXfu3X7vr7iYkJ/cqv/Ip+5Vd+5Y6fOX78uP7wD//wfk47tKF9oGx7e1urq6uKx+Oanp4OUoZoDktUMsFnPPov7Vdn43P8H6aF73F82CReokgxcLTRhPNyk/aj5f4zl6UhTZP2+2HgXHn/hnczHDacc3eeXIoFcADokIDukhmX+VHqFScNBgp5iZcYxtFwOZk0GJEm6R8wBIvG57je6Hwyxp5X5c4h48e94ix5bxMHfT6vMFPRIgiAGq7Br9OvEcldPB4PYwozg/TsQY3rxLmkXDBO1f0YAI4kcWcr+/2+MpmMSqXSHfvfAHLI9YKtII+uWq0G9oxrI9Fe2mdIMpmM5ufnJUlzc3NqtVqhQtvU1JRKpVKIdt+LkbDua8mvO8rMsA5YP4BZHFKAtbMjrAXfA76meBZks9kBholr4NjuZDijw7W43CrKGkgK41cqlTQzMzMASv3Y0SIE0XEhab9WqwXpIeNIlUQfMxx8L7VPXzCOwzrlGeqVyAAJDsjYx4wl48aeOaiHko8DgSWCEL73nZFnLv14qAB4zvG8cDkix+L/XJuvC+/jw9xxPn9/SAr9kZBmUpymVqvp+vXr+vM///OBuR7a/Vkmk9Hs7OwAi4G8+LAZ/gDKgHq9rtdeey3koL5f9tA5OkMb2mGz69ev65//83+uz3zmMzpx4kToKt1ut2+L2uBsuW6bFx0PQgAGrIoDI9dn4wDj1BPFdmaDFyJOtFeeIgqMbIHypzgCRNKj1+8SmneLbCcSCZXLZY2Pj+vJJ59UvV4P0UGXifCC53heUMGla0S7pT1HkOpuSDmohMQYuRMUzXOS9pt4OhikwhmRY5wJd3hwJviOtN/LByfQZWaADkAGQA4AWa/Xw1wwru64kGuF7I/rIs8H8OdgB2cHJvDs2bMaHR3V7OxsyFl5t3KnrLloMQ1fw/5z1lm0v83djPsiWo3znk6nVSgUQtL7QQCKPDT2GoUu2B/sRdhRxsbLU8fj8ZDH85GPfETHjh3TyZMnQyWreDwe8s2kvYIQ98KKZTIZzc3NSdJAThXzR44OzA1zi8HewXaxtgE/zhiyL2E8M5mMUqlUYHcByMhJebaUSiXt7u6G8uC1Wi2MNwEW2AnmlT3Ln/n5eaXTaZ04cSIwAtju7m6o4MYclsvlgcR/XwPValXLy8sB4M7OzoYCFNFKexjP0F6vp2azqUajoVarpVarpUajoXg8HlgYyj/zfIjFYiHnJ5VKaWxsTPV6PTwHe7290vW+/rkO1hrPpfX19cDitdvtMJYUctjc3NTExERgxQDnAFTPiSJPhn2EHFNSYIBZ/8gzm83mgFR4fX09FCXxXCHmslgsKpfL6fnnnw9qBIIyV65c0W/8xm/o9ddff18j7R9G8/fwYR/Lcrmsn//5n9ezzz4bghSex/x+2RDoDG1o92mdTkeXLl3SkSNHgvM7Pj4+wOxI+5FSHnquged3Hu3EKcIxh20AzBzEyBDtizqmOJCeBwAI8Gvw6mzeN4drcvBxL44szhcvVuQ0gIfoGPi1u1TMZWceZXX5HRFbBzH8G7Dj/SEcFHAOHA/0+sxJVH4j7UdXHaB6tJh5cpkc9wTo4nPkCTjb4pFXQAv6f2d5cE5g23AiuY7t7e0AYnGEdnd3Qy+YuzEwnCM619G5in4+OmZ3MpdNsiYZd6Q7LvE6yLz3DgCQ43iRCSwej4e+RDh/OLmFQiFUluv3+8HhhV1xBvbdjPwzPz/AyaWkvh59jwJQHCQz3g6anPHlfli30TxB1h8/I5cNAEAgAaaQ6/L8H2dHcLKLxWIYu+j8AuiYS2/my2eQK/KHCmqFQkGTk5MD83/Q2uIZ2el0Qgl2l7TyGQdW3AcGMGYPMj+sE9Z5NDDFmqB8PnuOvcizC+NYHrzynJ3oM5FnqFcLZA8ThADwOpvHsWGw6Q3kBTKSyWSokkkwAEbsjTfe0PXr1w+9c/6gxjPMc7cAt4fVksmkPvKRj+jEiRMDEu/324ZAZ2hDe0BbWVnRt771LS0sLOipp54KOTkuQ+LFJ+03lCPB3yM/Xv2I6B1d1vmcv8ApJUykUNp3bDgeDiAacSK0/vtWqxV+huPsziB9Jsgrcine3WxsbEynTp0KFde63e5Ap3EcOSLw0VySdDodktGJSsKcJBIJLSwshPGgnCxjRESaXJqodEdSyBVCQoXjgOYeZ6LX62l1dTWcF2eCXCZvZsz33ZHGkep2u6HMK45mKpUKTjF9MKrVanCEYHA8mZpjZ7NZNZvNwCA6kHQ5nqTQH4bI/5EjR27LlcA4Z7TCnjtrbgDNe5GvbW5uqlqtqtVqDSRbE2Gfnp7W7OxsGJOD1hj7B0YHp9nln9lsNnw3Kj3DkXQn1n/vuQ/R77ybMeedTic0HY3uF3K8HHQRYFhdXR2QeUYLaHhuDgwDx0IO6iWdcYx3d3fD/qCCXrFYDM1ZKajBWLBP+Bl/JicnNT8/r6NHj95WfILnjFcEpCohpY+Xl5cDYGs2m1pbWwvPqGKxqPn5eWWz2YF15z17Eon96m8wOG+//XYAVgQFGG/GnL3G/XQ6Ha2vr2tyclLZbDbsnU6nE6o/jo2N6ejRoxoZGVGr1QpjCEs2MjIScqiq1WpgUj2w4vJiSnb7M9xlis1m88AKnI1GQxMTE8rn8wPFO7rdrur1ulZXV0MxCNaEN/llDUWBFNfQbDZ1/vx5vfzyy/rhD394qEsgP6ydOnVKn/zkJ0MF4ZdeekmvvPKKLly48Jiv7PFZNpvV5z//eU1PT9+WG/h+2hDoDG1oD2idTkfXr18fSFZ1BgTnyxPIpf2KSZ6QHnWmPKrqLAQO8J1eXHzX5U4cS9LAC5iXMFFJnJBoLyCYC5eAvZvF43Elk0nt7u6GBFyvZuY6esrHE7UEVPB7dxS5Ji/RzX24I+RVyjzi6YwYEW3GFXOJVlQKxxgwt4ASju1yFC9MQNKx0/bOauHYuPMT1dZHo/kHJXdL+4UDGGeq+9GfhNyTO8nY7jTHBzn79ypX47q8aa7n/URZins5rucy+PX4z6IM1EHX7+e6F8B2JyNIQaU2rz7oeVa+b1njnjDue90ZNgdLvjep7sceiMViAz1TfN07Q+oAx8fc1yQySBhf8jq8fDlrOpo47+sTBgRnHckZc0iuVTQvy5k/af/ZScGHWq12G3MRfSZ6pH1sbCwUs/AiKD7Wzm55Yj/PChoP+7352vH9zf8ddEQj2pwXJ5B1xLvB85z8/nyf8+zwteSMIYDTc552dnbUarX0zjvv6Nq1a6rVaoeafXhYKxQKeuqpp1QqlSTt9Yg8e/bsoa62RlAlmUyqUqmEHnvvtw2BztCG9oC2tLSkr3zlK2q323rmmWcUi8U0OzsbXjC8hIiSS/sgBEmHpBBF85wAB078239G5JGcEPI/iG5GE2h5YZN82ul0guNDhLVarWplZUX5fD50dZYUmJ37lSnx3aeeeiq8RNfX10OOEU4hkU5e0u4USvuOAlFRxoAxJoIZj8eDnCSZTCqVSoWoMszU9va2ms2m2u12cOYANUhgkC0BTGB4XLvPteA8UCULZwpnAicE5oefezltevHAuGxubqpSqdxWXAItP8ATwMc11et1bW1thTLJzBHjV6vVlEql1Gq1VCwW9dRTT90GFKJObxTA+Zwc5IDdzRgHnHBnGiUFKVQul7sj4xSP7+XXwIYhpbrT5+9kd7qv98LINSHCvri4GMaePYqTzdy6XNF7q+zu7gYJIk47YCAW2+sZVC6Xw9plT/tzgM8CNhwcEYSg540zozMzM3rmmWfCGp6ZmQmV1Xwca7VayKnjmB5coNTz2tpakKxFmauJiYlQPMOt3W4HaRuBjW63q3feeUeNRkPr6+u3ldlmj8HkAADZm8h1t7a2VK1WNTMzE0qRIwFNJBIDwAM2GzAxMTGhYrE4IC+mLHmr1QqltqNFXAg6uDyT4AnVLUdHR0MlQSqiLS4uanx8PDD52Ww29Ali7SMVjAbGjh49qmw2q5MnT4Zn1u7urpaWlvTyyy/rH/yDfxDGcWgPbqdOndLP/dzPhR6Rr7zyin77t3/7UFZbi9ri4qJ++7d/W2fOnLlnKfB7aUOgM7ShPaDRt6FSqWh5eVn5fF6FQmGgilTUIZT2o8wHRY89JwAGwQEPn8HBx0nyiLCDEWcQotFAjyD7yzr6IOKF/CBJhDjjAA96ZHi0k2vwEtl+nZhLQvgcjoSPgcusnF2T9vt6IB9zVgBGKBpxd1mUs3Y4WH6O6Hn5DNcZ7UsSjfpGWQq/Fpe74BT7dxxcRRkAzicpNECk70y0h5mvSyLcd2KP7tXcIYwyVn5/7wY6ALyxWGzAsXMQzuce5HoPAtr3awDXTCYzwDRyXcwbfzMmLmuM5qj4vvP75XpxwLn2KLvgz59oDk7UAPLJZHKgmpo3lnRjbbP3WCvsNcAJeVWeP+cs3r2waTAr9D4CtLAPfT1wL87uAKyQeLnkE1aVfcb48QcAxLGjzCPsr+c9+r6NPifYy5xf2g+AOBBkjP3cXC89iw5ifDgvVday2WwIdHW7Xa2srGhpaUnXr19Xp9N517Ef2t0tlUppenp6oEDO+vr6Y76qx2M8nymS1Ol0dOHCBd24ceOx5IANgc7QhvaQduHCBf3mb/6mfuZnfkY/93M/p0qlEqKcFCjgxRmLxUJkF4d7YmJiQJoBk9Dv95VMJgMVzssW2QVacsodE3H0zuXkhRARjYKCzc3NIGuQdCDY4Lrvp1dK1FKplE6cOKGJiYlQccwTid1hO0iShwNDfg/3S4SU68OJcFDG/bbbbVWr1VDbH0fLWbFYLBbyOpCueWRXUoiY02eI3CL/HnKR1dVV1ev1gWg952UtMC/kReD8RR00zt1ut9VsNkPlKCLVY2NjIU/B5UuMK3khKysrajQa2tjYUKlU0rPPPntHJ5NGjp5fwFjcq8wLJqHT6YTIPw7dyMheU1IKApTL5buCHcaMOaOAAH+zR9LpdOhkf6fjRMFMv98P+Rj3whIRlIiOBRXQkGOtr6+HyLuvbZeZISOS9phdgOHm5mZgfFmP7njHYnt5FpyPfiysn2azGY6F0xuLxUKFN1gMPp9MJkPD4MnJyVAY4E5ABNC0s7MzUBKc/BIqq5FHB4NcKBQ0NzcXwJBXbnPL5/OBxWQvdrtd3bp1K9xbdE5jsdhA0ZWtrS01Gg2lUin1+/0gLSQvBiaGZymBAuaf3J1msxlAkoMdGNxEIhHm2hleb8iZyWQG2H6kPDBqvd5eb7Tl5eVQPIB7ZL5YP+Pj45qfnx8AkYyv5xLxbGQsY7GY1tbW9Ad/8Ac6e/bsY8ub+LDZxMRE2C+H3SYmJvTCCy/o9OnTGhkZ0fr6ur785S+rVqvdd7D0vbAh0Bna0B7Sut2uVldXw4tcGmRtPDIr7Udkvb+LtB89jEbgif7iUPAZj+hHzxFlk9wZi7I4vNBJ4scpvVsex/2aRxYpYY1jyvXu7u7eJrnj9y6FIarLPeIwe9+QKMPiINHHIVpdypkrBxp+PV7hKeqU812cR8+TcKfYga/fJ59nHPyl4MxQlPWLshn8zdw7WMKxqdVqQcYIUIoaY/mg8i7GGGeMcfZEe5iDaM+UqHkuA/kgfhwcPJw9cjfulZ3hXu8n4njQuLAncV7T6bQymUzYt8yZn8/3qjOOznr5sZ2x9SRf9hDMCkEE5tDz9zx3hBy5iYmJ0MOGEvRRIOd7j/MTjCAXj2AM1QxjsVjIwQNQZbPZsCfvFERhrbBP2+12KAPO2vY8NtaC32f02XEQE+P7hLGM9tbyuWEuouyJ5/l4BTj+jjKZXvyB54azSNFr9PXf7++VyqdSoD9rPDeHinasOyr7XblyRTdv3nwsEfYPk3lgC8kje+Cw2tjYmE6ePKljx44FdrfVaoVeZu+3DYHO0Ib2kIaO/OrVq7p69WpwMqj6xQsGB5UXU7RIAY4IL0EenETyWq1WeKl6hJYonaQg53BnJJFIhMi/R0a9lK1HYBuNhrrdrqanp4Pe+H4s6nBL+1WYyuWyTp06pcXFRdXr9QHg4hWq+IOjFnUOiV7ihMGK8bN0Oh3+OAvmuUk4T4wN41+v18P1jIyMaHZ2NsiP0PW7IwXDQO4B48/95HK5cL3ImriOKOuEQ+nf4Xdcv6QgLaJBJUCCc7JuHAi747SxsaEbN26o3W4rm82qWCxqYWHhtrkkD+ZBgA7X3G63dfPmTUkK+VLSvgxpdnZWc3Nz7xoJ3dnZa2B769atkMeWSqU0MzMT2A+cd7rF47TfqyE7uZf7fTd5HFKhkydPKplM6p133lG9Xg+5XjCI7FUvIJFIJELD3WhhjrGxMW1sbIQ8n1gsFv7P2I6PjyuTyQRA7OdiL/b7/ZAbc+zYMRUKBS0sLCiVSoXcnYPub21tTY1GI4xxJpMJQCkej6tSqajb7QYgXS6X1ev1AvMmScViMZTn73a7d2R0AA+VSkWdTkeXL19Wq9XS6OhoAEqsMwCGpMCUO3hjjfAcAYg5yOn394t3OFAmp4brcXmb55tRJW5zczPkIpLTQ1ESyr/DsuAE9vt9lcvl2/KyMJ4DXlaexshcqxe/oAT4sWPHVCwWA1u1srKiixcv6qtf/eowN+c9MFQXyDyXl5d18+ZNra2tPeYre3xWKBT0H//H/7GefPLJgcIlj8uGQGdoQ3tIw8FeWVnRa6+9pqNHj+rIkSMDZYFjsf2+J14lx5kdTzrHEYpGOv0li0NEAm00T0fa77YeTXyWBqUeXjQByU0+nx+IJj+sEfXNZrPhD13upf08DhyBqIwt6lxIGnixEyFlzHZ2dkL0F6cfCd9BUVnGxrXwfh04oVwHUTxniHBYPbrq5+L/zrDwO3ei/B7999J+qWeP7CNfI2LteTtEvzkGSe4wd9VqVdIeuHLd/0GM1v3Y7u5ucHpbrVZwsLkuzsWc3O04PhYOWHFypf1S156ncr/X/m6fP2ju7nYs7hnGysv3sveQqnKfzkJ6ThnSRq7DKzeSF7K5uRlArgNbX6OJRCIUPJD25n16elq5XE7ZbPbAvC23aN8pxoKSxowRjDCyWGdckWzCYDjj4fMAM0eVNQC9g0zAHGvBHX5nOnnW+n7nemFTogymMzCMPaxwtPy3V490mas/z7gWnvkHrSWO7Tk9HizzteX/5ro5fy6XUy6XCwVZYrE9yeJLL72kN954Q61W61CzDu+V5XI5Pf3005qampK0B3RefvllraysPOYre3w2MjISGpAf1Ej9fb+ex3r2oQ3tQ2Tf//739fLLL+sXf/EX9cQTTwRpAf0R0ITzAsOZ4OWLgwmDgaYcKQUyB3/J0UsDNsar/MTj8aDT9pe0tA86iHZ3Op3gUNCDg+TK+01Ev5sTODExoZmZmeCkr62taXl5eQAExuPxECVGzsSLP+rE5XI57e7uqlaraXx8PHR+J6J869atgUaUc3Nzajab6na74b6Q+uAUUf0Jp4eIFOAUB46eJA5YYcokBVkJTnk8Hh+Qw3B8PwdAyFk5T7JmnXhOlYM3/xzNIWu1WpCIMf7dblfLy8va3t7W+fPnVSqVtLOzo3w+r9nZ2YHGdw9qnU5Hb731VmC6xsfHQ2R5bGwsVPd7t4gfDCiJ1M1mM/RASqVSKpfLSqVSoYeMM1kHyfHuZi5jjBryOIIQ92pUIaNvDCwlQQjAIM8B36veO4Ux4JkCs5FMJkPjTVggerwA+jw3L5FIaG5uLiSo5/N5PfHEE+Ge341Z4zkxOTk5kFtSqVQG1iigqVAo3AY6+Xd0nbmENR7fqyTYbDZDtUacc4A8eWmUb280Gkomk5qeng4MFkwZAM2ZGZ4nW1tbYWzb7fZAwATWp1AoaGtrS2tra4GNYV1kMhmVSqUQWEgmk0E6yNi7TJYeUNlsNjzXPCCTSqXCmPH8oyw3BlNP7yfWVS6XCzmRpVJJhUIhPEeXl5f13/w3/42uXr362GREHzY7deqUfvEXf1GnT5+WJP3Jn/yJ/vE//sehh9xhNJQQuVwuFCF4HLk54Xoe25mHNrQPmZFDs7y8rIsXL4ZGbrwopX2AwcuLCK6kgQglLz2PKLrWnhexR+zdPBJI2WY3oq58H6fIGyxyDQ9Tgeogg1nBuSbZl4ps3ifGGS+cEwc6XsXL/45WlXPnDSkgRSH4g/TJo984EFyH5wVxLJfB+bm5PiSKPlfRvCquk7kAbHq0GEP+hcMGYEM2w3G5JqL9fAfg7euJZHgAAhVzuE8i1cyfnwNJkDvxOKWNRiNIpDxXwPu43EvSv69fmmaS5E4BARgFaQ9Q42Df79q8m0XzVe7FkCsiR6zX6wPrzHM0nMV0tkbaB2G+zpBK+jPDmUXuifWGVG16ejqUKgZAslbYf1HzIIqzFr4X/HxIQu9UyOAgdgyJKWy2MzmAwei9cV7WLcVZ+NlB8tB4fK/PF9flrDrjzxg4gxRlS/3+PecJMB/NtWEP0Tw5mqsD0OFZwfXSq413DPvMi3LwvWQyqUwmE/oekTdy4cIFnT9/PhSDGdp7Y5lMRidOnAgtGQDch50ti8X2ige9+uqrOnfuXNinj8OGQGdoQ3uP7a233tL29ra++MUv6hOf+ETQZeOU4lh4TgnOLE5itKgBUhVvfIeTSMTQHQYqCOHcwirApFChDUkW0fFSqRSikf1+PzBQByULH5SLc6+WzWYDayPtyZHW19eDU0g0eG1tLeQeuKQHJwFtO0wJAMGTsHGqcZKQa5GEjRPPd3G8kdS5o8k8MG442MwBIAWQ0e121Wg0BiqySfuSOJwUzy3AGZYUGKdsNhs+h3MKcHCw4U4djADrj7wFHCYcMyLCVAtcWlpSJpMJ0sVer6fZ2VkVi8UwBzjO7XY7VIniXrvdrq5duxaYyVwup4WFhSDh8n0AM3U3Y5z9vjqdjq5du6aJiQkdP35co6OjoQfM+Pi4ZmZmNDU1dd8g/W5r2dmW+zH6OS0sLCiTyejq1avqdrthL0Ylm8z56OjobUwn1RpxdinowfyyPpGEss5Yf4VCQZOTk3ruueeCo+8yMfr9eF4TgRb6TyUSCeXz+YH1y3lhI8bHxwOTdZBxzQQ2sEajoUqlEqoTVioV1et1NRqNMBaAcpcAk4tD3g4smMtQHTiMjY2Ffky3bt0K+TQuNYZpZIz5PePmwYp4PD4AAgEbsOyAo1KppNHRUS0vLwewAYMNE+3XSw5dqVTS5uZm6AfWaDSCNBfww1jClM7NzYVgUqVS0W/+5m/q7bfffmxNGz+sNjU1pX/j3/g3wjom93VoezK+/+v/+r9048aNx1rdbwh0hja099jq9bquXbumtbU1NZtNjY2NqVgs3sYMRFkHCgO480vk0CP3/oKFIYgyCx4ljia0SvsyOJfROJOD3p/cCqLLUflJlEm6H4vFYkqn05qamgrODAnEgARJAxFUnD7X++OIeTU2d/yIepN8vLu7G0ACQAsNvkdpcdKIdntitwNMB4Lkv7jz5mDLS1q7c8g5AUmc24Eo80uiMw60z8vExEQYA0qQM4aZTCbcC+wdjjtAievwyDH36cwBY0Xxi/X19ZBPAVtEvhnAvN/fK/QwMTGhXC53z8yI318ikQiyJErw+vy5rBN2gvwGjuXmuTHM7YPmJL3bPVDoABkH98U1Z7PZgbwqb6zpSfHsR/aKN5WVBtkJfp/P5zU+Pq5yuax8Ph++40YBlSibw/5pNBpqNBq35Yohj2UcXfblzyA/X5Slc1AOwxuL7TW5jZahd5Y2mm/j1+57FpbH2V0HK71eL5Ti9wIHrHvG1fOBvNgArAlMFpK4aBCCvT0+Ph72F3uR6/CgFWPm+xCghjSP4BBjUSgUVCwWw95uNBpaXV3VpUuXdPny5WE56ffIeK47o95sNh97PsrjtNHRUZ06dUqnT58OOaOVSkXNZvOhfIWHtSHQGdrQ3mO7deuWlpeX9fGPf1xPPvmk5ufnlc1mg+OCs82LlJcZ0iGXpOEk4uS4ZM1lXN60z8vN8iKVBssOw3C0Wq3Qy8SLFeBco0f35oxuD+sU5vP5gQ7iOD/VajXk0eCQx2KxUBEJx8CTfqX9fI6oNKPX66nZbKperyuZTCqfzyuTyQTnc3d3N7ALgAocOcAL+v5UKhWYF08UjsqycEo4BuW1E4mEVldXg4TMpXLr6+tKJBIBCDC3KysrAcBhXD8R9FwuFyK+VM/DMZ2YmFC5XNbIyEiQkzHn3D/5RUiHSJ5vt9uq1Woql8tqt9va3NwM+QydTicwC/1+P4yNdHuz2G63q8XFRZVKpZDPcC/mQGdkZERPP/20ZmZmVKvVBiRM3twyFoup1WqFbvL8LCo98/4j/X4/gID7tXthN6nMdO7cuYF+R+RgUXadIAPOAc8EWE0cWw9+wER4CXU+l0wm9ZGPfES5XE4zMzN3LCU+Ojoa8pzc6Bm1vLys9fX1kBfC+Xm+ce1UBsRpB3A60AGEsO5qtZoajUZgjVjDVBL0Xlu+rwA5rDnGiKDR5uZmYGFgeqJJ/jzv1tfXB9ZHo9EIgNPzeggE0IMIiSesPEEt/gDi4vF4YMIAh7ArXk6eY5HjKSmw/F41j/HJ5/MqFothDc7NzWlmZiY8t5aWlvTOO+/ohz/8oa5cuXLPa3podzeKehAsq1areueddw51tbVUKqUvfvGLOn36tFKplHZ2drS+vh4K3jwuGwKdoQ3tEVi/39fZs2fV7/f1Uz/1U3rqqac0OjoaNj8gxvMbXGPtTplry13qwQvaAYJXOnImBvmFR7eJgnvDUCKmW1tbQRbGyzaXyw2Uso7eL8CN678fS6fTmpycDM4/TlGUwSIS7eWgGTMM0OGJvIy1a/QBnplMJjhung/lx/PIsVe4osQzUiEi2i6nQ5LoeTHSfgd6d3hxYPmO6+8xd3Al3ZaDQ2Q7nU6r3+8H4IbDjCTNQbJLqJhPQATR+s3NzfCHCDaOKFINQDn5MblcTplMJow/1fZwlu/FokyiM1sbGxuq1+vBoafwhI+fl/5FLkewgL1ENP9hGcp7+YwzM/yMtSDtM1ETExNBpudJ+6wzKpZ5c0zm0echk8locnJSmUxmoJ/Kvdrm5qZqtVpgJRijfD5/GwMEwOLf0r5MMzoO8XhcrVZL9XpdrVYrAOp2ux3koD5mnrficjnPY0TexjX6MxPwDhMCCKFwAoCX8SEnCHCEfJSAkD9fAHg8j/ic5z0lEokQLIGl4h6dNeK6fU+S5wdTRG4Sc+8MNfNCkOIrX/mKzp8/f6iT4x+FFQoFvfjii3riiSckSevr63r99de1vLz8mK/s8dnY2Jief/75wOhIeqhn6ntlQ6AztKE9IvvBD36gl156SSdOnNDJkycHOmoDDLa3t7W6ujoQucXZ5XMuRfF8E5cX8X304zAH0n603vOBkH8kk8nQc4PIvB+XqLI7Xw48sH6/H5iC8fHx+86NIJpdqVTUaDSCs+ElaAFmkgKjQ6QXKRnlZXH0XFqAU4KT02g0gozNgZI7UC4hY9xw7qlylcvlQjRa2pcFklMl7TMHXqmN6/PoNDksVJMCoDrYIvfKc4m63W4AtawReqEAdLi2YrEoac+5ovpaVDLDGPAdItvdbnfA6WJdEUFHHjc9PR2APRIrrrdQKIT5vl8DQFK8glyXra0tzc7OKpVKqVgshrkCEFWrVa2trWlqaipUCwPkj46OqlarBTbwQex+gAPAOwp0WNv0bwKodjodJZPJAPAAOVT1o58O89Tr9UIOF2WjqbL2IEZOG2uRIMDk5KSKxeJte/1OjWfdWP/NZlOXL18OAY5qtapmszkA/B3kwWgyt6w78s4cIESLBwB+Ab0AGKoRrq6uanNzMzx/fZ3Bpkv7Zcw5LoVMyAsiuAG45zmRSCQCMwNjxbXTF8pLV7uMWdqXIjrjQ1CISnP5fF6lUkmpVEpXr17VjRs39M//+T/Xm2+++UBzP7Q72/T0tH76p39azz//vGKxmJaWlvTd735X165de9yX9thsYmJCn//850MFug+KDYHO0Ib2CK3f7+v1119XPB7XCy+8oNnZWVWr1ZA/AdhB9oOD7wnGABQcOk9cPyja7xIzvs/faMQpNwpoIUp8kMNGqebx8XFtbW2pXC7flkSOc4EjCrNxrwZg4N6QJRERxcEjmokj5AwUzofL2bhvErs9oRkHAofGe/JICs4NQGtkZL9DO3I/19q73A0nhvNhjD+yHS8O4feEFAzn1XtxEJl2VgbgLO0BCZKtx8fHVa/XgxPocj+cRe9Dw70ApohQM47I0JCwcU1cDxId2CykVIAtQOK9GmNIoQhJoQAC11woFJTP50MZ3UwmE66F++x2u6pWqyHyfuTIkYEqcvS5ediS2m6sMwd5JOtnMpnAIgEcPKrvzi3gl3WAdNBlkgBwpDTT09NKp9OanZ0NMsP7tVarpVarpWq1qna7rfHxcaXT6cASRYsZHGRcF0wmzyBy5paXl7WysnJbbhzBFsA7vyf4QREO9lez2QwyRQfpjDv7AxkZ6wAZrMvY+A7PSIAn+5XxJJDB/LKePNiCfJBgCxLWdDqtRCIR5HFcL7I//s8zmYIsXB+MfTqdDoU3YO14Jn3jG9/QG2+8oUqlct9zP7R3t2KxqM985jM6cuSIYrHYkNH518Z6feutt3TmzJn7et4/KhsCnaEN7RFav9/XK6+8oqWlJT333HOam5sLsgUi0h6Vw3FECoVjgOPpDrU7T1g0+Rog4CCEiKNHYnEKXdaGg+HJwd1u947Vsog+Oui410g31c8ASYAekt2Rlfh9OqDBSUQK5tIWL52LXMSPBcPD9TrQ4Q9SQ0Ah8+VzAggbHR0NkiNAGkUIHOh4VSiAjlfewzFiDQBiGauoTAc2rlQqhYhyKpXS6urqQOllxoex63Q6Yd1sb28HBsHHmLEkqb3ZbKrT6QQGB6DDS82BjqQgg/T8sXs1ggJIgwgQMEckXxeLxcBOAuBgM7vdbuhXU6/XlcvlArMlaSC/570yJHPRvZpOp0N5bKohSgr7hn1EICKbzQ5U+Gs2m2FuWSOAqunpaRWLRR07diw0An2QSnHSntO9tLSkSqWidrutTCajcrms2dnZUDjh3fa3Ax2uHzal1WppeXlZq6urgVWRFAI6VBlz6SIS02QyGapbUSghmUyGXBVnej1vkf0IC7mxsaFcLhf2pwd/kPm5VAxwQelrzx+MxfYKygC62M+ev0VpePZNvV4fyE+EofIcLmmw2SkyTQDU7OysZmZmApMj7e23r33ta/r2t7+t9fX1B5r/od3dyuWyPv/5z4f9tba2pjNnzjzmq/pgWKfT0Q9/+MNQgfZx2xDoDG1oj9iq1ap2dnb05ptvBjkGXathDZAcSRqQj8Xj8YG8GI8g+osQZojO4UT8XKsuKVRXiwIgl6SQ58BLGaecUqYH9QeIx+MqFAoDOSgPYjgR0l7y9vLyslqtltbW1oKD6xXEAC6MEffs5bklBVYnk8ncdu9IUXCeXeYWi8XUbDYH8lA8t4NKUV7MAMZnYmJiAKS4jAaHzYEEfzOGOPWed4Ej45FxwJhXf/Jys/RvkRQaHuI4bW1tDUiacJyJPBMF5x6QMFLut1QqaWJiIrBGzA+OKOOO0wkQfTdj3cJidDodNZvNkOczMTGhkydPamRkRE899ZTS6XSIkvt9UIoXsA7A8b5Ej8qcTXWDeWCdwtaw/lwyGI/HQy+udrsdmDoCEABUnPX5+XmVSiVNTk4GR/hBrd1u69atW8FRgc3xXLDo/Trryr5CWgmwq1QqgZnqdDqBAfR94nus3++HuUc6C0PFnFNa3BsiAyjYZzBoPGsBPEgWmSdntgE8zhB63pevaxggmCv2TlQyDPDyHCSuCTaSMSBoQqBA2q+gODk5qenpaU1PTwcWt9/v64033tD169d148aNMG5De3TG84kKl4fREomETp48qVOnToXn7je+8Q1duHDhA9FPaAh0hja0R2z1el3tdluXLl3SxMSEXnjhhVAxy50+f0HiJCHTcIfZK47xknT2R9rXc/sL25PfneFxh6zX64VGglR64tiAqIMiNLFYLLykH6YZnSfVEgl2xxr2i2R2xghQQuSV5GSvysT9RO89mtQPCAE0AfBwLHGeHCzgWDtAHR8fD2PhxSV8HrhOL1vrawEmje8j2UGCs7W1FaLRnrDtvTuQ2pAD4LkB3C/nRhrHHyo7wdDwMsc5BNCR70SuASzE9vZ2YA8BUO8GdDy53Jm49fV17e7uhh4r8/PzA310ousRR9uZ07GxsQAKHrVx/QddF04pv4f9wgn2wARVvaiQiIS02+2GHJZ0Oq1sNqvJyUlNTk4Gx/dhrN1ua319Pcg36VPjZdjdXEbqSfpIF5eXl9VoNHTz5s3wWWcSATKsF2dSkErC0JJHg3yOIINLW/kcz0ByjNjbXpCA/cCecKkcwR72shfDgOn1vc01c71ck4N3/vhznCCY5xhxX6yNXq+nZDKpXq+nfD6vY8eOBRkh13bhwgW98cYbWl5eHvZzeR+MBqGHuQkrQOeJJ54IjP/3v//9D0y+0hDoDG1o74Pt7u7qz/7sz/TOO++oVCrpxIkTyuVy4UXs1a6IAMJaoMHn5YtT4EnqgA/0/VFdLE57NGqJw0XklZK8XAc/63Q6geVZW1sLDA7skgMqHEzKsiJJuR/DeaCMKmCMCl6eaC7tRXY9Ku79ZLwABHkvOBdEeP33AARnVwA4mOdOOUBljIlQ0yQTGQyaf8YdeZXLW3AOJYWiBJ1OJwAa5FeME06U53H5WPh6cYfN5YkUV+Be/XOei4Hj5iCd8xH9BpwTNefY/X4/OO13M4AV90bexY0bN0IuFU0t6Y/ihSrc0um0FhYWVCgUdOzYMSWTySA5fBjzvXcnYwwchLHP2u22VlZW1Gw2VSqVAoCR9iWgVOjywhjI+GCFndGhbDdFKB7E+v29XkcrKyshL4/9AHBlbfq67fV6WltbG+j7A3tKrk+j0VC73Q5j56wm94m8jWMD+qjU1263Q+8Ykv3j8biKxeJAdbJoniPrkPVCcMJ7W3kwiLkFbABuCCrF43FNT09L2peCkl+Ty+XCs4TjYS6tZI4IXDDHyEknJiZUKpUkaSD3iGcqjZXJLbp06ZKWlpb0+7//+3rttde0srLyQGtgaHe3UqmkT33qU/rkJz+peDyuCxcu6Etf+pJeeumlx31pj83Gxsb0mc98Rh/96EeVSqU+cE1ph0BnaEN7H6zf7+vixYu6efOmfvZnfzYkCQMIvNGiR/NgK6IFBYhG8kKORgtxADi3/9udak/WdYdYGpRoIDFBJuIV2KJVl7heWIU7FTm4m8FOwALwfcCWpDAOSEc8wR8wxH062IG18Lwa/wzAzcEkjATOVzQi69XvyFPa3t5WOp0eKOtLUj7MhtP6PodegIK55mewS+Th8F0HvQ50HfziTMJqMcaAK2Rw3APzCUgDyPm5XYKDhI4543zkNXFvLueJGgDWx51rRpI3NjYWkvQJEhwEdFg/yCoZm4cpOsAY+P45yLxamBuyLfocAUy8iAg5XtG52tnZ631FpUDWH4Ca/I0HuSfuq9VqaWlpKfSJcYklIIHnkD8rFhcXByrA5XK5sB6R9vCM8zXluVwuoeX/kgIIREbq0lPYZPac34uDMxhIPudV95yNcTbHGR0YKkBKLpcL+4ZcJAqFuNyT8UEm62WuGZ9qtRrmmCCDB7lSqVRgiHh+wX5zr4uLizp37pxeeeUVvfrqq/e9BoZ2b5bNZvXiiy/q5MmTisViunnzpr72ta8d6iIEiURCTz75pJ555pnwnny3gNb7aUOgM7ShvY+2tbWl3/7t39a3v/1t/eIv/qKmp6cHHAk3fkbEj2Rsd9ZxJN1p6Pf7wRF15ganFnNWBGkYevpGoxGcABKoJYUkahyTbDar2dnZ2yLkyDC4fs9BitqdgFAsFguVhDqdjur1etCc12o1Sftsgycb40QhmcEJxMnF8W82m6GaEeV8SfZHpuXRVo+mRufMJWf8QXbkzVYBtM6GbW1tqVKpDEhWiArDbCAZAiR4hBlgtrGxMQDYGEPv1YOcxx1FPgeoRgbHsZz5IfrN34Bx1iZjV6/XB4AAgLler+v8+fP69re/rVOnTunFF18MY+TXDVjlekdG9hqFbm1tBYbMG7wSGIiuIwclnnf1MHlk7wZwHMgdtLcBNewvCkWQewPb4NcMOCAXDUYNBufEiROampoK+/R+rNVq6caNG2H/IFFtt9uq1+vKZrMhT6jRaATQQKEQqqRdunRJ3W43zI8HW7yqI2tnYmJiAMTB3KTT6YHmyr1eL+TiUJa51WqFQg1jY2MqFouhOh97FjYaBpC9RxCEz3M9PAe4ZnLvAOneOJi9xLVzLOaMn7OmAT5Uk4TJRY4LG8x98szgeZDP58PYzc3N6fjx44HtoVDHV77yFf3RH/3RsCnoI7Zyuax/+9/+t3XkyBHF43vNaS9fvhwYvcNoiURCR48e1ZEjR3Tp0iVdvHjxA1GEABsCnaEN7X203d1dvfHGG7px44b+3X/33w0d4qOOk8tiHJDwc3dw+bzn7OAkuqQIx1TSbZF9XrhEvZ1pQH4Bq0CeBs5puVweyAfiepwZeFDHkj4sU1NTIbrJi53fEwV1PbxHc2lqSFTVcwlwTiSFKClz4cCR40oKQABHxB06GC5yZTx3hvkHmAJcGC/PWyAXx/N9vOmpR6w954j1goOFA+nOpN9TlJliLTrL5XlMfAZA5JWqyFeQ9sAHoJBj4IDWajUtLi5qd3dXx48fl6QQBYwCRi/cUC6XtbOzE9Yy68HnJmrcJ3PAPDwM0GG+7mQOrhwoRr/P+DEPnmcW3df8IR8Hp5xKaKVSSYVC4b7vA4nj8vJyAAeSApNIXxlYFwDIzs5OCI4gt1pfXw8O+/b2dgCo8Xh8oNcXgJo1hTwUIA0QJzCA3BRwCKPJ+mK/+DPHpbcOFr1IB/fCNUSfp56/Mzo6Ghgqz/Xxz/o6d7bS1z7yUM/H8txKl8+xp2DskL1RSppnOX2Ozp07p5dffvm+18DQ7s9SqZSeeuqp0K+LPl3RfLzDZPF4XPl8XtlsVq+//roWFxc/EGWlsSHQGdrQHoNtbGzoq1/9qo4fP66f/MmfVC6XG3CY3UF1h9SlYkT0eSniGOJko08n2s+Lk4ghDgkODnlB0t7Lkwii97EhuRvZHAnSmUxGs7Ozd0wKv5tj+G6ytng8rvn5+VBJqtVq6erVq9rY2FClUgnXiYPP/RAlTyQSB8oDcXqQ+8CwwOTQZwOpCsci9wSHBxCRz+dDlL7ZbIbcJhLyXWZF3hGFDnAAcZC8LK6k4ExSGMKj/XznIIAjKTiv/IxIOoCV75Pk7PfFH+6Lz9Ovg2prnpsRi8UCI8V6Gx8fV7Va1dWrV3XlyhW9/PLLeuutt/Tqq6/qx3/8x/UX/+JfDECUuWCsGBdyJchdYwxg2A5aR5QgJjfHgwGPyqIVEfmbcapUKlpbW9P4+HhgGsnZYc4YN6Stnjs2OjqqhYUFHTlyRFNTUyqVSg8kV+t2u7p27ZoqlYquXLkSngv8AejAMjWbzcCSbG5uanV1NTTD5P6k/dLi9Xp9oFw+LB//53N8BnarXq+H0uy1Wk2bm5vKZrNhn3MO1uru7q6q1ari8bgmJyeDhIyS4kiE6dXV6XTCM8lzeHhGUPmRXBoYN3INXW7HfDi4Yj+zjwGN5NZtbW0NsMZe2t6lezzDATjFYlFzc3MqFosql8uBVfud3/kd/eEf/qFef/31h1i1Q7tXI/+MYi0fJObicZi3tKjX6/o//o//Q2fPnlWj0XjclxZsCHSGNrTHYLu7u7p8+bJ2d3f1F//iXwx6cWk/V4N/+x//uTTI/HhCOi9MXpoOehyMwDC4rAQnBEfTI44cKx6PB2e9Xq9rd3dXxWLxQHbK84P4+36dTZgdIrlEfVutVjiH57h4wnI0/8hzc5ydcKmgy9w8T8AdEgegHnEeHx8PjBOfcYCKg4Wz5I6RMynODHBfzJWzM278zhk+QCrmQM/PyRiyVjxC6VIs7gd5ICDaI9yejyNpgOmpVCq6fv16YBGPHj2qVqsV7gUn0GWa0n7zRy/JTrT/3RgdroPre5R2p7UNK9vpdEKfpChTisPve8UDGhw7l8tpcnJSpVJJuVzuvq8Rx75SqYSyzwRMYItYz7FYLOSNUGYd+RgSLO6XNcT6AqA5O8kaYt/4swwwz/7j3zj8jI0HcySFnDUAEcEOpKyAH+7Lc258PzgDCNDE2E8ObJyl8p9H14MzTA6AomWmfc1zj4D5VCoVmrUSZKjX63rrrbf0p3/6p4daOvV+mMuReZ61Wq1DXVZa2pOPU4l0e3tbb731ls6ePfuBYriGQGdoQ3sMtrOzowsXLqhWq+nKlStaWFjQ0aNHtbOzExKBqVyC84ATSt4Gjqa/KLPZbHDInRFqNBoDuTscB5CCPAUnnupW6NL5LqBA2gcUV69eDdeSzWZD13nPycGRQ+efzWZVLpdvcwiduYr+jogt1aY6nY5WVlbU7XZD6WGXKBENB6hx7VGGCYBJrwpke1Ryq1arwdH36BWOFPIdcmH4LCwThoPD/DOfjC0gkRwkHD2/J8BoNpsdcCgPkp+5kypJ+XxevV5Pq6urYc2QME/kGnCCk8t147wxr+l0WhMTE1paWlK32x2Q8+GgkZPglfmKxWJgDSuVijqdjj7+8Y9rdXVVCwsLIWctKsV0wIJj7evrTuCF/KaH6SfzsMaYLi0tqVar6datW6rVaoGpmZqaUr/f1/r6unq9vfLBOzs7oehHv99XoVDQ/Px8mJf5+XnNz88/0H11Oh29+eabqtfrunHjhkZGRnT8+PGQf8czgn4/0t740o8IYJbNZlUoFMI1kJcjKfSsYh3CirikFHYyGsxhvRM0kPZK9He73XA+cvRYDysrK6HUNgxkOp1WsVhUr9cLfXYoCOLNfClpzfonB2l5eTn8nnEjcEDuGIUEnE0laOIyUGSxBBHYd8gy19bWwvwgMYbJyefzmp+fV6FQGCgb/u1vf1u///u/r+9973tqtVofKMfyw2hTU1P6uZ/7OT3//PMaHR3VtWvX9Hu/93v64Q9/+IFKvH8/bWxsTP/hf/gf6vnnn9fc3NyAJP6DZEOgM7ShPQZDAjY2Nqbl5WVNTEzoyJEj4QVIRNMj7XwPp4+IpEdU3UHk/0Q4PW+A6CzMgrMwnhPgx/QXt7SfgI8DAFVNTk7UUSU6vbm5OZB87k7qQRFRN5xmZGJEbN3ZiEq/Dkq6P0haFM1ZiYKtgxgW5oLzRpk0vsd5AD/RPBjAk1831+7zLymwKc7SeR6Isyue9+KFAQC3XnyB/AqXv2Gcw4sE4IRzfJ9nzo0jyme8MIOD7Uqlorm5ubAu7pTX4s6yr6k75dy4vO9xGXPZarVUr9eD3AVAHC3EwNp0KSs5Ijj/RPXvxwDWnU5H6+vrajQaarVaoQ8L6/Yg+SPf9XLoRLa9+h5z7WDVi1z4vHF/zrb5WpL217qzjy7RdbkcY+oMSyqVGhjvaG4i98l+8vXM89HXD9fNOaL5jFGAHpWwOavszwD/OcwBe5PCG95fiAISZ86c0erq6sAYDu3RWDKZ1DPPPKOTJ08qkdirPnru3DndvHnzcV/aY7N4PK4nnnhCp0+fDr3vPog2BDpDG9pjtEajof/7//6/deTIEf2n/+l/GjrNe8U0ab8gAI4jL3qiqbAtfL9Wq4USpVT56fX2ygKTJ8DxXS7T6/WUy+U0NTU1IN9wNokXOZW2eDnTNHJlZUUTExOBeSGnAu070Vq+5yDrXiRtiURChUJBuVxOuVxO9Xo9RJuRsjEuREwPcpA98Z2EY0m3Odrco/cYIgGY6+EzRIu9h0+/3w95D8ePHw85Mjs7O1pdXQ1jyv1zLUTD3QFyORhlv+lWjwEGa7XaQJlbpC0kfJ8+fTqAlXq9rmvXrg00OfR/4wzzN2OFM8ZcM57k0zjjMDo6qkKhEFhHwPz58+f1+7//+5qYmAglWw8COfl8PrBdJHbjvKbT6VCYIvq9x8nmSFK1WlWtVtP169dVqVQGqnb5HnYGMBbbK2ucTqc1NTWlI0eOhGZ8JODfr21ubuqNN94IrBJOPOBa2u/LBOM4Ojoamsbyf1/r3W43zD3XD3vsFQMdRHn+F+ua5xBV1ni+IYmJFpygQiL7fXx8fICBjMf3KriVSiU1Go3AmNBAEzaGZyn37oCzUChoa2tLa2trSiaTKpfL6vV6un79epi/aDCEfQMgZRx5xrok1vcz/67X60qn05qZmVE6ndb09LSy2WwoQd5ut3XlyhW9/fbb+uY3v6nXX3/9A+tcftisUCjoi1/8omZnZzUyMqJbt27pD/7gD8J6P4yWSCT00Y9+VJ/5zGe0s7MTcjY/aDYEOkMb2mM0JBL9fl9Xr17V5uamjh8/HsrlShqovoPxb4ACEUyPEHpE2A0nRNoHAn48l2d51BOwgLPi/VrQwcfj8aBZpmQryeLcC4441+LOjnRvORQ46DAD3pPIxwcnA8Dn1+9Mx0FJ6s5kReVT0eIQjDmg0ucpFouFEte5XE4TExOhcaLPz0H5Q75ODsp9cbAWjYZzPbA+sHcATSRdOGdeMY7vIzOKSuP8XMwp8kXGFGfdHdtEIhEcuVarpVqtpk6no6WlJTWbzQOZHCzKJuIU+7zcC1B+EOP+7+fYBAZarZaazWaQbnrel0sLGV/OlUwmlU6nQwnpVCo1IOe6V2NfUC6asupIqAA2AHMvtMEa8iaePiaeQ8S5+NubGPu1+HqQBvN6fF8CrKIS2CizKO3vYQoE8Hzh384+Osvizx2Oxe8BTowDe4Vr873lc8k1+n1E79OZL2d02aPIBtPpdAgs8Gy5deuWzp07p8XFxQDchvboDNBcLBZVKpWUzWYl7SkX1tfXBxi9w2axWCys03feeUdLS0tDoDO0oQ3tYFtfX9f/+X/+n3riiSf09/7e31OpVNILL7wQqn/VarUQgR0fHw9RVu9pAsjY2NgI/S5oqshLm5LIJAJ708HR0VElk8lQHYiXNhp2JBU4v+vr66GMrJd+xalbWVlRMpnUiRMnBqLXJAzDCrXb7TtG5O9msVgsRIyffvpp1Wq1kBBJ7kyn0wmyDxwTWA7+T84IjjSOCayF5ylICg4cQAFn3vsd0byRv1988UXNzMyEPkVLS0sh78ABxerqaohkR/ONRkdHlc/n1Wq1wsvVARrHIdLslcn6/b2+QbFYLDRd9Upq9BPiWIwh45hMJsPPWGcuoYRR9PvBWWQNA4xPnz6tF154QS+//LL+6I/+SNvb27p06ZLW19dDbpgngkfBJ2AR59yr5bGG30vr9faq48VisdDH5m6GM3vt2rUA4AAa5ItsbW2FIgKrq6vh3mKxvUpcyWRSx48f1+zsrF588cWBnlb3Y+TGvfzyy2o0GuH5QH+ZUqmkWCymarUa5tr3biqVCswNYMefF/H4Xh8RnyuYEXLeHLyxZth/yWRSuVxO7XY79MYBsDgQALiQm5PP5wcqGXK8Wq0WgHc8Hler1dLOzs4AW0N+Duwkz5xoUYtkMhn67iBTI59ud3dXt27dGpBGAqaQ93mlSp6djAksbqlUCtXukBVmMhlNT0+rUCjo6NGjQR64uLios2fP6mtf+5q+9KUvDYsPvE+Wy+X0l//yX9Zzzz1330GGw2KdTke/9mu/pjNnzmh9ff1xX85tNgQ6QxvaB8B2d3dVqVSUyWR05coV7e7uam5uLgATZ2ucYYhGDYm+e1QR54PzuD7dnWnACr0jcN4lDUQ+/efRnJp+vz9QKUlScEB52bsj6lHyg6L5XGs0X8QNlmB3d1e5XC4AHI+awzhFo/+eP+CRYz+nR2K576gm3uVuRJvJpcjlcqERIrlRGxsbobQs4+fz5vfmuQWc66Dze05BtEkoDIh/FmfXx4bPc7/R/CDGAGbiTuMRnUOuf3d3V8lkUtPT05qbm9PRo0fDPAHG72Y+vkilvBrevdjdWKO7nfdejca79Xpd1Wp1IEfEAYGzYx7Vp1cOjTozmcx9X6/LD2m222g0bptPaZ994Q+BEK4P0OEBEs/ji869S2wB4L4+eQY52+IM7N3GnLXibDWsdjQnz591UTbOn4HSYP8rf+Z5XqIztdFrBVyxj5x15ffRZzf72mWMkkKABCAm7bMHb7/9tq5evfqBdCY/rDY2NqYnn3wyBOworX7Y56BcLmtycjIUIbl586auXbv2gWS4hkBnaEP7ANnNmzf1v/6v/6s+/elP67nnntPo6GjI2ZH2G/zhMABKkDfQA4YII47BQRXEiLjj5CMz293dDQUGOHYulwuOOecfHx8PSbOwQyMjI1pfXx+QWVWr1SB7I7cGgy3iXryRoKTQsyeZTA4k7zooGRkZUT6fVzKZDLKwtbU1VSoVdbvd4Di4JGdnZyd8B70/4wWb5eej1wk9FBijRCIRJHtEZovFonK5nFKplI4eParp6WllMhn1+30tLi5qdXVVKysrgSWAwUEG4Pr/kZGRwNAhoWi324EdAADDFLTb7dCno1gsBhDQ6/VCXyCA6A9+8AONjY0pn89ra2sr9EQi34EEdcpHk7C9vb2t5eXlwFyNjo6qUqkMSNg4J05/u90Oc0xT1ueeey5U+9rY2NDp06dDhSvp7pXUEomEyuWyJIXqcvcCBnAuo+vsbhaPx5VOp+96TW63bt3S1atXdfPmzdAvB9lTr9cLFfOYGwAOFb6OHTumiYmJAHbu1/r9vQpu9OZhP2xvbyubzSqRSIR1BviFdatWq5qYmAj9fbh+71OVSqUGItteeCORSCibzWpzc1OLi4uKxWKhVxSVwWBY2COA1omJiRAIiDLVGHsUZsdZYtY4ARXWuhcvkBTyAwnEcC88YyncAttNsYgbN24MrHGeKbCQ+Xw+HHNzc1MbGxvhszzDvYEurHgikQgOY7lcVrFY1BNPPBEkQfV6XdevX9c3v/lN/S//y/9yG+Aa2qO1XC6n/+A/+A90/PhxjY+P69KlS/q1X/s1vfXWW4e2CEQikdBf/at/VZ/4xCd09OhR7e7u6uLFizp//vwHsq/QEOgMbWgfINvd3VW9XtfS0pJee+01FYtFTU1NhUiqdHtfGhxdj/zjXEUZk2hE2X/n33ddu0dlvYCBa92RGuHckpCOVMxlX1tbWwO5LZ774xp8Pz7/vpNDi8NDBJTIdLvdDhIXvud5TS5XcUnbQdWjMBw75oOGf4yjR4K5f3qOEFn30roeNec4UZbrIMccZuOgfCcvX0vxA+RnfLbT6QSGxZkZWCbWCuuA6DP/Zp0gHeN6XB7pOQsA60QiEXIpSDZnXFut1m3J9i7/ic4343A/9iB5PHf7vJfmlvYKjFSr1TDnjJlLIsnDoLAGawBnF0YQZ/teDSDRbrcD8EVmKGmgshgyT2ctfK7cmFvGnc96joo0mI/C/CDh8kqBHNPXFTJH1pU/UzznJjqePj/RPC2+68VW3DiOr7HoOSis4fcRzTUC8HA+5sGfLxwf8MZzz5kpmBwCSPF4XLVaTa+88oouXLiger1+GyM3tEdjSFXz+XyQIEt7CoWLFy9qcXHx0M5FLBbT/Py8nnzyyTAuMLYfRBsCnaEN7QNo586d0y//8i/rU5/6lP7O3/k7isViKpVKof8EL0nvp4PzLO1ViEkmkyFCyu+IbHoiL5H2jY2NoLd3x4ZIqeeAEFnt9/shaonD12w2Q9QY55X/b29vq1arheT8qMFI8bKHCbkXZ48oazab1eTkpBqNhorFYijri8FEoeMH3FQqlSApi8fjoWqdjxPjToU7ouUkibtjD7tVr9eDo3blyhU1m83wGUo6u1QoCqpGRkZULpcDa0d+TTKZHCgLjBNFNTq6d09OTobx8872OE0uuQOUuSOJA9poNAaAKTkesFHxeDy89BqNRgC1JFiTPxOPx0NfE8Z/c3Mz5IUsLCyoVCqFMQBEAFgxb3x7r+aJ6u+VtVotLS4uhrm/fPmyrly5EvJaGCtAHjlSqVQqVL9jbzjwke6/uWmn0wlsCnk3HvhoNBoDCf9bW1uhopjLXXHKuW7YCKRqBBH4vhdMgXGYnJwMzk8ikVCxWFSn0wnAKx6Ph+p5FF1AWueAGPaYsWOtEhTyogjdbjf00EGSl0wmVSgUAhPqAQbup9/vh/uDcYOBmpqaCuwL9+4NlWOxWHjGUFlwfX09SHYBhdJ+cIS/vb/QyMiIFhYWAtPMZ86cOaP/6r/6r0KVy6G9PzY6Oqonn3xSTz311MCzplKp6I//+I/DXjqMFo/H9bGPfUz/1r/1b2lkZESVSuVxX9JdbQh0hja0D6Dt7u6VM15aWtIrr7yi2dlZPfnkk4EtwTmM9qcgGgmTgKMjDfZUcYYD8+iqRyExl3/wWc8vcS2/szSwBQA0HGbPPbiTfv1+ci84DtdKiet+vx8cIKKx0byY3d3dIKHyimbu1HjU2CuPefUmLznreTVe0ck7qHtU2J0vnxOPAuNMwpzgYDL3RII9twdnKsqI8W/PQeLYfM8Tuz3KDdDlc97w1MeUuQdgjI2NDTB30n4+Sb1e1+rqaihDnkqlBliAe8nheT+MNbG9va1ut6tqtapbt25J2puvZrMZ1joggXWBxAnJZCqVCoAUp/h+1zznRWbYbre1sbERQITnq/Cs8PGP5ueQQ+YSK0lBZgY48Kpkvn6cVZQU+oUxDg6sYRD9eqJsLsCUZ5qzLl7kAGDDPmFtel6QszZ8hrWNjC86/lHpG/caZYs854o94AU8/Dq5bgAdhR+Q80l7TVIpJd1sNj+Q1aw+zAbQefLJJ0PeWafTCcVEDvt88Ey7du2abt68+YGWVA6BztCG9gG28+fP65/8k3+iv/yX/7J+/Md/PFRMwpF2GcTOzk5w5tHF8zBGAgOQ8ZcyDrqDJ+RH7hhwLpe3uOQMR4Hj86Lf3NwMTheRz52dHRUKheBA4NDglAPQPLJ5v0bODo4790eOCk4c/0dS5kzW7u5u6JNA/oj3xiBngfGEbZEUnE1yEmA8UqlUSFLHAZM04BxKg6WjcYhwrjyvCGcOx5kxA7hxrVGHjzmlAAWOcDqdDiWIkb7xUuN+cNra7bY2Nzc1PT09AJ69ahsyLViMra2tUBYXBxMWYm1tTZubm2o0Gjp58mTIH/I8rcdtOzs7oTfO1atXValUtLi4GMYSyRpOK2uD3584cSKUjIYJdHD7IMZ4r6+vq1arqdVqqdvtDjjI5M/0er3QZJK1QJlvnDnYCNYXazCTyYQ1CuNCtTx+RqWyVCoVqs0hAQM0cTyYZGeYvZBGKpVSIpEI7FG321UsFtPU1FS4b3LoyB/k56wj9gv5jAQEyKPB+v1+yBN0xtqv10Gd7xfmOOoIe+CE/EdnYIvFopLJpKamplQqlTQ/Px9yLc+fP69/+k//qS5fvnxoc0Eep6VSKf2Vv/JXdPr0aaXTaW1ubury5ctaXFw8tExO1HZ2dvSHf/iHoXHtB9WGQGdoQ/sAG1HKmzdv6lvf+pZmZmZ05MgRpVKpIEcjgkq0FCfe2ZioXAMpi4MYAIu03xMCMMXvnUFA7++sgDRYBcmj+nwW+VSz2QzyK8ybdkqD+v6DotzRCGnUESaZuFAoDEhukMh5JNkdLYAF9+ZVlHBKveeFsxjulERzj5grjxJzjQBRrtsZIpw/jsEcRyOLsVhsoHt9LLZXrtij1A6uer1ekFh5U0gcOwArLBF5E8wTY+jlrqX9BG9PQJcUADjOMI4+c0zktNvtan19PTAUvV4vOJT3CnacKWN+opF17vVOBlgj+R3nvFarqVaraXV1Va1WS51OZ6BhJQUX+M7IyF7j3ImJCeXz+SDNjCbbP6gBAqrVqiqVSpgzQJTnj/g+4hq9JxZMA9fl7BwMiKSB/c1zArAgaQAQSfuyuujccH56YAGgkPzBeHixFWcpefbBALNPPBgBWEYKB/PDfCJ7A9TBfEkK1xLN/XHGFDksMj0CBMyNB424R6S2qVRKhUIh5Ku1222dOXNGb731li5fvqyVlZWHXh9Du39LJBKampoKzbObzaZeffVVnTt37lADzyNHjmhqakq5XE69Xk9Xr17V22+/HfbvB9GGQGdoQ/sRsDNnzuidd97RX//rf13/yX/ynwSnjQaAAAEiny4DkTQQsR8dHQ0vVc+dgOHgpUy+CtF4nG6+B+vikc5YLBYi71EGwaVuRELJPZH2nB8cBpwPnKZ0On2gg4sThoNxUA8VSvTS9G11dVW1Wk31en2gFwW6fq9EhzPsUi4isxsbG6E6VrfbDRXd3CkCUERBIeAF55iXxvLy8oAkxqs1cZ/024jmSEl7DhgMFBIwnDeq3eGY4gBWq9XgaCaTyeDkZjKZcD6ug/shsk/DPCq2Ud6ac6+trQ1IpbyLeC6XCz2OkAYCpEikd0lgPp8fyNO4m/l6g2105oB1Cfg4yMgvazabeu2119Tr9VQsFoOstF6v68aNG6H3DNc1Pj4eqrRRBCKdTmtubk7ZbFYzMzNBnvReWbvdVrVa1bVr17SysqLp6emQDwW7QX4d6xMG0cGupJDjwvOA9QJDyPqFqYURjT5reC6wxiuVykDpWQ8ywOql02lNTEwEUITkLZ/PKxaLhfXG2gCUwJa5bBPA5vk2Hugol8tKp9MByFSr1ZCLNjo6qmKxGACU7132AGMiScViUalUKvwsk8loZGRE1Wo19MbxAjGs+5mZGWUyGc3NzQX2b2VlRb/1W7+lixcv6pVXXvnAJnh/2G1kZEQnTpzQiRMnJO31uvvSl76kK1euHFrZWiwW06c//Wl94hOf0Pz8vHZ2dvTSSy/pm9/85gc6f2wIdIY2tB8Bo/zr2bNn9bu/+7s6efKkjh8/HgoMUPJZGmwi6c4HUUScARgEr/oDk4FDBDiKx+MDTmmv1wvOEzIMz7Ph39Kghl/aZ6loXsi1eWQbZwoneXNzM0RKo/p5N2cWovkuY2NjoSQszlEymQw9Yer1ura3tweqiPE9Tw73HACX/lE6dnt7e0B+E41ec52AAtf1+zl8vujBcxBbBygCTAEWcEBd/ufMH04gn0O2xx/YICLXLrXzOXY5o+ciefQahsdZFb8uz1cCQHtFuZ2dHa2tranb7apUKg0UiGD+nBEADHJ9SKhYZ8w/AM7ZC6L1OPqdTkeVSuW2ayQPx0Gpd7NnjCYnJ5XJZFQqlQIAfFDz6l/Sfh5Zo9EI0kv2lrMtyA3ZHyTcw7x6I1iAjn+fCDZg0UG3S099LcAQekll9g3MCnJOzyGKPp82NjaChC2Xy2lsbCw869g/rBkCOIA4b0IMyGWtswa8VLavO89TBOj4MxUQDevn+48/BIdgsggklEolZbNZzc/PhzXT6/V09uxZXbx4UefOndPS0tKhZg4el8Xjcc3Pz+vEiRMDeYHb29u6deuW1tfXD7V07eTJk/rUpz6lYrEo6faeVB9EGwKdoQ3tR8BwDr773e/qu9/9rv723/7beu6550Kkf3FxMejXefkDXNxyudyARAj5laTAiLjT4iwKpU+J/iIjcWaB77lDjWPmLweqsyWTSXU6nQCiMAco/X5/4N78mvxc9Oo4KF9GUpDJ0cMjk8mE3jI7Ozu6evWq2u12cHSQ3kRLdSOtcQcQJ4poMdcZLTiAeS8inEOi0NwnjA9JyjB2jAn3j7PmCdKSAntCsjuAChCDw95oNMJ80iPF8xQAozimDvr4jINszMcrn88HxxDAShSedeYJ9Lu7uyEPiXVz7dq1wL7w9/b2tlZXV9Vut0MDv/Hx8QBOkBBRbpkxKhQKA7kVgC9v6MrY7ezshGID5Mex3hy0AGZgD1kTk5OTymazKhQKD9VZnfmi+hagmjyZSqWiTqcT8s28DDIRaIBMqVQK8+OBhEajofHx8cAweql1mBQqoLGGYYUdHEsa6DtFyWsHSIxfMpkMYIG+WjzDXKI5Pj6ucrmsnZ2dkLdChTp6WhUKhfDMAWADbGBfPS8IUOdVE71Yguchsk55prikL5lMhnXL3nFpZqfTUavV0tTUlMbGxjQ/P6/p6WmdPHlSExMT2tzc1Nramr7zne/orbfe0ve///0Btnlo75+NjIzoueee09NPPz2gENjc3NSVK1e0trb2GK/u8VosFtOLL76on/mZn5G09zz8UbAh0Bna0H4E7bXXXtNv/uZv6sUXX9SpU6eUTqcVi8WC5AMZlbSft4FefXt7O5RbxpH0qKSkAQfWE9GjRQ5wCIigxuP7PWEwPuvaeZzJZrOp8fFxlUqlAScQRsBLV0v7EXci1OPj4wNVxJxFulPUzfvqwJRsbW0FaRSR6GjuDrkkgBuX9MECOEvhAIc/noyMQ+Vj5CW34/F4mDecShwuAALgpdPpBMkQDppXOCOSz5w6M4NzSSEDZIM4mp7bFWXJiFr7/6M5MP4ZB2bIhFwKyXk5N9XYtre3lc/nNTExEcozs17r9bq2trZUr9dD/pk31IV9ZM3BxBC95/zSPrPEfVMKnLFiTbjMi1yYQqGg6elpFYvFgUIb6XQ6gIN3MwcvgK3d3d3QxBNQUq/XVavVwnXX6/WBNeL7CLABaHVwAqOFkw+gd0ko1+W5VYA41iKgiTlnXTnT68ET/7xXJ0N6y7WQ69TtdoO0DXDkRTG80hvzzr0D/Fif7BFnA32vRq+X/cqa4jnm1S5p6sse4xnpMuF4fK90NaW0YSa3trb05ptv6vr16/rWt76l69evD+Vqj9HGxsb0Yz/2Y3rhhRcCSF9ZWdHS0tKhZnLc+v2+Ll26pMXFRTWbzcd9Oe9qQ6AztKH9CNpLL72kl156SX/37/5dnTx5MkSRqXxSq9VCKWokKS65unXrlkZHRzU1NRVe+IAdnBN+jjODLEYabOIIEAKoUNLWQQ3HwJEkMo3TQMEADEeIY3rpW3IK3MnCOF4UbLnhqJEnQYUxouCrq6sDunwcM+RhsEte1Q2Hh2g0CeruQDlAc6ADOESaIynIAmk4CYuGNMbzEZrN5m2JoD42OM5cB4npOO/uiCEDcoef+TgI6HgBB583xgMgyO9xRpPJpBqNhprN5kCxAM+bwbFcWlpSp9NRuVwOczYystcThkp6jBU5PJIGrh9mgpwbAIXPn6QAnPk8hRBwjn1cvC9QPB5XuVwOkfrJycl72ca3GWBkY2NDtVotgLlSqRRYlo2NDS0tLeny5cshp43r9bwX5iIWi4WeVjBT7C2kVOl0OtwT9+9J/7CYW1tbSqVSA0AacASo9HNyT8hbnS2W9gGUV2fk2ACasbExra2taWdnJ7B89B1yoANDyLrhPLCWDoSQsEXH3vcP18p88ywksMHaJ7+oXq8HNoc95wEESr+Pj49rcnJSk5OTYR2//PLLOnfunL785S8HwDS0x2Pj4+P64he/qE9/+tOS9opoXL16VTdv3hxKCf+19ft9vfXWW3rzzTdDoZYPsg2BztCG9iNs3/ve91Sr1fQX/sJf0FNPPaVsNhuS+SkPLOk2B0ZSiH57JTWcVBxrj+p6FJfve/TYjxv9GyeDXCN3iJDMUHr5oH4pLonD8Yjm53A/ziRICveAExI1xuDIkSPa3NxUNpsNTtTOzl4jUZwxad+B9D4hOIGes+LV8HBE0erjjAPkGA+cLe4TuVKtVhvImQAsco29Xi847X7fAAiOTaTdHVW/L4+GM2f0CfE1wDlxYAEIXlWLdQSDA8hywBjNI3FAAXOTTCZD1Hx7ezskeiOFcoYMMMm1R5k0JImsJ2RUDhC8kAZrGkBRKpUCG0BD22QyGZq0Iok7yLhG7s+vnzFzIEofoWazqU6no+vXr4cqdLVaTZVKJQBjT8Z3AMC/y+VyGAckYzBavi9hfykEQEEB5FnIIGF4AZiAQsCtV6A7SLoJkPCCHC7ddNbXe+F0u93wb87t65D5I0eKAAwSJNYc1xUFO+wLZ8G5PmeimC9+Lum2inSeg1coFJTL5TQ5Oal0Oh1yoBYXF7W0tKQ//uM/1qVLlz7QvUgOqzUaDf3u7/6uzp8/H55vh9GOHTumubk5lctl9Xo9vfzyy/rWt74VJMMfZBsCnaEN7UfYXn31Vb322ms6duyYPvKRj4QkY68ChkOD40NUCmfQI/DRaC4ONU6ypIFSzx7hd6DjeQ84zchjcOT4LAwIjMpBQMdBE9FUdzokDYAPN6L+XHvUcMyIetMzAQkgeTFeaQdWBQeRiC/mZaHJlZD2Ha1CoTAAIBgPd/Y8j8bBB3PKd2FryEdh3HE8AVw4+rBOuVwuyMc4FkAHB817qeAgw+7xOXcsYYM4LywL1a3IpeB4gFAvlc4cecU5Eu4BCAAdWEUfQyRDzsBIGmDDcFqJvDOefM8ZRMYsnU5rcnIyfJdclmKxqLm5udty2qLmDnm/3w9yQ2fbuF4qjcXj8dAAtNFohDGq1+uqVqthjUX/MCYEBADU7AXAGt8HVODgt9vt0OcHkJ9Op5XL5YI8jmvlPmq1mnq9XsjH4nnieWrsHf4wrl5hkevmmlwOC5PLOBA0kfZZxXg8HgogsL8JLMDKAHScxSEQwvqDcQYkeW6aByWigQWep84KZ7NZTU5O6oknnlA+nw+sz82bN3Xx4kV95zvf0c2bN++4dob2/ln0vdJsNvXlL39ZV69ePdRAZ2FhQc8//3wAOmfOnNG3vvWtx31Z92RDoDO0of2IW7/f1x/90R/p/Pnz+rEf+zEdPXo05DKgeccoXgDAIR8l2nCPhOt8Ph+cDhwTXugcg5wXd0ak/epvXsHMK0Z57gmO+MbGRkjcPqhUNC8hz4PZ2dkJTh/R/ijg8Wjy3QynhHLbntsBoImWkeb4AEKAGCAgkdhr1Ei0mLH06D0gitwbz2kBhLnEkGN4xJlouztv/m+Xk/GH+XHpGIDAy37jWNI0Mtr8sNVqBQec+wRgSQr5JowX5/Nkd84N+GG8JQ1Ux2ItISujshZyJ6/wxrqGyWKOATsu7+v390tyZzKZsM58Do4dOxYcZ74PI+jOUXTt9Xq9sBed3QP8MX/OOMHK1ut1tdttVSqVwE70+/2wJtDIsx4ZB4AnAMzBCX8IeuC4s+65JkAFoN8lZzAtAKtkMhk+y9pmnpwRnJ6eDr/3CoCUaYdN4v6YF67H9wfnZO8B1gDAjKdLDL1CGvcuKcwNUs5qtap+vx/2blSyybORcXHJL/K6mZkZFYtFzczMKJ/Ph2IvN27c0MrKin79139dFy9e/JGQ/3zYLZFI6OMf/3gAoxhsMs+ww2qf+MQn9HM/93N64oknHvel3LcNgc7QhvYhsFdffVWvv/66ZmZmlE6nNTs7GyRszhLglOAsErnG6QIUAWDS6bTa7Xbok4HkxasLwSJ4Yi8ONo40n43mEODQelWwra2t2xqJunFMad/hqtVqAzKrg5idezF3iBmfTqejjY2NINkiD6Ddbgcnidwbad/hxPnFCWTsSbwmUs7fLuVzZmBsbEyFQiHIdJyRc8nSxMTEgKwsauSm8HlyDaISRJxEQBnnA9wCcnBWYYU2NzdDTyfWFWPC/bmjHAVXGOvL2Y5oQQOcWsAX9+OJ9VwbjrNH+T2vzMeMefJGlcxjOp3W1NRUkHDdq7Ef6EtFTxfABZXcKKHsbNPm5maQqt26dUvZbFalUinkgwFKnKVir/k+iZqzbtJgcQWKNfj1e45KlA1jrMirAthwTCRxyNTy+XwAcKwjvk/gg7wW5pj5chDjRS0ALgA75phr87L5rK2o08oa9Twbl6D6mHBMZH7+TIU5HRkZUbFY1OzsbJA08pxbWVnRO++8o69//eu6dOnSPa+loT06SyQSOn36tJ577rkArNkjnp96WO2pp57ST/zET4Tn/o8S6BsCnaEN7UNivV5P3/jGN/T666/rJ3/yJzU/Px8cMwcoUaecn+N0TExMhIg2n/Ummq1WK8hfcHiI/EoKPSH4v0dXcQBwRnB+dnd31Wg01O12NTo6GpidXC4XJEIOlHB6Y7G9ogBzc3MD7MLq6mqIquJE8R2XK3kuxEGWSCQCVT81NTWQYwRj0O121Ww2QwRY2mezPGoPoIhKWziPj5EnWbuscGRkJDQ+5R5w5nGwALcHVbdinAEHXs6ZucYhc7Dq1eXIk6CaFYUlfL6JrMOucAxyEygXPDo6Gpp5siY5F4nnrVZLW1tbwfnAqaW6GiWP8/n8AIDjc/y/0+kEtobcGpzZYrEYksQBuvF4PKwfxiubzd5T9TRpX8oU/YPUi7wmwPnq6qoSiURItK/VaqEoAc60V0wsFAqqVCpqNpvhuprNpra2tgKzyXxQEpeGnAAhwAEMIcAgn8+r1+tpbW1NY2NjKhaLA88AGFz2P8EUAh+AEH7vzxkcRpgpJKs+b5R9B+AA8mHvmNtMJjOwP/j59vZ2YOu4NvJ+2G/sn6h8FycOSR0MNeu70+mE73iuIUGH8fHx8Ox64oknNDMzE4IcNHj+9V//dZ05c0bLy8v3tJaG9uhtZGREf+Ev/AV99rOfVbFY1MbGhr7//e/r3Llzw9wpsz//8z/XO++8o6Wlpcd9KfdsQ6AztKF9SKzf7+vixYu6evWqTp48qZGREU1NTQVpGc4TL36XszjL4lWN3DFwaYZXNnKHTtrXyruD4/p2z+vBsSeviBcKJWVxOHBwcDZcKkS+yfb2tprNpjY3N1WtVoPDSnU0z+WAvYIRuJP0KB6PD0Tvd3d3A9OENKjVag0kX3MOZyaIbjMXLtXyMYlGjv1zHB9ZInIiLzBB9BEHjuN5ngQSjGguFtF5v0bWxkEVqLa3twPoxRkkV8cZQJfDkf9EoraDMO6f7+E0Ig1yFg92Sdpjqpw1dKCIAwqQGx8fD0A+k8kEEEVfpdnZ2SDfcpaFAg73Y4wpa9bXA7kn7CskaOyvzc1N3bp1a2Cd8rlutxskbAAO1ioyS5goDCma5/CwBjifF2mYmJhQp9MJfaXYPw7GWQ+sQZeVAThdrglrxnjw8263G5g0rskrDPLsoiACY8n9ANB8jHmOcE7WA2sCoOO5gl6swVkgZ7mZV/ayf581SKnxqamp8If7X15e1tLSkv7sz/5Mr7766n2tp6E9WkskEjp16pQ++tGPStp7rpw/f15vv/32gPz7sBnvJ/bQtWvX9Morr4SAy4+CDYHO0Ib2IbOdnR1961vf0muvvaZqtaqFhQX9m//mv6mxsTFdunQpvIg9OZwXNS93zxHg/wARHGmccCRc3oMDqQ5OE6Ag2ngSxwhnBACFY4ZDW6/XNTExETqj00AT4/xUoaJSGpFhIujkU3jy/Nramnq9Xmjy6N3Uo+bJyTAWyWRSxWJR8/Pz6nQ6Aw0dyTvw/BOkNjjlXj2KaDJglD5HtVotAAwcYsbP+wB5OW56wDj44Jg7OztaXFwMeU1eXtyZJhxH1gC5PUT1iWZns1nF43s9YzyZnznCuURq5NI9z6vhnDib3Mf29nboTUM+ECxLMplUKpVSuVwOLKRXSPPKYdIeUDp69OhACWhybvjbHeJocvK9Wr1eDzlLsVgs9LphLp1FIc9sd3dXlUplALCydvP5fNiTSDxzuVxgbBkXZKTMPfsbNgn5orSfnE+CPP2JYFaz2ax2dna0uroaSj1TNhpGjmNKCmPnoNTn3+Vjnn8F0BgZGQlFQWCmAESMA5Xotrf3msXyDPG5hm2pVqsDzxnOBUjjmh1Iw3DCOLGPvDqhSy83NjYCIOY66JVDsIW+R7/yK7+il156SZcvX77v9TS0R2flclmTk5MDz6F2u63f+73f09mzZ0NBmsNoTz/9tD7ykY/o5MmT6vf7+v73v69/9a/+VShV/6NgQ6AztKF9yKzf7+vGjRtaXV3VqVOngrOGYwj42NraCgnDOAmpVGrgJe4lU13uxXmI8Ev7kR+XSUmDvW2IoroDxJ9oVBUnF8fIgcpB+mAcGo9M46C4DAtn1pOTKcWNg3QnoAOgwjyaLOm2vj4wK4BCpGzIcryCFNIZxorIOBFt/jA3NCzleA5imBevjMZ9udOLnMyj214FjHnwBHfG2nOQPKdF0m09VXAiGVfmxfs0+ZpyYMJ68PHo9XoDTVPT6bQymUxgBimaAEPFmgNoF4tFTU1NDVQoe1hzdpTrgG2KxWIh18ur6HkhiI2NjYF5IGgA6KX6nDva9Gnh2A6oPT8OBoNrY92xJphfZKTO4rXb7cCqcU3RUu0cM8rYch/83EGXr0dJ4f8jIyMBqLFm/Lqdset2u0ok9vpP+bHIsYGpihbeOOh6/TklKQQ8ooUc/PnjbLgzis64EahYWlrSyy+/rO9+97vvyXob2ntjBGrK5fLAs3x7e1uXLl3SO++88xiv7vHb1NSUTp8+Hfrc3bp1SxcvXny8F3WfNgQ6Qxvah9S2trb03e9+V2+88YYqlYpmZ2f18Y9/PDiFGxsbqlarIc8EB4ycCV7ggB1e4uTvuCTLnRmMaDLON6AKp0raL3GNE+9J0t5rBfna/Pz8bWAiaslkUseOHQtR4E6nE7TxvNRSqVQATkTFyS2AbSDH4H4s2geoUCgMOEPIkHCayHep1WoDRQk8sRl5HA5xs9lUvV4faPRIAQcqRAEgpP2oOo4XL3PPtcDBxhlGrkSPIMaH62LeATg4zUjYyBnywgbeBNPlTzjYXtLaHVLyIU6dOqVUKhXKGPv8pFIpHTlyRNvb26pUKkqlUpqcnBxwYKV9hiqVSt0XyHFgHjUcaooNEL3n2PzdbrcHqubB9rAXAJLsPZcPchzAngMq8tKazWaYx5mZmZDMT96V70/6Gnliv4N/ct+ofNZqtQLgIc8Hppbr9vkjgMDzApBPUQjWPef9/9t78yC5zipL/GRm5b5W1l6SSiqVZMlaLW+yusEYrLYxBtxAMIYmGrqHgQC7J4aGYYAOlm5+0ZgxMQQwwxg6aKAZ1gZs07i9YGNLIGTZWLJs7VJJtUq1V+W+VWW+3x/q89XNp/IiW1aqqu6JUEiVy3vf+973Svd8595zi8WiqfOiIYK9jo4mIPzZ7XYb5z2u20rlrOMh1zqd3bimeC7er1KpZK7FrmiSaFFh4u+xTCZjNoY4l7FYDMuXLzepgQ0NDYhGo8jlchgfH8c3v/lN7Ny5U40HLkG4XC687W1vw7XXXoulS5cCqCawix1XXnklPvjBD6KpqanWQ3nFUKKjUCxQsAg5nU7jyJEjSCaT6OzshMvlMn1c5A4703VIDrhDKes0ZAoKd6D5un2nlJ8j7KkihD0FTQboMsXHvrPKz8vvc5yyczqDKlkrwvExyLPvNr9SyFomAFWpEMBs3RKvkWNjECU7w/P6WasklTbWaUhVh/UnJIw8Ju8jFRCp8Mn7IufQfl/kPPE7VBdkQM51JMmcPK40SmDKohwfg1SSXypXTqcTDQ0NxjiCZgQkMT6fD/X19SZQDQaDZgfSfm2c/1eSjiaJh7yntB9PpVKYnJzE5OQk/H6/IWNMHZRknqqX/Iycd/t9sJ/XnkIla7V4X0hIqfDYn7UX2jQgcWF6mlQegXMVPbsqS+VHpv/JZ4PKifz9IxvuSmWPKW3ymHLnXT4b8vePHXKc/H0l54vrimuXKqlUh+V6lnVhgUDApFZS0XE4HEgkEhgZGcHBgwexb9++F19ciprA4XBg2bJluPzyy40qnM1mzebSYkc8HkdXV5fZcJuP9UpKdBSKBY5SqYRDhw7hxIkTOHDgAFavXo2/+7u/q7LJzWQyxjFJkhYGkQwqSYTsKTWsFWEAymCGDkoyaGPQwoJ17qA6nU7jFsXjs6iZQfnQ0BCSySTi8bipz+C4AVQVoFNJoEOc7HlRLp/t5O50nu1azpQ4GaTwGlgYLdO0Xgjc3WeAaIfd3S0YDMKyLPMf7FzBbalUMtbQdE2TfTdaWloAANFoFD6fr8qljHPMOWH9U11dHRobG808pNNpk/LFNUP7X9YlsUaEQTUwm+ZGEsoddr5G5zQqCgBM7YdUD6g6cHdcummRALE+i31YCJmyWFdXh/b29hdNPZTk++WC635iYqJq7TudTuRyOUxNTRnraNZp8ZlibQtt2rk2R0dHMTMzg/b2drhcLqRSKTgcDnPt/FnWedjrzajAUm2jMQDvDckij0XnukQiYdzUqBJS3WMqKolAMBhEW1ub6ecjbei5RiXxoREAvy9rozheNuIlgea6YmBpNxWg8kMCInsOyc0L1jZR5aRRyMzMjJnHUCiE+vp6cx6qj3Ttm5ycNI6CLpfL9FShWiZVW6/Xi+bmZixfvhybNm0yc1MsFnH69Gl8+9vfxkMPPYQzZ86c95pTXBxIokOL8/vvvx9Hjx7V/kaY3TTZs2cP9u3bhxMnTtR6SOcNJToKxQIHA9BsNotUKgW3242hoSEEAgET+DFIlKlEDFJkoS4w6ygGVLuo2eV+e60FMBuUcledn+HnpDokz8Gfs9msSW/hbjTHznPK8zDFhL1VqJhY1qyNLNNceI32lCZ5XfL65lI+ZEqR/R7YPwvMNr98IXtrvsc5sNfZsF6CQSzTvBiISitxppjxfkejUXNvGMDJcbO+h2SDhd7cYZe77nyPREeSD1kbBsD0Z5J9j3h+v9+PSCRixkKiTTIr74u814RUqi40pCIl7ydT1tjskkoN1UhZeyZ3iKXC8mI7x7ImjseQ65HKGte6fBZ4Dq77cDgMYLbuSSoVUomZ69hyvFKtk2qoHK80BZDPhD3VTKqz8vmRa57vk2DKz0jwZ0ny7DWFkhQzRVKqN1w/mUwG09PTxvjE/ruFGymBQACRSAThcNgQs9HRUZw5cwbHjx/H0aNHX2ppKWoEbvaFQiFjslEul9HT04MTJ06YdOvFCKaZMithZGQER44cmZfkT4mOQrGIUC6XcerUKXziE5/AqlWr8Jd/+ZfweDxob283RID/2VO+t6cgMVCw1+XIWgMAVU35eGxCdni3rLPORul0+hxHL5IQOlLRNCCRSMDr9aKhoQFerxf19fUmEAVgVBLmygeDQWOXy+thWl8+n0ddXR1isRh8Ph8aGxuriu/t1tG8Hn5PNhiVznXALNljwEVl7HzA+gJeVzgcNiqODPY4Zwws7WlWMtWJ94kqRT6fN40oK5WKScEiWJfhdJ41RqBD3fDwMEqlElpbW+F0OnHy5ElTAO52uxGLxeDxeEw6GoOKhoYG+Hw+ozoxfU9aYnPM9nRAuQ5fKV4qzWkukFRz7dIBj2uYqV5UDzOZjKl9czqdCIfDpgFvpVLB0qVLYVmWmed4PG5c1zjnuVwOw8PDVWlk7DNFR0QSmVKphJaWFgQCAUxOThonPqaw8ZkAZp3FWCfEjQ026CSBZi0R1T35zPPcTCckaWD9j1xvfAZI1kjMLcvC1NSUIb+5XM48V3MRNzoYkkxy7ln/EwqFTB0N4fP5EI/HDbliU1KPx4N4PG76d01MTMDj8aCpqQkOhwNjY2Mol8toa2szc0wzAiptTU1NWL16NWKxmCGN+Xwev/jFL/CDH/yg6hlSXFpwOBy47rrrsGHDBixbtsy8XiqV8MQTT2Dv3r1V62ix4bLLLsOf/MmfYP369QDONiX/xS9+YRxR5xOU6CgUiwylUgmDg4PweDwYGBhAPB5HS0uL2aVk6op0MbIrGXblRn7GvoNq/5uf4c/cfZUkhcGtJEqSLDEg8/l8pg6FqXIMjklAZH2PPBZTfxh8UcFiKou9d8pcgfVcO8pzBc8y91/O1QsF2fb35blfSerVC8HpPOtYJoNJpqvJQJLKDAP6cDgMr9eLTCaDQqFgAj0SIO5289iSIPj9fkOcAoFAlc20fY4lkX4hvNhcShVPrifWStGumK8ziH2huZIqIgvUGaRzTcl0LQDG1Y9NPfl+LBaDw+EwjWbnuq9M1eK8y/HNNWb7fEnlgcRO/s2NDemWJ59X+YzK54fPmVSB5RjsvyP4TNBgQNa9MEVV1s/Ya+V4TTyHrCkEZn+fSHtrEip+j69x7mQaKs0SeEy5sUAVSNb0yaagtHXn5snJkyfR3d2N/v7+OdeR4tJBe3s7LrvsMqN0FgoFZDIZJJNJpFKpGo+utmhoaMDatWuNCUEul5uXag6gREehWLTo7+/H1772NWzatAkf/OAHEY1G0dDQgMnJSQwPD1cFdjKtjCkhBGtaKPPLYl/uvspiX5mHT3cl7nzLgJvkgOOQqTAcw8jICJxOJ0ZGRkxQw742TEWQ45Q2xlQT2Hmewdbw8DAcDgcaGhqqHKVknQnrT+yQ5FD+LOsUGGDORaZ47VQBXqs0LMLj8aCxsdHcFwaxuVzOuHix7kC6ZLHnRDAYRKlUwvLly019E5VAuk9ZlmXME+h4RxUAwDkB/PmA5BaoTikiJicn0dfXB5/PB7/fb5S1XC6H6elptLa2IhKJmCC3oaHhBee8rq7OqF4M1nn/ZNDOdT8+Po5EIoHDhw8jlUqZ4DqRSKBQKKCjowN+v9/MM68hHA5X1ZrwdaoJfr/fEAYSUKZe0dmN95briD2DZKNfrv1wOIz29nZjLiLdyEhS3W63eVanpqaQy+VMjxsqTVRbOAeyNox1WnSH4zH5LEsSwUauvMaZmRlTPwOgKu2NBI6f5fmYlsv7SiVYpkTydwlJXzabRS6XQzweN9flcrmMDXt9fb1RmZqamrB582YEg0HU19cbFenee+/FV77ylUWtBMwXOBwOvO51r8P73vc+0yS4u7sbvb2981K1uNDYsGEDPvjBD55jqDMfoURHoVikmJmZMa5A3d3dWLJkCerr602gIHdhGUhJhYeEhe/JnXX7Tir/TdhTkrgrzMBNunbJHWAe296PQ8LpdJpiZwbcJDEyCGYgQ+VH1lLIGh66g9l3g89HWZGpPrz+FyuYl3P4QngpVejlfo/joPIl6384vwx+SSRoyRuJRIz9NPuZyLlm7xcZzEr3PAacL+caZB0JMNvXSVqhS0XN4XAY4wamSdlrOLi2STSkbfZcY5JKF4NqeU1SoeQ4pCU0NwyYNiiVA9Z3kMjwGHbXMqm88nwkCfIcAIzyIhvJcv1yvct7zbVAwsv0OO54SyWWJIz3ns+OJDo8llRkJLGV6o2cM/uc2mt17PdGqrY8v6wt4pzIuj/Ou6zn42uyzozg75FQKIRgMGjSMbnh0t3djWPHjmFkZOQl17Li0gAt+YGzz0ZPTw+OHTtmbPIXM9iYe2pqCmfOnJnXCpcSHYVikaO7uxtf/epX8brXvQ6NjY0IhULo6uoyRGF4eBjj4+MmX95eCM2u0SzwpcIje3hwp5gBonTikuk1oVAIPp8PqVTK9LpwOmfd2BhAsV+M/Zcvd2bT6bTJvY/FYqbuRu7Wc7xMw5L2vzQ+YDEyVQCmQtEd7OXCrhK8mFIjA9O5IFOKOB/nQ7oY7M1FQGVqjmVZxt2L6kIul0OpVDLGAdwhZ9C8ZMkS4wIGnA0kmJbI+WZQzx16u934C11zLpcz6WLlchlTU1MoFovGjYzri8TY6/ViamoKQ0NDiMViaGhoMEEq15JMDWO9iMvlMs1EX2gsVB+Z8sV5pPMZ01+mpqbg8/nQ3Nxs3M2i0Sgsy8LExIQ578zMjOn/Q/cujk+mAnIOOWaqKFSs6LrG74yPj6NcLptr5rMr555zKdcE1Z5MJoNly5ahq6sLvb29GBsbM8fO5XJwOBxobGyEz+fD5OSkUWblBkc6na5SU9lbSs4bSZXsd0N79JmZGdM4mIRUGnSwTkimBTJQpaorlWMWWEuzFa/Xi1AoZNahJIskQUy5bG9vNymabGp8//3346677lIlZx7BTpanp6fx4x//GL///e8xNjZWo1Fdenjqqafw8MMPY//+/bUeyiuGEh2FYpGDQf3Q0BD279+PlpYWrFixwqSRBIPBqs7o0nlK7q7a8/Llv+XP8vPcfeWuL4kEU6Xsu7r249nPxx1uBqIMkOzpPkybkgoLwfFwB1c6htl3wO05/BcDVM9ervLzQseQf9vfkz1CpPkEiRUDTfv3GUBTQfH5fCZglXbgL5auxjnlOuOOv3TcYyE9ez4x1QvAOUqF7AUjHd9mZmZM41iZavly52+uOiwqgXQ5zOfzcDhm+yKRJFH5IEkEZt3ISF74vHEOJAlhHYos9KdaImudJCkGYNwHuQnBeZFKE92W5LGlWx+/I+vtOO+STHH80kwDQNX18HeANCDhd6XyR/IjlV5ZR0R1y34fpVoj63moQksVjL8TOJckZmxW3NzcbPoz0axlbGwMR44cwYEDBzA5OVlVm6W4dNHW1oampiZjMw6cXRfpdNo0b16siMfjWLZsGZYsWQIAGB8fx/Hjx81myHyEEh2FQgEAOHjwII4dO4YtW7bgxhtvxLp167B+/Xo4nU4EAgGzW0s3KaZ+MUAgkZDBKQMPe6oSA1n2G+EuK5u0sZGmbB7J3W2eS/7NHXrZP4P1CNzN5TkaGhoQiUTMdctAEIAxIwiHw0a1kGOmaxYVoEKhAJ/PZxytLgYYyJ6Paxgxl5IjweBUKhoMoH0+X1UKmP17JBUMLBng19fXV9UcvdiYSXInJiaQy+XMONLpdJUb2PDwsFljvId1dXWm3w9rQdrb243zW2NjI1pbW88h3VTvmLr2YuOTBEGC6V6JRAJDQ0OmRiccDlf1ohoaGjKqB+8H57RUKmFgYABut9uoYuwDxTq1VCpllDauQzaVpesalTcSMqp4dB8bHByEZVlGkZO9eUhoWKdVKBQwOTmJcrlsmqDKBp4cAx0SWY/EDQW69blcLvP80IiC94GEkymSUtlxOBxG0aX7m+y7w40a1lDJZ5njCgQCVRsUNIfwer0YGRlBJpMx65XzHQqF4Ha7sXTpUtTX12Pz5s1mflKpFE6dOoXf/va3+NKXvlRVT6W49PGGN7wB27dvx+WXX171Op05FzM2bdqED3/4w2ZuTp48iUcffbSqLne+QYmOQqEAMOs8NDo6isOHD5sgMhQKGYcoy5rtqeLxeKoCbZnjz51/7vIyOJTuSAxi5A6tPb+eQRTJB4/P3Xt+j2k0QLXlMlOa0uk0SqWSMVeQQSDHBJwNOhmAcZeZ6S0M1likbd+J5y65rN14rfFK1JxX872X811ePzCrKJD8vtB3pbpXKBSMKsLmprxnVImYSlSpVEz6GNUe9jMhKWLKl71+R4Jkai53L6Da6U7WfvAzDOCZMkkSTPLE70jCwlQ8aTTAsVGlkHVhXIckQLKeiOYKXOOyhoiEhI0sSQi5McHnj8+ANPuQ3wXOEl7788jngg1p+dySjPC6uRkh66JknR/n0n5/pHopyTeVRs4fz8/3uOki1WeZ3sbPcfwkZvw7EokYa+pIJGKUv0Qigf7+fuzYsQPPPfecuY+K+QPagnPDa3h4GGNjY1qbAyAcDmPlypWmmbDdRXI+QomOQqGoQk9PD3p7e3H48GEcPHgQb33rW3H99deblCC6jXHX1Z7yw6CWQQPTYFhAzZ4pXq/XpPYwuGJgTFMCpvEA1d3R0+m0CQjljq4cGzBrl8vCb9YtsN6orq4OjY2NJsef9RmSmDFtjy5T8XjcXCuDxVwuZ4JymjnI/jqv1DjgtcBrPQbOnUzT4m73XLVH0vCAvWnY1ySXy1UZVrjdbkQikSoC2traaoiQx+NBNBpFKpU6L6JJNUOCaWjA2Vojme4nG7zm83lMTU1hdHQUp0+fNoEzr4djo0sY18bk5GSVU6GscWNNEskz5ycSiSAej5v6n0AggFAohGQyiUQiYepMpGkEVU2ZCkiCIPtK8TkrlUpGlQFg0ulIGkdHR5HJZIyzIeeOdUNut9soahyDbDhL4iefbRI1v99vNiP4fHN+mHrI51Kmu8q0PgAmNZHPPdcGa/EkSeTYeF/489KlSxEKhbBkyRJznZlMBt3d3di9eze++MUvVl2DYv6gq6sLr3/9682ztX//fhw9enRep2ddKDQ2NuLqq682G5/zWckhlOgoFIoqyMLsvr4+7NmzB5lMBm1tbaawPxwOmyA/nU5XdYGX7kVyR1q6eEkFhZ+nkiP/kFwRdtXHnhrH4EjWaPD8HBvHyZ1+khxZUyALnV+sDoakRp6f4yyVSmaOGAzRVvlSIDwXA/I+OByOKpttEiDuuufzeRQKBTPvkUikyiKcqkAwGITT6TQBM1OmqGRkMhnkcrmq3i+v1MJa9mIhaCXMFEnueMqaHK5DmeYl07KoGAKzTmYM0u1rTtbJvJBLmEx1k4YarF3h97hjzWMy4OcxvV6vqXniOaRbnlRn5EYD+09J9UqqNbLfDlP4ZPqerO+RzzeVUZJGOV45V1SLSGxkyh5T+3h9vH67isznNxaLIRAIoL6+3qTqAcDY2BjOnDmDhx9+GEeOHDGqlmL+IBqNIhaLIRKJVD1fR48exZ49e+Ztn5gLDafTiZ6eHjz//PPo7u6u9XBeNZToKBSKOTE6OorR0VGcOnUKDz74IO644w5s374dDQ0NcDjOdg7PZrOYmpoyO5syCOJuMTAb+DMomiulRb5mJzyEJC5Atd0vf5Y1N0z5Yd69VJWCwaDp7SLrVqLRqCl8lmk5cwXJbrcb0WjUnIu7xdxNZkDM8bCG4mIZF9QSknByDmihTMUik8mYIJ4EhYFmS0sLPB6PCdapIDJIkcYHvDeZTAanT582pIlg/dD5EB07Ued3C4UChoaGzEYA+zbRpYyBM+u2OAesG2PqHQky64PoGifdvki4JSmUa5LrnWSP6hFT92RNHDCrcMpUFKZjMh2Q5I3KBlVK/qGhgVRigbN1TrQglySMCi4VHTaJ5TMpU8b4Op/nSCRiTEVouQ3APEPcDGEdH5UbaW1dKBTMGHg/JAHn7ySSq5aWFtTX16OpqcmoQMViEb29vdi3bx++9rWvIZPJnGO2orj00dbWhssvv9w0wQTO/t/0u9/9Dvfff7/eU4G9e/fi//yf/4O+vr5aD+VVQ4mOQqF4UXAXdffu3ZicnMTVV1+NpUuXGqOAkZGRKqtW2ZOE8nexWDTWq9ztlWoHd2WDwSB8Pp8JPLgrLxUXYDZFBahWDewKijRBkMGddKUi8WHw5vf7TS0SAFOLZIe9KF/+W+6QA7NNDpkGRILGYFS6m9kd3Fj/w3Q/+TrrUC42cZrL7YqpapLUskg7n8+bxpZUEljrQZCMMB1L9ilh2pcM7uV5ZWE/A3GpfIRCIVNz8WKQNWQyRY0qyczMDFKplDEaoLU170soFEKhUDDBNe8PA3LeK45NKgkM3llvJgkN/0xPTxvLaH6fiom9LsqyLIRCITgcDhOYMy3OrrTxOU0kElXppLKWTSqeMiWOvXrs/ba4tknSmAYjrb05XunEx2siWWRN3MzMDCYmJgyhodEB7xtVPdnkl/eFa5Pqs1SjaDbS0NCAcDiMeDyOaDRqDAyOHz+OkZER7Ny5Ez09PVVETDG/sHbtWrz97W9HV1cXgLObFtxoWOz3dOnSpfiTP/kTbN26FcDZTaPBwUGk0+kaj+zVQ4mOQqF4UdDu+ZFHHsFjjz2GT3ziE4hEImhpaTGpX5J4MPil25nH40GhUEAymTRkhooHAxru6EYiEUSjUeRyOUOQqBTJfiuykBqYzeGXTRSBWcVH7t7SAYvgLjIDIBa5yx3jFyI6PLf9ddnA0OVymbngNTPQpOrFYI7uUpK40MmLCpR8vVAo1EQh4nwyRYzkI5VKmYCS88qAmBbfVLoYQEtlz+/3IxQKGcc7p/NsvxYG6azfsKstJAusCZHBPxvfNTU1VdXWzAXeGwb1fr/fqAIzMzPI5XJIJBIYHh5GKpXC1NSUITrsrZRIJMzapVOZdBGTqWAy4KfakE6njXJF9zs+H4VCAalUCoFAAMFg0CgqrCGh25pURFwulyFkbI5IUkIiQwI+MTGBYrFoiIp9c8DhcBgyMTU1ZTYJmIpGm2pp1EGbeKlYcb3IFFbW8lmWZRQpXifHTKLDtDSqeTIdVZqgUD0i0eJYOPflchmRSAShUAgtLS2IRCJobm425gOpVAr79+/HkSNH8J3vfGdBBH2LGVu2bMFf/dVfmZ+ZkcDNvMWMVatW4c4778SSJUvgcJztUdfb21vrYV0QKNFRKBQvG5VKBbt27cLAwACuvfZaNDc3o76+HvF4vGr3njU7LOhmHxX2MWHRLwMPv99v3LNoYy3tbIHZ/jHAbM0FCRbJCANepslwp9ce/LCb+1zObuyhQ2LBXWrZXJBgOgxBRYFBob3uggSH85NIJMx10tGJhAeYVXNSqRTC4XDVLjhrFpj+I+29+V0STpIpy7KMFbFMEeQcsSib42eQKHsP8fyVSgVTU1PGhIHgd3h+WehO1YbKDMcuHfwCgYApaJfqj11B43rM5XImFYxzR2WRO/t2p7wXgmw4SSI6PT2NVCpVVThvJ9hcR1SoaFPMhrdUnwhZHyDTpngdMzMzGB8fN0E9x8b1xjohrnum/c1VT0R1Uho4sMaOzw5JpSSM0iKcRIsKmzQAIKEjQeRzzGPxuSIhko0/pWrLY8n0N9pbx2Ix82zxHvEZkOos55LXKp/lQqFg1FQSuenpacTjcQSDQWM+EAgEUC6XcfjwYZw5cwaPPfYY+vv7q9RHxfyHZVnYtWsX9u3bh/7+/loPp+bw+/1YtmwZ/H4/JicnF5TNthIdhULxsmFZFnbv3o2nn34alUoFa9aswQ033IB4PI58Po9isYgzZ86YwmsG6kw9Yv8M7s4Xi0UTiIbDYZRKJeTzeZOuRELClCjZT0fu6suaAAa3PAaDKwbjgUDA1EeQfFAdkQFkNBo1vUpY5O5yuRCNRg1BCIVC5xCdudQVBrHA7C53qVQyzekYRBWLRbjdbtM9nvPIuZVpWwz4eD0kmgz2mD6VTqdhWRYikQgsy8Lk5KSZQ6mosDs8iRyJJFOZ7ERnZmYGU1NTSKVShniyUahsKmvfuafKQpLj9XpNCpIkYLz2Fyv6JtFhM1KmqUmrYQCm/8lLrW2qHCQ4U1NTKBQKplM6SYgkbtI0QxIdEh9pHy3JhFx7cv1QmZmYmDDHAs6SlmAwiPr6ekxOTmJqasoYajBNLhwOm/vK7wAwr01PT5teQ7JORqZYArPmAFKJIiGl2sL7RCLKZ00qMPI+kajJ5qCSnPBYmUwG4XAYdXV1pq8Wr4ubIxwDf39wXrnmZAor6+honEJVkM9hQ0MDmpub0dHRgWAwaFwcjx49iuPHj+Pxxx/H+Pj4i64dxfwD/y/75S9/ieHh4VoPp+YIBAJYunQp8vk8RkZGkMvlaj2kCwYlOgqF4rxRqVSwd+9e9PX1YWJiAq2trXjDG96AcDiMxsZGY40rU82o9NDVCYAhQ5lMxgQv7IfCHHsGaLKfDVNXGEhSRQFmC5ClOkNixICLShKPzeCGaXoATFNUkqvJyUm4XC6TduVyuapUFqDarpbBNo8pHbxkYArAWFZns9mq9DnpFMfidtmzhGSQRFJa75KgyZolSQKpeHBMxWIRHo/HXD9JBP/Dk7v8LPb3er2IRCImzYq7+TKQ5r1nepC9BkkqBSRoDFip9gHn9rLhzjuD9blAlYqOXvl83hA3YDb9UCpspVIJyWTSEEyuTxJyWYNE5SCZTBoiL40F7G5lTqfTKGz2NDyuWa779vZ2Mxan02mUqlwuB4/Hg9bWVkOAJWmXdT+8TpIRNu8kuaJVN803SLxIUuz1OVwXHCefbeBcldVumJDNZs3zLOvLZC8c6bTGZ4zGJtPT04b0kfzymnm9VC353GUyGfPsezwehEIhBINBBAIBNDY2IhKJYNmyZYjH4ybl8fe//z36+/vx4IMP4vTp06a2UDF/sXbtWlxzzTXYtGlT1evpdBrj4+OLWq0LBoNYvnw5Ojo64HA4sH//fnz3u9/FoUOHaj20CwYlOgqF4rxRqVTQ3d2Nnp4eZDIZLFu2DNu2bUNDQwOi0Sjq6urm3AVlQMcgljvBTKfi7i4JAEFywkBJEgbp1MZzMBCStTZAtXGBTM8CYCxsmbbEQJ5B+NjYmFGXeE5ZiG5ZllFXZPAondikMxYwazfd0NAAACYgZ8E2AzkGhEzlk+eUKTqsC5J1JbxeYFZZovogPy/dpzhmEh3ZX4T1FjQYcLvdaGhoMIE+AJMaRacxNvKsr68/J21L1jORRBQKBZNuxnsu086YAifTE+1pW7xWjplEJ5FIVDma8XXWQhWLRZM2RvJVKpUMyZFGGkyBogrBAnaSY1mvQpLO45II8Zj2NdHU1IR0Om1q20hoC4UCgsEggsEgxsfHjWW0DPrtkGYb3CSg+iWNHfgs8LP2OiJZe0OViKSP8ywJNJUh+YzTLEJuQtjT5uT3AZj7wzXOZ5KQKavSaY1/89ny+/1m7pqbm9HY2Ijm5mZEo1GTqvjss8/i0KFDeOKJJzA5OXnOXCrmHzo7O3HLLbdg1apV5jWS72QyWcOR1R6BQACdnZ1oaWmBw+FAd3c3fvCDHywocwYlOgqF4hWjUqlgcHAQU1NTuPvuu9Ha2oqbb77ZmBVwl5y79CwYliqNvY5DumUxmJHpZwwW7akxMkBiYCt3sWUALV3cZFM0EgoGZHTBku5wTBljsCYd3pjKJvt/yEJXewoVr0nWHgEwcxQIBIxBA1P+7MoGSYmsFWGwTdXI6XQaW3DW85B4UQViahEVKCpyoVDIBIFU0Lh7TscqBuccm7RyZt0Gr0lCkla7csCfWV9ir68hSZ4rrU1eVyqVMuYAdEqzN7uVfXFkKhkhHf3kWgmFQia9LxaLobm5GQAM+WBtVDAYNKolVQuSVJn2RYVIqkOsWSLh9fl8ZvzSopr1LTIVkgSApgo8B8mFTB9zuVwmZXJqasqQD5kGxnqoSCRi5koqQQCMayGd3thhneuJBgaSPDHNj2SaKiBrcewmBpJwScLs8/nM+uH8AGcdpILBIBoaGoyTWzweRzweh2VZSCaT+OlPf4ojR47gqaeewtjYmCo5Cwitra247rrrUF9fDwA4ePAguru7MTAwUOOR1R4dHR2444470NHR8ZI1jPMVSnQUCsUrhmVZSCQSSCQSOH36NJqbm7Fp0ya4XC60trZWOTsxSJaBNgMoqbJIwkJyIWtJgFkbYP6RJAk41wmNx2LgZFeFWPRNsiADKxnM2dPfZMoQgHPqUnh9JD72gnheryxGJxmRwXowGDSkyH5tTNORVrvSZc7v95ugXKYNMThkKhvvD6+NChp34FkDxHEwTY81N/K6eFzeF9ZmSEMBeQ/4M++9XAOyvkquO17jC+088t7lcjmk02mkUikkk0mkUiljiMBjpNNpU5sj51uSAc6rDLypLHBewuEwkslklWrFOiSmw8l0LrlGZOolj88+LjLAJ9mlgkF1joRduqVxvkmM5P2R18Y0MqponFNJJLkGYrEYfD6fcdiTzxfXOokLa9okQZTPPO8T55dryU6iqEJJ8izvy1z2wJw/AMbQIBaLGZWWKWx009uxYweefPJJDA8PL+pUpoUE/i6Jx+Po7Ow0rw8MDGDfvn2q2OFs2vQNN9xQ1fh6oUGJjkKhuGBIJpP4/ve/j7a2Ntx0001obm7GmjVrUC6XTY+ORCKB6elps+PLQH1mZsYUlDOQlgSFAQkAE5BTyeBusQyanE6nURzoSEW1hQqFtEAmuZCWtTwXf/lzh1v+Z0BVhTvQvBam8HBcTqfT7Pjz2qSyQuttGaDx+JJIyXPSESyfz8Pv98Pn8yGbzSKfzyOZTKJSqSAej5v+KQzsmdbDwFnOKWtY+F4wGDzHOpjBOgkOA1kSMc4TjSU4d7LGSBI7Xpt0SgNgUp6oGkkiyzlkShRVBTbuHBsbqzJyKBaLyOVypr6I56A65fV6jb1yQ0ODccKjusfr5P2XAbvP50OpVMLw8DCSyaSpt3G5XEbd4f3lXJOYMt2KRITpVpxzAIaEcQ64Hlh3wpo4zqtco5KMM+0yHA6bZ4vrQQY5rBnjfZEKYTAYNIoTNzAsyzKqHY0YwuFwlSFDfX09CoWCseSWyi0hU/Oi0agxg5Dph9J0QD4PTCGUz5zsS9XQ0ICuri5DcHK5HEZGRvD//t//w969e3Hw4EFMTk7Oqegp5ic6OzuxdetWXHHFFVWvP/vss/j5z3+OoaGh2gzsEkQikcCpU6cWRINQO5ToKBSKC4ZisYiDBw9iZGQEa9asgdPpxKZNm0ywkUqlTME9gxWZfiKVFPuud7lcNvUzrFeQLksyaJKF4AyAZY0Jgz67Y5RUU+wF2VKpse98kawRJCGyfojpc0zhYuDMsUj1gkG4rENgYCrPwWugK518nWmBVB44LzK1jaRKXiNVB1kPYa+Fsf+R907WQ1E9AmZ7H9kVKUIGsPY6HpIREh0595J4MP0smUxicnLSWDzz3KzD4nF5PZLISMtuzolUW3g+ziEAo+zQDpn3gumX+Xze1KPwXlI5kfUpvB5pocx54H2R6gVrpKThAdeWXCuyNkcqaXaVkq+xHmkuJVMqUG632xAdzhmfX6agsbZGWkPLdS2VQK5Z2jwzpZDXRkVNKmvy2eBnuVY4Hw6HwzjW8dipVApjY2PYvXs3nnjiiTnXpGJ+Ix6P44orrsCSJUsAzK734eFhHD16tMajqy3sankmk0Fvb++CdBhUoqNQKC44EokEfvWrX6GhoQFHjx7FypUrcfPNNyMajcKyLOPuxWJ32dxRmhTIYmQGscBsDYvs5cFgSqa3kFzwfRmI8fx8nelVMp1HFk+nUimjRpA8yTQxvkaHNDZ0ZADGehEqVyzkZnoPFRmZngecm+IlSaDD4UBLSwsAGIUgGAya62SROFUYqU6QBHEswNnaGhbU8/x2e2v+B+lynW2EyusAzja9lPPZ1NRUVSgu3fEKhQIymYwxcOB5qGgwLS6dTpu0Rq/Xa0hcOp1GsVg0Nsx0yGNKVS6XqwrgSXBJnCSJ4BiZFibNLPjdmZkZpFIpc/3T09PIZrPGDY921OwXQye6VCqFXC5nGodSteM4OR9S5eBaIpkgIZ2ensbo6Ciam5vR0tJiXKPq6uoQj8eRSqVQKBSMckbiRkLEZ4b/5gaCVKio9knbb2DWKIO1RlR0UqmU6UfjcDhM+h9JDhugUoX0er2mCJwqKzchJicnqxqict1KAsx7SIWX6phlWaZPjmVZCAaDaGlpQTAYRDgcRlNTEzweDxKJBIaGhvCd73wHjz/+OE6dOvWqf98pLk0sX74cb3/7243Zy+TkJMbHxxe9AQEANDQ0YPv27bjyyitRV1eHo0eP4ktf+hJGRkYWXPqaEh2FQnHBMT09jYGBASSTSbM7vG3bNhOwsZBd1oZwB5ZBuNxJZ2AjA0NgNq1M7uySKEiCIANWu1ogC7MBVAXA8mcGzHJXWab9ALNpPqwB4Y4/A0qqLRwfaztkmpE8PoNd+3XKWhbWHPD7JGOcd1lrItPCmIomlQseg2OVwbCsqZDpaHJuOEaOjYEy761MU2IgLI9FxUSeO5fLYWZmxrj1UaGhepJKpWBZlqn9ymazVQ5yrH8ipELBn3lP5PqTygN/ZiDN4n7pBEZCyzmhY5xcv1yfdXV1RomTdWlyjHLcvG80iSBhTafTxrxAknQ+Z5LEyTo3/mxfWyRYXJfyOeLn7S5/bLYrnwGuF/l8yB5YJMdyjknMaEnO6+f47KmR8h4yHVX+HvH5fIjFYgiHw4jH46Zv18TEBCYmJnDgwAE888wzUCw8cN1EIhF0dHSYVOBkMomBgQGk0+kaj7D2CAQCWLt2LVasWAGHw4FkMonnn39+QaZuKtFRKBSvGbLZLA4ePIhTp05hz549uPzyy/HWt74V0WgU69evx/j4OEZGRkzQx8COCghJDS1sA4EAXC6XUW5k4bYs8rY7cbEGg7viPJ9MTysUCgBmHdwYJKbTaVNzIFOAuFMu64Wo6LDTvAx+ZXrYyMhIVad4Kh78HkHSFA6HUSwWkc1mEQ6HEY1GTUDKXijScatcLiMSiVQFqzxuKBSqsgNmMC7BQJKKm9/vN00bZa0ElY9wOFx1DxhU0/WKY5DweDym3oPnJDGpVCo4ffq0cROrVCpIp9Mm2Lcsy+z0s8GrNHKQyo1U7+zmFhwv1RU6nPG+8jpJVGSgXSqVkE6n4fP5EIlEzL1nHZAk1XV1dcaBr6WlxfQGKpfLhhTzviSTSXN9wKw1NJ8Bkgue2+/3V9VQsW5mZmYGsVjMqEdcS06ns6pRKNPZWGdGglqpVEx/ID43MsVOFuzLvk4+n8/cF2nyweNyHU1OThoLchI53j/W4tECm6oen3v75gSfI6pDoVAIzc3N6OzsRCgUQiwWw4EDB7B//378/ve/x969e3HmzJmX/P2lmJ8IBAJobW1FS0tLlYr84IMP4p577tEGoTib1vfud78bLS0t55i9LDQs7KtTKBQ1BQOeTCaD0dFRuFwunDlzBg6HA42NjXC73Ya8SHLBnV4poTNgko5RMtCRSo3coeYuNgMp7hrLGiGZumMfCwATeNrPy4BZ7mJzN5rXL3fI+H4ulzNEieqE2+02gaZ0LKNrWj6fRyqVAjC7+01FAYBxRePuOm2lpULE77KmgUG7veZGzjkDYemyJuuqSNLs9Tn2Oia78ibT/bjDz2CeSg7VGgCG0HA87AEjVST5N2uQ5HjlPeW64RzxuHJ9cdysl7EHBFJ5pKrD16X7mSS5Eva1JD9DQmlPj6Qqxh5OdF6Tx5YqiXSXs98LqT6RqJHMkjzZlR05Z/JZ432z259zLnlv2JNJEjmem3NJYieJP78v65Ak8SEp4sZAOBw2tTqFQgEjIyM4ceIEjhw5gsOHD0OxcBEMBtHR0YHGxsaq9Tc6Oqr3/j/g9XrR2tqKUCi04NP5lOgoFIqLhuPHj+Mb3/gG1q9fjze84Q1obm5Ga2trFdkol8sYHx83KUuVSsXs+stmhExjYvG0DAKBWVLB/hkMPGkzTMgmiZXK2V4hrCOqq6urcvtikTXTaEjUgNlu7dxxZx2CrM+YmpoCAExMTKBcLqOxsREOhwODg4NmDmhTzGugQ1Q2m8XU1JRpegjApDBxF5y7/6wpYQ8cAEY5iEQi5toLhUJVQ0qZbsX3c7ncOY1aGdzKgnpJNJk+RmVOpjGRFNAFDTgbEGcyGeRyOdOTJ5lMIpFImN4nPA7nsqmpqcocgGpPPp83xJquccBZBYlqizSqYKBMSILNMWezWdNnhmuG10Iyz3nhfMv+TEzXpLJTLBZNID40NGTSFKUCxH400u2OJJj9jjj/oVAITqcTo6Oj5n5xvfJYHDMAU5dF572mpiYkEglMTU1VOeJxfllHx9ofp9OJbDZriDVroyqVCurr643DnJyjRCKBSqWCWCwGj8eDxsZGo7QBMGOjyiR7Dsm6MCptTqcTLS0t8Pl8iEaj8Hq95u9QKGTW4v79+/H000/j6aefxp49e7Q/ziLAxo0b8eUvf9k0wZTrVFG9wXL8+HF89atfxYkTJ8z/hQsNSnQUCsVFQ6FQwPDwMKLRKHp6eoz6EI1Gq4J75tPLHHy+JyF3pu3qgSxEp3MYA9disWgCJ/tuv3Qrs+9gz1UnIz/DwBKYbWgpd/x5bPkegHMIAQNVpkgVCgUUCoUqK2UARgmh4sNg2eVymYCOSgV356WNMW2LSc5oQy3Tk4rFIvL5vAmqudsvrX05F1KNkelW3LGXqUxMISTBpC02ABOYyzoNnkteA1+TJFnWT/HcsjZFrg9JTOz3USqLfF+qLzLtUK5Lzh/PT8JMgkLI81Bl5DhpQsA5lPU10gGOcycJgRyfVDWpcnE98GfOJ8cnjS6k4569xkeOX843ibZd/ZQ1atxIkHVU8n7yWbfXgvE54phZ6xeNRg3hoSnJ1NQUBgcHcfz4cRw9ehR9fX0YGxuDYuEjGAyis7PTbHKkUimMjIwsaNXi5cLtdqO1tRVtbW1wuVzIZrM4duzYgk7lVKKjUCguOk6dOoUzZ86YAPD222/H29/+dgSDQXi9XlNwToLBAma5m8tCewDGlYu1AkyDyuVyZneXQX06nUY2mzU9dew1PbJOSKpEVI+oCDCQY7Ame/+QoHAXPBKJwOPxmKaRsrgfQFX6kVSPeL3pdNrUy9AVi0Ek55BkiW5Yg4ODqKurM2RP9iiqVCqmNoJF/KVSCaFQCE1NTSagnZqaQiKRwMTEhKl9YL+ZfD5vdtBZF0OlinNG5Ya9U5imVCwWTYCazWaRzWaN2rJ06VLU19dXFa4DMPeLZGF8fNzUlUjzBBoxRKNRo0b4/X4zTipGwGydCe8hG6SSZBQKBZTLZbOGqIDwPJFIxLiocR1xjum21tbWZgKuSqWCaDSKuro6TExMVNmNZ7NZuN1uo2AFg0HTD4lBPdcN68Gk4iOfEfa04XPE1MJUKmXmiiqLJGJtbW0mZTAUCiEcDhsnPF4XQYWK5JjzmEwmDZHlumLdW6lUwsjICAAYBz3eZypAJGJ8BmXKJZ37qCwFg0GEQiEsW7bMKJ3FYhGTk5PYsWMH/uf//J/mmZf274qFDaYxMhNg9+7d+N73vocjR47UeGS1R1tbG774xS+iq6vLWLgfOXIE6XT6nNTahQIlOgqF4qKDAS/R39+PEydOYOnSpYjH43C73caqWNoBM7iz18/IXWOm20gyQXWHgSt3iiVhkTvLc9UgMCiTxfaS6EjXJ/mz3KHmLrXcmZeQ+eR2lYApWpwDnku+L52p7CoKj8/rZ8E8u8OTwMnUnlQqhXQ6beyeAZj0P36eDR0tyzLpSayNYOG7dEIjOaHCxXQzaaZA8sK5kAoWz0UDAako2NUf+X07mWVqljy2rE/iMUliuUaowAAwBEJCqivy3Dy/TIvjPeV3SGzl2pQ1O3Jscn1yfdPggZ/leyQM9noaScp5Dvu6lEYPvD4JkiySJZ6TpIXHkzVhXOfy2ZO9pOR18XvA2d1opi7y38Fg0NTypNNpTExM4NixYzh69CjOnDlTZTqiWNjw+/1oaWlBa2tr1ZpNJpPo6elBIpGo7QAvAXg8HnR0dKCtrc38rqV5yUKFEh2FQlFzPPLII9i5cyduu+02XHfddVi+fLnZXWZ/Etn4kcGazOunSkFFB4DZ/adL1fj4uHFXc7vd5wTksgZIprpx91sek+eVJgQM9vhaIBCoUqjYTJI9QqgEMHDmsaUFNd9noDw9PW3qMGgh7HA4TKqfTJsiESNmZmYwMjJS1YSzsbHREB9+L51Om54/09PTJphk/5Tx8XHk83mk02lTFA7AzBHdvizLMoqOLDSXTTFJeh2Os31s2KeFqhLvARW1JUuWwLIsDA4OmnQvSRCpdnCdkHgyVa9YLBonMd5zEhNJqGVNizRhmJ6eNmRQqln8Q9WBtTis8WG6Ih0ASe54jvr6enMfZXqWTJPjnPJaSJK4vpqbm9HY2Ijh4WHk83mzPsbHx415AQDkcjk4HGebaM7MzGB8fNyoKrIOjT1wpDMeCRnTKX0+X1WdDJ8JzhH7SZEQ03Kcx/J4PMhms8hkMmhpaanqB8RNBJoNNDc3IxaLGXWPz1c4HMb4+Dh27dqFvXv34p//+Z9NKuRC3aVWnIvOzk781//6X3HZZZdVEfaJiQkcOnSoypBjscLj8aCrqwv19fUYGBjA0NDQgn9Gzt1SfBHcc8892LRpEyKRCCKRCLZt24aHHnrIvH/DDTeY/yz45yMf+UjVMfr7+3HrrbciEAigubkZn/zkJxc0k1QoFC+NYrGIZDKJvr4+HD582PwpFoumcaHf70cgEDApWPaaCWDWUEDuigOzig77lkiXMHuqGI8rO9nzHLIYnAGfXV2SNR8yeLanqjGYZKBPNUamB1Gpse+4yVoTkhN76h6Ddc6JVBAIzov8DFOVcrlcVZ0U64Gk+sJzsH5Ikj7ppiXVLLlbb1fWJPFjEMw54Dl4DZxD3gdeHwNwpgjKgEfeK9lQc676K7taJl3BmLZlJ8eydoVEmsTTbrghiQyAqv40VEakIkSiJNMVubakOiLXKNeXVJHsdU/yOZE1bSRUdlXGTnzkMWQNj1Rk5OeoNklnOBJirhUei+egahSJRBCNRhEMBg3JKZfLOHHiBA4dOoT9+/fj6NGjmJiYQDabXfABnKIagUAAnZ2daG1tNQYnvb29GBsbM8/PYkVdXR06OjqwfPlysxHzzDPP4MiRIwueAJ6XorN06VJ8+ctfxurVq2FZFv7lX/4Ft912G5599lmsX78eAPChD30IX/ziF813ZB+FcrmMW2+9Fa2trdi9ezeGhobw/ve/H263G1/60pcu0CUpFIr5ij/84Q/Ys2cPwuEwQqEQPvnJT+Kqq66C03m2V004HMb09DTGx8dN0AXgHGUEQFWwJFOiGCgxWON/frIBosPhMHUAPLbP54PL5TKmCfyurCEAYFLTZNAn032cTqfpOzM+Pm5qiTweD5qbm811yACX/UzoAMeicZkOxR4wrANiLQqDWioAbHbJAJZKCYPNfD5v6hq4Q8+deFpc5/N5E5BWKhXTo0b2ceH8yEarJAskD0xXoquaVK9CoRBCoZBxY6OBRHNzs2kGSWKTy+WqDBuYHsfdfpl2RoXA5/NhYmLC1KPIBpsyJY1KIfvO0JZ1cnIS/f39qK+vRzQarVqHwFklw+12V1knU2Gz19GwkaXP5zMNDkmuKpWKMYVg7Q5JHkkUa4/S6TSKxSKCwaBR3lgHxHQ/r9eL5uZmEwgCMM178/k83G43otGoaawpXe9olBEIBDA1NVWlnpJgSZJGJYfPoEwBJImmupXP500dGutvaF7A+7h06VI0NzdXWbj39/fjnnvuwcmTJ/H73/++yu1OsbgQjUZx3XXXmWf+5MmT+PWvf419+/bVemg1RygUwrvf/W6sXbsWwWAQR44cwWc+8xnzO2Ih47yIztve9raqn//xH/8R99xzD/bs2WOIDhs1zYXf/OY3OHz4MB577DG0tLTgiiuuwP/3//1/+NSnPoW///u/N4GHQqFYnJAd5GdmZrB//34UCgU0NTXB7/ebYng2XOSuLXeQCekaBcAoEHZSInfDueMnd8alc5XcsWegxp8lcSARoWNVsVhEoVCoqq0hGFhLQiV3wvm+HIusEeK5GYQydatUKpldeVl4z7QsAFVKwlz1T7Sj5jmoEsnvy5oPqWpJpzC7yi+VM6bXkbjJFEJJhnjeSqWCRCIBn89nPiN7JPF6pCmAdCuTBg5SDaRTF6+TSgxTu6TxBGuLSH4Z2EtCK2u97I57XN9cg1wnMr1NrkMeW9bPSMUJgCF2XOe8L7K+Rt4fWbdFSJWHZJ3PGVMJ+TzJ+eXPdsXO6/WasfC6pSop1zpVVp6b6X+WZSEYDKK+vh7hcNjYnKdSKaRSKRw+fBgDAwM4duwYhoaGjKqoWFygkrNq1aqqBqGTk5M4ePAghoaGajzC2sPj8WDVqlXo6uoyabisz1noeMU1OuVyGT//+c+RzWaxbds28/qPfvQj/PCHP0Rrayve9ra34XOf+5xRdZ588kls3LgRLS0t5vM333wzPvrRj+LQoUPYsmXLnOfibhbBpnkKhWJhgrvz3/ve9+D1evGOd7wDXV1d2LZtG4LBoFEmZBBsTx9joGZZZ13LmPMvC55nZmaMUsOddwavMtCUBevshSJVFqnKcMc7EAgY9YNd4gOBgElZk8Gnz+erssDmbjfPLfvmsC8KwQCS15dKpUz/ErnrTxWGAS1rJZjixfqhcrkMv9+P+vp6E7gytc6eWkQyIhUopjzJnj0yKJe9elhAz343Pp8PiUSiqidQNps16WUOhwP9/f2mT4vb7TZ9WdhbhumJTHekcuLxeMx64bzwOsLhMAKBACYmJjAzM2PUMfbqoV02A3X2jGlqajLpgiRVklADs0oh0/l4n1gfxoa5o6OjRh0jWSaJ4vVKoiM3BnO5HJLJpJl/ri2m9vHe8p6x9oYkiOMl2aOy09LSgnQ6jcnJSTQ1NZmdcmlFnkwmDUGUxDYWixkHNCozkmByrZGU042uWCyioaEB9fX1htDx/vj9flQqFZw6dQrHjh3D5z73OYyOjpr1p6lqixPNzc14//vfj7Vr11al/vb29uLee+/V8gicNWq44YYbzqlfWgw4b6Jz4MABbNu2DYVCAaFQCPfddx/WrVsHAPiLv/gLLF++HO3t7Xj++efxqU99CseOHcO9994LABgeHq4iOQDMz8PDwy94zrvuugv/8A//cL5DVSgU8xz8D6q7uxuZTAalUgkNDQ1YvXq1SWchyZAF6ABMEMV0GtoOMw2OYJAnd9KpnsiaExkYyxoEqcAA1b0+OA47CZOpTKx94XjtwdoL1VPYyZdMwePfDJQBVDli2ZUCBtMkLUzTmkuJorJBNYXjk6mDsrEjPycL6jleWTtEFcheYM+xcxzs6cOgN5PJoFgsVqkZHKd9DiW55HzLoneZfsjv22tfqP4Bs7VWJLvZbLZK2eB9cLvdxmSAc0IyyvtC5zCenyltMoWRa4qEjmSOlulMueO5Oc92VU2SVY5Xzo9MM5M1SpLo8j6T6Nlr0WSNFEkVAHMfSfilSsk6PJpf0OCA633//v3I5XLYs2cPent7DalXLG74fD6sWbMGy5cvh9PpRC6Xw+joKMbGxs6pS1yMkM6IxWIRR44cwYEDBxYNATxvorNmzRrs378fyWQSv/jFL/CBD3wAO3fuxLp16/DhD3/YfG7jxo1oa2vDjTfeiJMnT6Krq+sVD/Izn/kMPv7xj5ufU6kUli1b9oqPp1Ao5g9mZmawZ88eOJ1O7NixA+3t7fj85z+P1tZWk+oEzKanMdhmDxWv12v6kMRiMfMdGbTK4J2BmUwFk69L9yt7MT8DYrfbXVVID8xaVjOIZeDncDjMDjuJGF/neSVhkUSHwTkDRdpxT09Pw+/3G4JAty8ApmaCASvTxhgEM6Bm8E6FhK5obDJXKpWMMQTVBQadrCWSATfPJc/DsXKuOZdOpxOJRAKZTMbU5DA9MJlMoq6uDvX19ZiZmcHU1JRxNuO9IlniPAGz6YqSaLJnTLFYNMoP7/nMzEzVeOkuR4WHfYV8Pp9x/ZqcnDTnJLlmDx9ppMDrdjqdxk49FouZNc+6LJIwXg/TupgO2dbWZgjp1NQU4vF4leuf3+83pJEEFphNTWMtkExNq1QqVXbTnC+eU6b/ScMFuyGIvW6LzyWJquybw9TB+vp6xGIxk6rW1dWFaDSKkZERTE5O4v7778eJEyewc+dOpFKpRR/AKs4iGo3ida97HRoaGgCcTVl7+umncfLkyRqPrPZwOBxVNX2JRAI/+9nPcPz48UWRtga8AqLDPD8AuOqqq/DHP/4RX//61/Htb3/7nM9u3boVwNnd2K6uLrS2tuLpp5+u+gybh71QXQ8AUwCqUCgWL1jYPDExgYcffhhNTU1YsmQJAoGAaTAZCoXMbh4Ao0wwUJZF8oTcYZbqiVRXpKJC9UC6pPHfAEwgzPHKmgxZaE/Fg7vkMoWIkEG2/JmKwPT0NCKRCAKBgCnCt39fpjhJNYc/yzoKea1UYhj8Spc0Wa/BegxZLyTJBZU09uph0Mzglg1EGWAHAgFD+KRqRuI0lzMdDQ0cDoexTpbjoL04cPb/MNa08GcG+7znkUjE7AxTMXS5XIhEIlVjKRaLxugBgDFsKBQKhuzZlSX+zTnl/FMV4TrkMUOhkJkzkkV5XbQBJ1knMWU6pryPJCpUa/L5PBwOByKRiNkY4DlLpZJRTOyNeTk3cszSeEIqgLwPJHZ8n6luNB4JhUJoa2tDPB43KZ7j4+MYGRnBzp07MTAwgH379mFsbMwYaCgWN7xeL5YtW4bOzs6q3wnDw8N4+OGHcezYsUW/Tnw+H/78z/8ca9euRSQSQSaTwdGjR3Hq1KlFU8/2qvvo8Jf9XNi/fz+As51YAWDbtm34x3/8R4yOjqK5uRkA8OijjyISiZj0N4VCoXghsHbn+9//Purr63Hrrbdi+fLl6OrqMsHx5OSkqUlgShB3jyXZYIBM0hKJRKpMBBhMBQKBqh3rXC5nSIX8PIO8mZkZ4xTGz3k8HhMcUkkhQZI1HfI/Hqno2N3bSAoKhQIaGhpMAC7rM0iQSHTsv6cZADCglsYIMjWJtS2yFoefkXPMGhl+jgF1IBBAU1OT6ZWSzWaN+hCLxUzjT46PqUvSwUwSTta8yPopp9OJYDBYlYonU1ZYqM+gOp1OG+JDEpbNZg0RiMfjcLlcGB4eRqFQMGlhPAdJGMkSyQcJZzabNeRPklT5N0knFTcSCpLyVCpllJ50Oo1EImF2ZuV9IRkjsalUKmb+uN5InjgfVK7S6TSCwSCampqQSCQwOjqKUCiE+vp6jI2NYXJy0qS8cG1zvdPhjUqYTNHkveLftCXnGqbtdV1dHWKxGBoaGtDQ0IBly5ahtbXVqHeHDx/G4OAgvvWtb+HQoUPn9btCsfDh9/tx9dVXY/369VUNdfv6+vDDH/5Q0xpxdtPijjvuwJVXXgm/34+RkRHs3bsXp0+frvXQLhrOi+h85jOfwS233IKOjg6k02n8+Mc/xo4dO/DII4/g5MmT+PGPf4y3vOUtaGhowPPPP4+//du/xfXXX49NmzYBAG666SasW7cOf/mXf4m7774bw8PD+OxnP4s777xTFRuFQnFeyOfzOHDgAAYGBnDmzBksXboUb3zjG03nZyoFMn1MppzJ2hq5+y8bMfJnmXZFkkIDAQbaDMAZcPI4THWThEXWmzDgm8siG4AJCqUyIBUYkiXZn0Ves6zLAGCKyEnOSOjYUDIYDJpcbja8tCzLXKt0IaORA62BSZqY0sbxkaDyWAycZW8LGVCzqSeL0KnC2FUmKgNM2+N3qNhwborFoiEfVKYYBJGsMiUMgCET0pQCmK0ZI2kmiSSRIsHmXMnrJbkgSaLBAsdhr99iSiFJIwl3JpMxTTplvY29BocqJUlxpVIxZgI8H48/NTVliCBTEwEgHA5XuaUxlY3rVPbpiUQiJg2T80qyS5Lo8/mqzCd4L+vr61FfXw+fz2f6e/T09ODYsWOm1kKhsCMcDuPP/uzPjNsan7nFUnvycsGNjuHhYYyMjCwaJYc4L6IzOjqK97///RgaGkI0GsWmTZvwyCOP4M/+7M8wMDCAxx57DF/72teQzWaxbNkyvOtd78JnP/tZ832Xy4UHHngAH/3oR4170gc+8IGqvjsKhULxclAoFPDcc88BAHbu3Ikrr7wS1157LaLRKBoaGlAoFMzOfTKZBACjogDnmhBQHXgxoiPtgr1er0nfYkDH4Jf9aOh6JYkOMNscksE/iQHHJBUZezBMyOtgMEqiw2CXY5f1MtxUovrC+g06oDG4ZTE808xkg0wqVKwDSiQSVUREppfRxctOmphqJYmOz+eDZVnnEJ26ujpkMhmTJscie6Yj0pnL6XQiGo0a5YjzQqITCoWqzA9ITBl800yA983e1FPWc8maHRIdpuDNVetChYfjlkSba49rQ6awsc4lEAgY8wUSBxJP6XhGVCoVE/yl02mUy2W0tLSYlDWusZmZGaPc0Nwjm82auWStEABj5CGNCXhPeX0jIyOoVCqIRqNV1t8cDxt/trW1GaITjUYRjUaNQcSuXbvwxBNP4NSpU0gkEq/q94Ri4SISieCmm27C0qVLAcwajKiSMwtuAjmdTgwNDWFoaGjREcHzIjr//M///ILvLVu2DDt37nzJYyxfvhwPPvjg+ZxWoVAoXhKDg4P47ne/i6amJqxatQrxeBxLly6F1+tFPB43gXsqlTIubeyLQvtfaSEsi9e5Y86UNgaz3BGngiP7jACz7ljRaNSkGvF9KiKSQMk/JEIyZY3/aVGZ4Ni4u87/6BlUypoI2VOIn6OVNH+W/UukIiKPwzFwHNJti8oI7a9Zk8N/83pkw1OZxuRyuYwKJxtKcuySLNJOmXMg64L8fj/i8bghQvzD4Jx/OP98neSR52YqHIN1ql8ykGJ6IBUdEiBaXmezWdNziddeV1dnyAcVKbZhSCaT5xg38D7U1dUZskbjA6kWUYHieqVKKFPtSFb4eZIVqfpJq3OaLszMzCCZTJp7R7LL+8ZjhMPhKrtpmha43W74/X5jHd3Y2Gg2AHp7e9HX14f+/n4MDw/jueeew+nTp01qoEIh4XK5EI/Hjb07cfLkSXznO9/B4cOHF51qYUddXR2uvfZaY+iRTqfxwx/+EEePHjWK7WLBq67RUSgUiksBo6OjuO+++9De3o5rr70Wl19+OZYsWWL62ni9XhPsspCf/UDYwV4WhcuUJVkrwmBWNjikagDMuqsBs6lZ3KGnaiNf46683d2LpgdAtT2ydHST5yPRIWFj3QzVA6ZTsYic18EaFapBDNplih+DBhIdqiAMxnlNDGpJxJjGxHmj3TeDZPu4qYrIxpnshSRVM8uykM1mq5zEJCGl6sP+MnT3Yn2WrEdifQk/w7otea08pzRNkCRCGljI9Cy3241cLodMJoNgMGiUIpJDXjfJGV3kqFDJVDSSDr/fb/o4sbcMCQuJjlwzXCOSVBEkQHJugOoUT97TTCZj2kpwfmV6H80GAoGASYfjOmeqZzAYNLVZ8XjcjGNoaAg7duzAvn37cOzYsQv3S0GxIEG3QvZaIvr7+/Htb38bmUzmHGOXxQaXy4UtW7Zgw4YNCIfDyGQy+PWvf43u7u5aD+2iQ4mOQqFYUEgkEnj22WfR09ODffv2YfXq1di4cSOamprQ2NhYpeQwKHU6nchkMuZ1BrhSQaCSwyDX7ipFAuLz+arUA0IaC9A4gY5ZwNnAlGlYDDpZmC5VJgaXTB9i2hTJAQNQ7tTzu1RZGNSTmMk0BjrGcXdeBrLAbO0K/yaouLCJJwNzBtWyhwrPQ4WNKWL2AJ2EgGlobOIpyaLdRY7BvGygynmkykLljU1Y7ZbUMsiXJMzlcqG+vh7T09OYnJxEuVxGJpOB1+s1PXD4XQb9PJ6sH6DlM+8hwdc4LjqccR5YC5XJZBAIBBAKhZDP55FMJs26kGuSa46Ejq8zDY6kl8RfkkkaS+TzeYRCIUOu5HxwzHx+aDYQj8cNOfN4PGhra4Pf70csFkMwGEQsFkMikTCkhoXRp0+fxvj4+Mt/0BWLFuFwGO95z3uwZs0as5ZlOuViJznAWUXn6quvxnXXXYdwOLzoVBwJJToKhWJBIZfLoa+vD319fXjuueeQTCbR0NBgep4w4GWQxtQHplkx0Jb1OJVKxaRyUd2w1/rINB2mG9khndD4GUkIGGxKZzZgtk7ITjxkupW0fuZ7rN+RDmUMVGXdD8fFVC9pIywJjWy+KmtCOFbu9EuzBJl6RwLAFMBkMolQKGQK2eXnGeCnUimUy2VDDvk+r0f+IRGlG5lMqWMPIKbi+Xw+o+rZ75HdsILKUTAYrOpDRGJK8iINGPg9uYZkAGafW56b5JZqnlw3hULBuLH5fD6kUikkEglT6M/jcT6YBsix/4XIJwAAODBJREFUyXoaKn3STQ+AeUZkSps0l+CccH0wlS2dTp+jXPp8PjQ0NCAYDKK5udmoqxMTEzh16hR27dqF+++//5znRKF4Mfj9fmzbtg2XXXZZVV0f03YVZ39Xd3V1Ye3atYue/CnRUSgUCxonTpxAOp1GKBRCOBzG5ZdfjhUrVmD58uWIx+OmhoLF9kztYpoPe95I22JZBM8gn+lDLIJnME3iI1USEhWSDjbzlEG33PXP5/NVioYdHBeDS7tC5XK5kMlkzDllypXsPM/3GaiTlMlaGHtdB8ct636Y4kU7Yl6PtIbmnMr6GmCWNPHYnCP2UyM5IeGYi1yR0EmlRo5bBvkco1SWUqmUIVR0CGPqH1UKzkm5XDZmF/X19YZoAbM24OVyGfF4HH6/34yB15/JZAzBIclyOp0IhUJVc0jViIqUTCGkPXY4HEYwGDTmECRrVO9YtyX7AmWzWVN74/P5kM1mMT4+jnA4jPb2dkxPT2NiYgKVSgWBQMCogbwe3qf29nZjh00jCa/Xi0gkgkKhgBMnTqCvrw+7du3C5OQkBgcH1U1NcV5wOByIxWJoampCe3s7mpqa4HK5TH3mkSNH1IgAMPVLHo8H+Xwev/rVr3D06FHze2qxQYmOQqFY0JiYmMDExIT5ua6uDg0NDaZZoSzoZ2E6a0Vk/QX/LR2ypDubNDSQtQlSvQGqFQ6ZfsWfgdnO9Qx8SWJk3RAh3buYUsSaCdYgyboTBr+SpMk6HL7PsZLgyXoVGeTL75BASFevuWpGOAdSFZLnlK/xXDy3TEmT92WuOSGkg52s0+HPVCVIdKiEMC2LagnnjXUoTOfiPLOeSDbOZPogyQpri3idJIycL56D9VV83+v1GpLDdcgx8xySuEpFiscgmeJ98/v9Vc1vgbPqYSKRQCgUQjQaxeTkZFW/IKZJyvomp9OJWCxm5sDr9ZpCcaYfDg0N4dChQ3jwwQeRy+VMc1KF4uWCToDsHRYKhQCcNfDYtWsXBgYGFr0JgcNxtgFwY2OjqT197rnncPDgwUVr7qFER6FQLCo888wz6O7uxgMPPIBwOIw3vvGNWLFiBerr6xGNRk2KEINOKhKBQKBqt54KgQy85fsMrLnDKAmDTBVircRcgb90EpOub9K8QKoqsi8NCQCDWzaPJGkIBoPmeASVDZ5bprjJ88kGktzpJ6anp00wT9c0n89nUsio4gSDQaNwyFQ5XjMDcFmXRDJJ4kAC43a7jRsZr13C4XBU2SLT7prnZCNOfo+fY0BPYwDeXzbvjMVihmAGg0HjTsb7yTUgHdyoDNEJjtfHe87jkWxJS2s22CR5cLlcxgKa91828cxms1U9nagKcc2wrqa9vR25XA6jo6NwOBzGOIFkLxwOG5MINk2NRqMIBAIm1W3FihXGGKFUKmFwcBCDg4O4//77kUgkMDExgVQqZdzyFIrzRTAYxEc+8hGsX78eDQ0NAGA2Vrq7uzE2Nrbo15bb7cZHP/pRXH/99Vi1ahVKpRL27t2L5557TomOQqFQLAaMj49jfHzcqC0rV65EOBw2QSBfZ52CTBOSqgQDOu7OM8VKGhpIhy9JbOz/GXM3XqZsEdJxTdahyF418rg8nz1Nzq4KUTEg0ZEqi6znAaqJA48tr086iTFQ5x8G+1LhkeqQzKuX88L5lN/h+aUixWuUxIevy3oeqeRQtaNSI53VpOpFZUaqHaVSybioyTnh5+wkS74vxydNDuxGAnMpT1IllLbfrImRa5Dzxb9lGp/8w/tLEl8oFEwdDe8blRtpukFDg2AwaBwNSY44tyMjIzh16hT27NmDZDKpCo7iVcPj8WD9+vW44oorzOZJNps1BHoxF9wTDocD69atw9atW1Eul5FKpTA2NobR0dFaD61mUKKjUCgWJRjkP/DAA3jiiSeMk9U73vEOtLe3o6OjwxAeez0Fg0mp7lANCQQCpjCW3+MOO+t/GNDLtDZJNKjCADBBJQNc/mHNjjQ0YMqaJDxUp4DZni91dXUmbYnn9fl88Pv9CAaDJqDnZ1kHk0gkkEgkzM8sbpeubDwe06jowiad2KgMhMNho0Iw7Y/zQztqqgQ0i5BmDpwfKkFUa/L5PFKpFMLhMBobG5HP543KJO2cGZxTzWF9FYAqcwRJXkgSSTQZ2BcKBaOa0XpcEicqK7wPdsWrsbERMzMzpqg/EolU1YJxTbGZqkxt8/v9Jm2Olt6cGxJ2mWaYzWZRV1eHcDhsxskePUyxczjO9g1ig0+mwkQiEfj9fkSj0SrTgkOHDmFkZAS//OUvMT4+jnQ6jXw+j/Hx8UWfTqR49WANWXNzM5qbm+FyuTA0NISvfe1rOHbsmBLp/4Csbdy3bx9OnTpV9TthMUKJjkKhWJRgEDoyMgIAJtAbHh42u9RM92HwV6lUTL8T2WdEWlIDOMcxSyoG3F23qxvy8/ZaHLuiY69RkalRdrezuZy+pILA1+3vybQ5qlwMapl+Jd185koX47Ek8SJZoe0xVTOpMEjVgceSwbq8Pvtc8XOShNnnT94Tef8kEeK47WqSnC+pFvGc/I6cG/vn5HVwHEyR5OepIskaJKp5MgWORg0kXZJI0+RC1l0BqHL6k4SRBIpEjkSJPXBIDmkMQdOLbDaLvr4+DA4OYt++fRgfH1/0KUSKCweaEDQ2NiIQCJiNhEKhgAMHDqC3t7fKqn2xgptH3FwaHBzEqVOnjBnJYoUSHYVCoQDMbvqPfvQjo2TE43G89a1vRXt7O6688kp4PB60tLRgenoa+XzeqBr2IByYDVAZ9LKzPB3UZCArA2GSKLsSQHWJRIm7/VQkZIALoKoWRBbz02aZBewscLcHwvl8vmpsHEepVEIoFDIKFetTJFlh0EHHukKhYHq+MKDP5/OGUEiTAkKm5cnUKyoz/EwwGDQpYzweVSgA5j95v99v5ozXyvtC1YjnItHw+Xzm2KybodU1gy3eI64Hnj+bzZrXWNcyPT2NZDJpxsB0O2C2JonH5Trg8YLBIIrFotmdpXEGiZM0l2B/Js41Ax8aUxSLxap6KxockLjTTKC1tRV+vx8+n8/0wenv78fExAT6+vowPj6Of//3f8fg4CAKhYLpMaQkR3Eh4fP5cMcdd2DLli1YunSpeb1YLOLEiRMYHBxc9Kqhw+HA29/+dlx99dWmNufHP/4xdu/evej7UynRUSgUiv9AuVyuymVOJBLo7e3F9PQ0GhsbDbFh+pIkH1LloXpjT6uSgalURWSQLU0LAJyjnMjXpOoiCYO9jgaoVm0AmLFIcsHjStJE0wWZViZVJQbjPJe9HkVeB9UcqWzYa2/sxfvyWu1ub5zbuVQrkg678YMcr1R45Nzx+pgiJxuw8r6SuMiUMBoGSIc31i9x/kku7YqUnHOptthrtOS885wkdRyH/Lf9Psi6HipDgUDAzDV7DoVCoSpCnsvlUCgUcObMGYyMjODkyZMYHR3F8ePHcebMmZd+uBSKVwin04nly5dj9erVps/U1NQURkdHTXuAxQ6Hw4ElS5bg8ssvRzAYxMzMDEZGRjA0NFTrodUcSnQUCoXiBZBKpXD//feb3f1YLIZ169ahs7MTN9xwg8kbl5bUVGykS1k+n69qTjkzM4NCoWAUAtY6FAoFk0LHAJuBs1SGpqenq5p3ymJ8urLZU8mo5PD86XTaOKaRMFCZYM0Md/NZR8M/Y2NjVQ1SGUjze0yZYqBPhzd57fbmlnxdKhtutxuZTKaqJgmAIQx0OCMpAWDUJpITHk8qMoFAAH6/36gQPCavI5VKmfEGg0FEo1FzvyzLMnVMLH7m3PL+yDQ1qW7FYjFMTU2Z9DZZM0XSJJuMSnDupQEGa4NotTs1NYV8Pm/SzUiKaJxAIr1s2TJD1kKhEJYsWWLWn8fjQSAQMER4dHQUY2Nj2LNnD44cOYLBwUHTp6dcLpv+TArFawWn04mOjg6sWrUKbrcbU1NT+L//9//i6NGji77+hHA4HLj88svx+te/HpZlqbuhgBIdhUKheAFUKhWkUinzcyaTQTgchsvlQm9vr9mJZ/8QGgnIGhgaAjCA5WsEd9ztjl/SiezFHLnsqgGDbHvtCv8tlSUSMcLu3CYVHgbssr+PPLZUdKgCzOU2Jut/eI3SFY2Q7mT8Yw/+ZS0PyZ1UtngOWRdjr32ShMv+Xc6THCOAc45lV4z4vlSk7GOW5hPAbOoaIY8BwKhJJJby+uzXJRUxkmGHw2EUSObxO51OQ2ZpnsGmudlsFul0GsPDwxgfH8eJEyeMha+6WykuFjwej+nNxM2M6elp9PT0oKenR2tzAITDYYTDYZNeeuTIEQwMDOhz+h9QoqNQKBQvE+l0Gs8++ywOHDiA3/zmN0bRuPLKK3HjjTeioaEBzc3NVWlTdHcrFAoIBAImCCVBYaobMFvQz6CWtRRUc6gqUBGhkgOgKp1KOqZJIgDA9FKQ7jzShlmmusk6nunpaXg8Hvh8PjNO9mexExi/3w/gbLGwTBeT/XaAWSIh/9D4gME562poEiANAWTNkeyLI3vHSHWFRLRcLiOXy5nCZt4rBlI0FqDKlE6nqzquc37j8ThyuRympqYAwBgCcHxMa2TNU11dnbkW2jTzHo2NjZm5sywLiUQCgUDAuMYlk0m4XC60tLRUmU5I8sxaHCpSXV1dpoaJLmsejwf19fXwer3G2S2TyaBYLGJwcBDHjh3Dnj17cPr0afT29hqiyDWgu8SKiwWHw4HW1la0tbWZ3ynA2d8rzzzzDI4fP15l/LJYsXXrVlx11VXo7OxEqVTCXXfdhUceeQSJRKLWQ7skoERHoVAoXiYsyzIF+blcztQ4DA0N4eTJk5iYmEB9fT1isRhCoZAJwln3YHdZY50Of5bKDBUOmbbGoNPu4DWXOkNI5UAqNbIOSPZ/kY0656rXITmTKgbJm1QRqBwBs65fsubE3iST3yUBsKtGxFx1OXyd56ICZVe5CJ5b1gHZ50WOT/ZH4rGlmYRMCeTPUrWSJFLOG7/P+h8Axu1PnlsqZdK6muuJa0Q2t7Usy6S0cR2SpJK8TkxMmEahyWQSQ0NDOHXqFPr7+zEyMoKJiYm5HwSF4iLA4XBgxYoVhrCXy2WcOXMGvb29yGQySnL+A21tbbj88ssRjUbNJgk3ThRKdBQKheIVgyTjueeew6FDh0zguWXLFqxbtw5r1qxBe3u7aa7IPiWpVMqoNUx9Y+0IcDZdg6oOMNt3pVQqmcCXgbFMS6LrFskJCQuD3JmZGdMrplgswuVyIRQKGaetcrmMZDJpajxYM0PrYfYDymQyRvGg8iTrdUiSSAa9Xm9V6hvT/GRdDEkUHc1YPyM/J00PpNudPAdrjFhv4vV6Tf0UwSJ71gGx3oTXK8mOy+VCPp83io+szWGz1VgshkKhgFQqZcgF75Ucs0x5k05pTG+kKsP5tizL1FK5XC6Ew2E0NDSY62ONDmvFSEJbWlrg8XiwfPlyhEIh436Xz+eRy+XQ19eHgYEB/Pu//zuGh4dx9OjRqjXD+6pQ1BJ1dXW4/fbb8cY3vhEdHR3I5/P4yU9+gsOHD6taIXD11VfjPe95D9xut5K/OaBER6FQKF4lpHoBAMPDw8bOeGRkxOy6r1y5EuFw2AS8bNhIBYTF7rK/DNUAkgF7ypKs12FQ7XA4zJiY8mFXc6gC2R3dqEbQ9ED2o6GaxECcxgdSjZBBPcciVSGqNrwuu9UzyRpfkwYEQHUdDMcpVRl7/xrOL8mjdEpjUM8/PK8M9CVBoclCNputGjvPJc0h+F2+x3Q4Kl6y9ofzIs9FlUgqeXT7I7kLBoNwOp2Ix+NoaGgwJJhkk6YTfX19KBaLmJycRC6Xw/DwMIaHh9HX14epqSmkUiklNopLDg7H2ca5DQ0NcLvdyGaz6O3tRU9Pjwb0gOl55fP54PV60dvbi+HhYSWBNijRUSgUiguMkydPoqenpyqdzO124+1vfzs6OzuxbNky43ZFJaZUKqGurs7s0suUK9af+Hw++Hw+o6KwRw7rZ6hCOJ1O0ymcwTkNERigUxEhQWODSLqssW6Dyg9To1wul3GVSyQSZqy0JHY6nUgmk1V9XKj+8BihUKiqjgaAuZZIJAKv14tkMolSqYRIJFJl/8y6HipiTP8jsSQx4/VQ7amrq8PU1BQKhYJRr1gvQ2ITCoXMXE1PTxviyF5D9fX1yGazmJqaOodYuVwuM3apwJH4kMTyPo2NjcGyLMTjcWMKQCImCR4w26uHig8JWnNzM7xeL1auXImmpiYzN1QMx8fHMTExgZ/85CcYHBxEd3e3GYcki0pyFJcqYrEYmpqazHP4xz/+Ec8+++yi75sDnHVyjEajpvbzgQcewO9+9zucOnWq1kO7pKBER6FQKC4w7DUhwNlgtbe3F/l8HsPDw/D5fKbT9/Lly43FMABTjM/CegBVaWmyp409NYrBKwNl2W9F1sqwXkOaFTAFjp+1qz32miCqOfa6GxIIjk+el8eR5yLJsSsyMgC3B+W8bhKoUqlUVVNkd7iTDmlUpOT1k/zJehem4nFueJ1UfgqFQhWZlbU4HIvdLUoaO3Bd8J5T1fF4PIjFYmacrKmJxWIm7c7tdhsyl8vlMDY2hsHBQSSTSQwPDyOXyyGZTBpFh/U46lKlmA9Yu3Ytli5dioaGBlQqFRw/fhz9/f1IpVJKcv4Dl112Ga688kp0dHQAAEZHR9HT02M2uRRnoURHoVAoLgLK5TKeeuopANUNIWOxGP7Tf/pPaGlpQWtrKzweD4LBoAmai8Ui0um0Sb8qFArGOY2KBYvk2TeGvWyY0iVT0Kg+xGIxo2rwPSpC0ipaFtHLNDUA5lys8WGgnk6nTW8bpr2xKJ/qBOuAcrkc0um0UY2o/shGnzxXuVw2BE72HmLTzEqlYmpxeB32oIiqiqyjkecAqtUtYDZVjSSHhDOZTJraJXmfZQ8d3k/eA85NJBIxLmmWZZleN8DZGiKqfXSJIwHiuaQxxvDwMLLZLB555BEcP34cR48eNekrksgpFPMBTqcTt9xyC970pjdh5cqVmJ6exgMPPIDnnntODTIE3vzmN+MTn/iEUflPnTqFvXv36rNugxIdhUKhuEiwO24BZ+2eDx06hIGBAdPfxOv1IhAIoLm52QS7bLxprzehskFCYk9ho9JBgkCSRcWDqgbTt0icWNjKInySFtbESDXE7vYmiRdVEHleWbNDJYh/k3xJVzE5Zyzi93g8VaYMdle6UqkEr9db1XeGhIzv87M8h72+h+/zuCRhrJWSf2ikwHORKJHc8Vr9fj8qlYqxtma6H5Uxn8+HQCCApqYmc/35fB6JRMLsZg8NDeH06dOGEOZyORSLRRw5csSoObrrrZjPaGlpwYoVK0zz2p6eHhw7dswYgChmf5+x19Xk5KSSnDmgREehUChqiFwuh9/97nfnvN7S0oKtW7eivb0d69evN+lMrClh0M1Am4G7tDgGZlUKFq5T5bETD9aGMC1K1tcEAgETcNBhDahOC5Nkh71hGLzz81RZpHkDSQuPMT09jVwuZ0iXnYTwnD6fzzi7MVVQEhIGROFw2KSj+f1+Q+joCMfzsgaARA6AIVxMtaN6RGImXcr8fj+CwaC5Njq68Zo4Bo4nEAjA6/ViyZIlqKurQyqVMsYCfr8fTU1NhsSUSiUkEgkkEgmMjo5ix44deOKJJy7UElQoLik4HA4sWbIEGzZsAACkUikcOnQIzzzzTI1HdmmBqa0nTpzAvn37MDIyUushXZJQoqNQKBSXILLZLLq7uzE6OoqhoSH4/X6EQiET0MdiMeNIFA6HEQwG4Xa7EY1Gq1LYmE4Wj8fhcDiQy+UMmaB7l0xvsve8AWYd0Xhu1qZIO2kG9LL3D8EaFwBznkf2CSI5oprC4n7aQJPcyaJ6YJZsSYImm5jyGFR5OC9Uu+RnZdqcPD7rpmjGwCawsVjM1OJwvJwTqjLFYhHlchn19fXweDyIRCImRU425BweHjZ1XCSW2WwWExMTKBQKyGQy6O/vvwgrUKG4+Fi7di06OzuxZMkSWJZl1O5kMlnroV0yWLduHTZv3oz169cDAI4dO4bHH38cw8PDNR7ZpQklOgqFQnEJIpPJ4PDhwy/4/mWXXYauri5s2LABK1asQGNjI2KxmOlRMzU1hXw+b4hMY2MjHA4HpqamTKE63cns9TgAqhQaSXSohgCzBfRULpgmJ/veMN1N9teRpAmYVZVYdC/H4ff7TV0MMKuysE5JQipAHBuvRaa7yZQwusNJS+i55oDmCRwvSWMwGDSuUEw98/l88Pv9pm6oUqkgkUigVCphyZIliEQiiMVicDgcOHPmjCFwyWQShw4dQk9PDx588EFj0qBQLBZs2bIFb3rTm7Bs2TKUy2Xs27cP+/fvx+TkZK2Hdslg8+bNuOOOO7BixQoAwIEDB/DQQw/VdlCXMJToKBQKxTzE+Pg4yuUyJicn8dxzzyESiZhaHjaX9Hg8Ru1pa2szhgGlUskE7W632zTaDAQCaGhoQCaTMfUglUrFBO12owBJQGSNDAN0kqhcLmfsp2UvG0mMmCJGQlIoFAwpkv1+SIJIruykio5zdCWj0iTrfgBUNeekpTcVGdnY0+l0IhQKmf43Ho8HLS0tyOfzpglsU1OTUYt4bM4NlZzR0VFMTk7iwIEDVXOQTqcxPT2NfD6PQqGA8fFxJBKJqoaxCsViwYoVK3DNNdcYt7UDBw7gD3/4gyo6ArFYDCtWrEClUkFPTw/S6XSth3RJQ4mOQqFQzENMTk5icnISJ0+ePOc9l8uFtWvXor6+HuvXr0dzczNcLhf8fr/pN+P3+6saUBaLRfh8PkOQqLDQIlmmqZHISNWEREXW+/j9fqO+1NXVVTmGyc8Bs0SJChHrYUh+pKU2r1GSHKa/ATDGADzXzMwMCoWCIT+SEAHA1NQUpqen0dTUZIweCK/Xi9bWVgBAOp2G3+9HW1sbCoUC/H6/SV1jjU6hUKgqmOZcjY2NYWBgAPv27cPo6CiGh4e16aFCIeBwOLBs2TJs3rwZwFn79qNHj+Lpp5+u8cguLYTDYSxduhS9vb0YGBhAJpOp9ZAuaSjRUSgUigWGSqWCM2fOYHJyElNTU/D7/XjuueeMikPS4Ha7EYvFEIvF0NHRgVgshra2NlPH4/P5zGdCoRDS6TTy+XxVzxfgbHoZncP4PaoupVLJNBkluZqenq4yPJCpa1RGqMBQ1aHzGwkX3cpYZxQKhYwiQhtodg3PZrPGjICqCz/jcDjg9XoxMzOD1atXIxgMGjWLCpnP56uqecrlcpiamsLJkyfNuJPJJMbHx1EqlYzFNQCj3IyMjCCdTmNsbMykzCkUirNYsWIFli9fjra2NgDA0aNHMTg4iPHx8RqP7NLBqlWrsG3bNlx11VUAgF27duGXv/wl9u/fX9uBXeJQoqNQKBQLDJZlYWpqCgAwNDT0gp/z+Xxobm5GR0eHKZgPBAJVTS39fj8aGhoQi8XgdruRzWYRjUar0ssikQicTicymQxKpZJJNXO5XCgUCigWi6ZY37IsU0dEouFyuZDP5w35IKFhCplMM2OjTxoClMtl1NXVIRwOn2Pl7PP5EIlEAFS7x7HGhqSJx1myZAnC4TDGxsZQLperyJSsdyLRGRwcRDqdRiKRQE9PD44ePWrmTqFQvHy0trZi06ZNaGxshGVZ6O/vx6FDhzQtS2DZsmXYvn07Vq1aBQA4fPgw7r///toOah5AiY5CoVAsUkxPT2NiYgL5fB7j4+Pw+XwIhUJV9TJUQjwej6k3iUaj8Pv9aGxsRCAQQGtrq1FKABjyQqWntbXVpK6xUSh7QLCIPx6PI5PJoLGxscpdjultskZG2mRTZQkGg8hms4b4BINBRCIRNDU1IZlMGnIlm5ImEgkUCgUMDAwgm83i2LFjAM7uJmezWXM9PA9NGJxOp+ltQwOFTCZjiJ9CoTg/bNmyBe9///uxfPlyWJaFXbt24cEHH8Tg4GCth3bJoLW1Fa973evgdrtNk2DFS0OJjkKhUCxSlMtlZLNZZLNZjI2NvezvsSalq6sLsVgMqVTKNMCsq6sz/wZgDBFYy0OzAaaVsUEp++aEQiFT/E9nNPaxoZLDehzp6Obz+VAul02/IJIiv9+PYrFoapNYG1QsFjE1NYV0Oo2BgQEkEgljnb1//34kk0mTPqdQKF5bLFu2DFdffTWAs3b03d3d2Lt3b41HdWmAv/tisRg6OzsxMTFhGgMrXhpKdBQKhUJxXshkMsjn8ygWi3C73Th48GBV3xgqLkxNY/8ZCao+VHTYaJNuZfw8Hd74ORImAKaGh+ek6QB/Zn0NiQ3BpqHFYtEoNXQ5q1QqSCaT6nqmUFwE8HcFn+upqSkkEok57eMXK9ra2rBlyxasW7cODocDjz32GP7pn/5pTiMaxblQoqNQKBSK8wJJiSQPCoVCcb7wer1VCnA6ncbo6GiVc+FiRyQSwdq1a9He3g4A6O/vxxNPPFHjUc0fKNFRKBQKhUKhUFx03HDDDbj99ttx5ZVXAgAeffRRPPzwwy/aLHmxoaurCx/60IcQj8drPZR5CSU6CoVCoVAoFIqLjtbWVlxzzTVobm4GAJw5cwYHDx5EIpGo7cAuAbCeMR6Po6urCzMzMxgbG9O+OecJJToKhUKhUCgUiosOFtizp9bExAR6e3vVoh1AY2MjrrrqKmzcuBFOpxOPPPIIvvKVr6gT3XlCiY5CoVAoFAqF4qKBVvZ0cMzn80ilUkilUlqf8x8IBAJYs2aNaaI6MTGB/fv3o1Qq1Xhk8wtKdBQKhUKhUCgUFw2bN2/GO97xDlx33XUAgKeffhq7d+/W2hyBzs5OfOxjH0MsFjOukvl8Xnt1nSecL/0RhUKhUCgUCoXiwiAWi2HNmjVoamoCAIyNjeHo0aNam/MfYM+w5uZmuFwudHd3Y2RkREnOK4AqOgqFQqFQKBSKi4b29nZs374dXq8XAHDo0CHce++9alkPwOVyIRgMwufzweFw4KmnnsIXv/hFnD59utZDm5dQoqNQKBQKhUKhuGjweDwIBoOYnp5GOp1GOp1WN7H/QCgUwpYtW3DZZZfB6XQim82iv78fyWSy1kObl1Cio1AoFAqFQqG4KHA4HHA4HACA8fFx9Pb2YmxsrMajunSwdu1afPe730VjYyM8Hg8KhQLOnDmjTnSvEEp0FAqFQqFQKBSvOerr69HZ2Ylly5bB4XBgbGwMBw4cwOjoaK2HVnM4HA54PB4EAgHU19ejUqngueeew6lTp1Aul2FZVq2HOC+hREehUCgUCoVC8Zpj/fr1+PjHP46VK1cCAPbv349vfetbGBoaqvHIao+6ujrE43FEo1E4nU50d3fj7rvvxsmTJ9WE4FVAiY5CoVAoFAqF4jVHMBhER0cHGhoaAAC5XA7j4+PI5XI1HlntEQ6HsX37dmzcuBFutxu5XA59fX0YHx9XNedVQImOQqFQKBQKheI1RywWw6ZNm+B2uwEA6XQag4ODNR7VpYH29nZ86UtfQltbG1wuFzKZDJ5//nnk8/laD21eQ4mOQqFQKBQKheI1g8/nQ0NDA+LxOJxOJ6ampnD69GkMDw/XemiXDJxOJ+rq6pBKpfDHP/4RTz/9NGZmZlTNeZVQoqNQKBQKhUKheM0QCoWwbt06dHR0AAAGBwfx2GOP4cSJEzUe2aWH06dP4xvf+Ab6+vowMzNT6+HMeyjRUSgUCoVCoVC8ZmhsbMSb3vQmbNiwAU6nE2fOnMHOnTtx6tSpWg+t5ohEIrjtttuwbt06BINBnD59GoODg1qbc4GgREehUCgUCoVC8ZqhtbUV73jHO9DQ0ACHw4G+vj7827/9mwbyAOLxOP7H//gfWLlyJfx+P/L5PHp7e7VB6AWCEh2FQqFQKBQKxWuGuro6RKNReL1e5HI5FIvFWg+p5nA6nYjH42htbYXP50M6nca9996LgwcPolQq1Xp4CwZKdBQKhUKhUCgUrxncbrfpD5NOpzWQB+ByudDY2Ijm5mZDdH72s5+ht7dX5+cCQomOQqFQKBQKheKCIxgMoqurC11dXaY25/e//z0OHDiw6NPWQqEQ/vqv/xqXX345YrEY+vv70d/fj6GhIW0QegGhREehUCgUCoVCccHh9/vR1dWFpUuXwul0Ynx8HLt378bJkydrPbSaIxAI4C1veQs2bNiASqWCUqmE0dFRTExM1HpoCwpKdBQKhUKhUCgUFxwtLS14//vfj+XLl8PlcmFsbAw7d+7E5ORkrYdWM7jdbmzduhWrVq1CJBLB2NgYvv71r+PYsWNqQPAaQImOQqFQKBQKheKCIxwO47rrrjONQtPpNLq7uxd1fxiXy4U1a9Zg3bp1pjbnV7/6FU6dOoVCoVDr4S04KNFRKBQKhUKhUFxwOJ1OeDweuFwuTE9PY3p6utZDqjk8Hg+2b9+OLVu2oFQqYWxszDjRLfa6pdcCzloPQKFQKBQKhUKxsOByuVBXVweXywUAKBQKi1rJAc6SnGAwiM7OTnR1dSGXy2FsbAylUgnlcrnWw1uQUEVHoVAoFAqFQnHBEAwGsXnzZmzatAkulwunT5/GI488gqeeemrROoq53W685z3vwcaNG7FkyRIkEgl88YtfxKFDhzA2Nlbr4S1YKNFRKBQKhUKhUFwweDwedHR0oL29HS6XC5lMBkeOHMHAwMCiTc9yuVxYvXo1rrjiCrjdbqRSKTz77LM4fPhwrYe2oKFER6FQKBQKhUJxwRCPx3H77bejs7MTHo8Hw8PDuO+++zA1NbUoiU5dXR18Ph82bNiAzZs346mnnsKpU6eQSqVqPbQFDyU6CoVCoVAoFIoLBrfbjWXLlqG1tRUOhwPFYhEjIyPI5/O1HlpN0NjYaP6EQiEMDg6iu7sbxWKx1kNb8FCio1AoFAqFQqG4IHA6nfB6vWhubkY8HkelUlnUhfZ1dXX49Kc/jbe85S1YsmQJSqUS/u3f/g1PP/00EolErYe34KFER6FQKBQKhULxquFyudDY2Ijm5mb4fD4AQCKRQDqdXpQpa6FQCOFwGCtXrsSqVaswMjKC4eFhDA0NYWJiotbDWxRQoqNQKBQKhUKheNWIxWL4L//lv2Dt2rUIBoOYmJjAQw89hH379i1KVed1r3sdtmzZghUrVqBUKuF//a//hSeffBKnTp2q9dAWDbSPjkKhUCgUCoXiVcPtdmPFihXo6OiAy+VCoVDAyZMncfr06UWn6DgcDrS2tmLNmjWoq6tDIpHA4OAg+vr6UCgUaj28RQNVdBQKhUKhUCgUrxo+nw9bt25FV1cXPB4PxsbG8Mtf/hJjY2OLslnotddei3e/+904ceIE9u3bh1OnTuHMmTOLtpdQLaBER6FQKBQKhULxiuFyudDc3IylS5ciFArB6/XC4XCgXC4jk8kgl8vVeogXFcuWLUN7ezva29vh8/nQ29uLAwcOYHJyUknORYYSHYVCoVAoFArFK4bP58PNN9+MtWvXIhqNwuVyAQBmZmaQyWQWna30O9/5Trzvfe/D8uXLMTMzg5/97Ge47777UCqVaj20RQclOgqFQqFQKBSKVwy3242uri6sWrUKHo8HAExNzmKqzYnH42hsbERHRwdaW1vR29uLiYkJDAwMaF1OjaBER6FQKBQKhULxiuHz+XDDDTdgw4YNCAQCAIByubzonNbWrl2L66+/Htdccw2WLFmCf/qnf8LPf/5znDlzptZDW7RQoqNQKBQKhUKheMVwOBzw+Xzw+XxwOByYnp7G8PAwxsbGFkVNitPphMvlwtKlS3HVVVfB5XLhxIkTOH36NCYnJzVlrYZQe2mFQqFQKBQKxSuGnejk83kcPnwYJ0+eXBSqjsvlgs/nw8aNG/Gud70LDocDTzzxBI4fP46xsTEUi8VaD3HRQhUdhUKhUCgUCsUrQl1dHdxud9Vr2WwWf/zjH3H06NFFQXQ2btyIG2+8Eddccw0cDgdOnjyJJ554AqdPn6710BY9lOgoFAqFQqFQKM4bDocDXq8XPp/POK0BQCqVwqOPPor+/n5MT0/XcIQXB3/6p3+Ku+++2/y8f/9+/Ou//msNR6QglOgoFAqFQqFQKM4bbrcb119/PdasWYNwOGxeL5fLmJiYQCKRWNCuax0dHbjqqqtwxRVXAAB27dqFHTt2YM+ePbUdmMJAiY5CoVAoFAqF4rzh8Xhw44034oorrkAsFjOvz8zMGKKzkNHZ2Yn3vve9WL16NQBgx44d+NznPlfjUSkklOgoFAqFQqFQKM4bLpcLK1aswOrVq+H1egEAlUplwTut1dfX47LLLsM111yDTZs2YWxsDD/96U/x/PPP13poChvUdU2hUCgUCoVCcd5wOp1YsmQJOjo6jCFBpVJBuVxe0Clr0WgUmzdvxoYNG7B69Wrk83k89NBDOHHiRK2HprBBFR2FQqFQKBQKxatGoVDAgQMHcPDgwQVpQuB0OuHz+bBq1Sq8973vhdPpxOOPP46dO3di9+7dmJycrPUQFTaooqNQKBQKhUKheNWYnp5GT08P+vr6MDMzU+vhXHC4XC4Eg0G0t7dj27ZtaGxsxIEDB3Do0CF0d3cr0bkEoYqOQqFQKBQKheJVI5fLYdeuXTh+/DhKpVKth3PBsWbNGnz2s59Fa2srRkdH8cwzz+AHP/gBxsbGaj00xQtAiY5CoVAoFAqF4rzgdDqreucAZxWd3t7eBafosF/QkiVLcOutt6JcLuPUqVPo6+vD888/v+DNF+YzlOgoFAqFQqFQKF42nE4nLr/8cnR0dCAUCpnXp6en0dfXh4GBAZTL5RqO8MKis7MTn/70p7Fq1Sr4fD7s2bMHn/vc5zA4OKgk5xKHEh2FQqFQKBQKxcuGw+FAY2Mj2tvbja00cNZxLZPJIJvN1nB0Fw5OpxN+vx/Nzc24/vrr0dDQgHQ6jd7eXuzevXtBpuctNCjRUSgUCoVCoVC8bDidTqxcuRIbN25EMBis9XBeMzQ1NeHOO+/EZZddhvb2dhw5cgSf/vSncebMmQXpKrcQoURHoVAoFAqFQvGy4XA4EI/H0dTUZPrnWJa1YJqFOp1OhMNhtLS0YNOmTViyZAmSyST6+/vx9NNPLxjFajFAiY5CoVAoFAqF4mXD6XRi9erVuOKKKxAMBmFZFtLpNDKZzIJoFBqNRvGxj30Ml112GbZt24aBgQG8973vxeDgIPL5fK2HpzgPzEuisxAeIoVCoVAoFIr5DMuykM1mUSqVkEqlkEgkFoyiE4/HUV9fDwDIZDI4fPiw9sm5xPBy+IDDmoesYXBwEMuWLav1MBQKhUKhUCgUCkUNMDAwgKVLl77oZ+Yl0alUKjh27BjWrVuHgYEBRCKRWg9p0SGVSmHZsmU6/zWE3oPaQue/ttD5ry10/msPvQe1hc5/7cBUyfb2djidzhf97LxMXXM6nViyZAkAIBKJ6AKrIXT+aw+9B7WFzn9tofNfW+j81x56D2oLnf/aIBqNvqzPvTgNUigUCoVCoVAoFIp5CCU6CoVCoVAoFAqFYsFh3hIdr9eLL3zhC1UdeRUXDzr/tYfeg9pC57+20PmvLXT+aw+9B7WFzv/8wLw0I1AoFAqFQqFQKBSKF8O8VXQUCoVCoVAoFAqF4oWgREehUCgUCoVCoVAsOCjRUSgUCoVCoVAoFAsOSnQUCoVCoVAoFArFgsO8JTrf/OY3sWLFCvh8PmzduhVPP/10rYe0IPH3f//3cDgcVX/Wrl1r3i8UCrjzzjvR0NCAUCiEd73rXRgZGanhiOc3fve73+Ftb3sb2tvb4XA4cP/991e9b1kWPv/5z6OtrQ1+vx/bt2/HiRMnqj4zOTmJ973vfYhEIojFYvjgBz+ITCZzEa9i/uKl5v+v/uqvznke3vzmN1d9Ruf/leOuu+7CNddcg3A4jObmZvz5n/85jh07VvWZl/M7p7+/H7feeisCgQCam5vxyU9+EjMzMxfzUuYlXs7833DDDec8Ax/5yEeqPqPz/8pwzz33YNOmTaYB5bZt2/DQQw+Z93Xtv/Z4qXug63/+YV4SnZ/97Gf4+Mc/ji984QvYt28fNm/ejJtvvhmjo6O1HtqCxPr16zE0NGT+7Nq1y7z3t3/7t/j1r3+Nn//859i5cyfOnDmDd77znTUc7fxGNpvF5s2b8c1vfnPO9++++2584xvfwLe+9S089dRTCAaDuPnmm1EoFMxn3ve+9+HQoUN49NFH8cADD+B3v/sdPvzhD1+sS5jXeKn5B4A3v/nNVc/DT37yk6r3df5fOXbu3Ik777wTe/bswaOPPorp6WncdNNNyGaz5jMv9TunXC7j1ltvRalUwu7du/Ev//Iv+P73v4/Pf/7ztbikeYWXM/8A8KEPfajqGbj77rvNezr/rxxLly7Fl7/8ZezduxfPPPMM3vSmN+G2227DoUOHAOjavxh4qXsA6Pqfd7DmIa699lrrzjvvND+Xy2Wrvb3duuuuu2o4qoWJL3zhC9bmzZvnfC+RSFhut9v6+c9/bl47cuSIBcB68sknL9IIFy4AWPfdd5/5uVKpWK2trdZXvvIV81oikbC8Xq/1k5/8xLIsyzp8+LAFwPrjH/9oPvPQQw9ZDofDOn369EUb+0KAff4ty7I+8IEPWLfddtsLfkfn/8JidHTUAmDt3LnTsqyX9zvnwQcftJxOpzU8PGw+c88991iRSMQqFosX9wLmOezzb1mW9YY3vMH6b//tv73gd3T+Lyzq6+ut73znO7r2awjeA8vS9T8fMe8UnVKphL1792L79u3mNafTie3bt+PJJ5+s4cgWLk6cOIH29nasXLkS73vf+9Df3w8A2Lt3L6anp6vuxdq1a9HR0aH34jVAT08PhoeHq+Y7Go1i69atZr6ffPJJxGIxXH311eYz27dvh9PpxFNPPXXRx7wQsWPHDjQ3N2PNmjX46Ec/iomJCfOezv+FRTKZBADE43EAL+93zpNPPomNGzeipaXFfObmm29GKpWq2pVVvDTs80/86Ec/QmNjIzZs2IDPfOYzyOVy5j2d/wuDcrmMn/70p8hms9i2bZuu/RrAfg8IXf/zC3W1HsD5Ynx8HOVyuWoRAUBLSwuOHj1ao1EtXGzduhXf//73sWbNGgwNDeEf/uEf8PrXvx4HDx7E8PAwPB4PYrFY1XdaWlowPDxcmwEvYHBO51r7fG94eBjNzc1V79fV1SEej+s9uQB485vfjHe+853o7OzEyZMn8Xd/93e45ZZb8OSTT8Llcun8X0BUKhV87GMfw5/+6Z9iw4YNAPCyfucMDw/P+YzwPcXLw1zzDwB/8Rd/geXLl6O9vR3PP/88PvWpT+HYsWO49957Aej8v1ocOHAA27ZtQ6FQQCgUwn333Yd169Zh//79uvYvEl7oHgC6/ucj5h3RUVxc3HLLLebfmzZtwtatW7F8+XL867/+K/x+fw1HplBcfLznPe8x/964cSM2bdqErq4u7NixAzfeeGMNR7bwcOedd+LgwYNVNYGKi4cXmn9Zb7Zx40a0tbXhxhtvxMmTJ9HV1XWxh7ngsGbNGuzfvx/JZBK/+MUv8IEPfAA7d+6s9bAWFV7oHqxbt07X/zzEvEtda2xshMvlOsdpZGRkBK2trTUa1eJBLBbDZZddhu7ubrS2tqJUKiGRSFR9Ru/FawPO6Yut/dbW1nNMOWZmZjA5Oan35DXAypUr0djYiO7ubgA6/xcKf/M3f4MHHngATzzxBJYuXWpefzm/c1pbW+d8Rvie4qXxQvM/F7Zu3QoAVc+Azv8rh8fjwapVq3DVVVfhrrvuwubNm/H1r39d1/5FxAvdg7mg6//Sx7wjOh6PB1dddRV++9vfmtcqlQp++9vfVuVQKl4bZDIZnDx5Em1tbbjqqqvgdrur7sWxY8fQ39+v9+I1QGdnJ1pbW6vmO5VK4amnnjLzvW3bNiQSCezdu9d85vHHH0elUjG/kBUXDoODg5iYmEBbWxsAnf9XC8uy8Dd/8ze477778Pjjj6Ozs7Pq/ZfzO2fbtm04cOBAFeF89NFHEYlETPqJYm681PzPhf379wNA1TOg83/hUKlUUCwWde3XELwHc0HX/zxArd0QXgl++tOfWl6v1/r+979vHT582Prwhz9sxWKxKpcLxYXBJz7xCWvHjh1WT0+P9Yc//MHavn271djYaI2OjlqWZVkf+chHrI6ODuvxxx+3nnnmGWvbtm3Wtm3bajzq+Yt0Om09++yz1rPPPmsBsL761a9azz77rNXX12dZlmV9+ctftmKxmPWrX/3Kev75563bbrvN6uzstPL5vDnGm9/8ZmvLli3WU089Ze3atctavXq19d73vrdWlzSv8GLzn06nrf/+3/+79eSTT1o9PT3WY489Zl155ZXW6tWrrUKhYI6h8//K8dGPftSKRqPWjh07rKGhIfMnl8uZz7zU75yZmRlrw4YN1k033WTt37/fevjhh62mpibrM5/5TC0uaV7hpea/u7vb+uIXv2g988wzVk9Pj/WrX/3KWrlypXX99debY+j8v3J8+tOftnbu3Gn19PRYzz//vPXpT3/acjgc1m9+8xvLsnTtXwy82D3Q9T8/MS+JjmVZ1v/+3//b6ujosDwej3Xttddae/bsqfWQFiRuv/12q62tzfJ4PNaSJUus22+/3eru7jbv5/N564477rDq6+utQCBgveMd77CGhoZqOOL5jSeeeMICcM6fD3zgA5ZlnbWY/tznPme1tLRYXq/XuvHGG61jx45VHWNiYsJ673vfa4VCISsSiVh//dd/baXT6RpczfzDi81/LpezbrrpJqupqclyu93W8uXLrQ996EPnbLDo/L9yzDX3AKzvfe975jMv53dOb2+vdcstt1h+v99qbGy0PvGJT1jT09MX+WrmH15q/vv7+63rr7/eisfjltfrtVatWmV98pOftJLJZNVxdP5fGf7zf/7P1vLlyy2Px2M1NTVZN954oyE5lqVr/2Lgxe6Brv/5CYdlWdbF048UCoVCoVAoFAqF4rXHvKvRUSgUCoVCoVAoFIqXghIdhUKhUCgUCoVCseCgREehUCgUCoVCoVAsOCjRUSgUCoVCoVAoFAsOSnQUCoVCoVAoFArFgoMSHYVCoVAoFAqFQrHgoERHoVAoFAqFQqFQLDgo0VEoFAqFQqFQKBQLDkp0FAqFQqFQKBQKxYKDEh2FQqFQKBQKhUKx4KBER6FQKBQKhUKhUCw4KNFRKBQKhUKhUCgUCw7/PxkCLnqgyIG+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(400, 400, 310)\n", "(178, 146, 310)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/82 [00:07<?, ?it/s]\n"]}], "source": ["import numpy as np\n", "from glob import glob\n", "from tqdm import tqdm\n", "import h5py\n", "import nrrd\n", "import nibabel as nib\n", "import pandas as pd\n", "# import xlrd\n", "import pdb\n", "import SimpleITK as sitk\n", "from skimage import transform, measure\n", "import os\n", "import pydicom\n", "import matplotlib.pyplot as plt\n", "\n", "output_size = [96, 96, 96]\n", "\n", "\n", "def ImageResample(sitk_image, new_spacing=[1, 1, 1], is_label=False):\n", "    '''\n", "    sitk_image:\n", "    new_spacing: x,y,z\n", "    is_label: if True, using Interpolator `sitk.sitkNearestNeighbor`\n", "    '''\n", "    size = np.array(sitk_image.GetSize())\n", "    spacing = np.array(sitk_image.GetSpacing())\n", "    new_spacing = np.array(new_spacing)\n", "    new_size = size * spacing / new_spacing\n", "    new_spacing_refine = size * spacing / new_size\n", "    new_spacing_refine = [float(s) for s in new_spacing_refine]\n", "    new_size = [int(s) for s in new_size]\n", "    if not is_label:\n", "        print(size)\n", "        print(new_size)\n", "        print(spacing)\n", "        print(new_spacing_refine)\n", "    resample = sitk.ResampleImageFilter()\n", "    resample.SetOutputDirection(sitk_image.GetDirection())\n", "    resample.SetOutputOrigin(sitk_image.GetOrigin())\n", "    resample.SetSize(new_size)\n", "    resample.SetOutputSpacing(new_spacing_refine)\n", "\n", "    if is_label:\n", "        resample.SetInterpolator(sitk.sitkNearestNeighbor)\n", "    else:\n", "        resample.SetInterpolator(sitk.sitkBSpline)\n", "    newimage = resample.Execute(sitk_image)\n", "    return newimage\n", "\n", "\n", "def set_window_wl_ww(tensor):\n", "    # sl_window = [75,400]\n", "    sl_window = [-125, 275]\n", "    [wl, ww] = sl_window\n", "    w_min, w_max = wl - ww // 2, wl + ww // 2\n", "    tensor[tensor < w_min] = w_min\n", "    tensor[tensor > w_max] = w_max\n", "    tensor = (tensor - w_min) / (w_max - w_min)\n", "    ### min max Normalization\n", "    return tensor\n", "\n", "\n", "# 根据标签图像（label）中的非零区域来裁剪图像（image）和标签本身\n", "def crop_roi(image, label):\n", "    assert (image.shape == label.shape)\n", "    print(image.shape)\n", "    ### crop based on lung segmentation\n", "    w, h, d = label.shape\n", "\n", "    tempL = np.nonzero(label)\n", "    minx, maxx = np.min(tempL[0]), np.max(tempL[0])\n", "    miny, maxy = np.min(tempL[1]), np.max(tempL[1])\n", "\n", "    px = max(output_size[0] - (maxx - minx), 0) // 2  # DCB: 扩展边界，防止连预设的96 96 96 都不够？\n", "    py = max(output_size[1] - (maxy - miny), 0) // 2\n", "    minx = max(minx - px - 25, 0)  # np.random.randint(5, 10)\n", "    maxx = min(maxx + px + 25, w)  # np.random.randint(5, 10)\n", "    miny = max(miny - py - 25, 0)\n", "    maxy = min(maxy + py + 25, h)\n", "\n", "    image = image[minx:maxx, miny:maxy, :].astype(np.float32)\n", "    label = label[minx:maxx, miny:maxy, :].astype(np.float32)\n", "    return image, label\n", "\n", "\n", "listt = glob('Dataset090_Pancreas-CT/imagesTr/*')\n", "\n", "for item in tqdm(listt):\n", "    name_image = str(item)\n", "    name_label = name_image.replace('imagesTr', 'labelsTr')\n", "    # pdb.set_trace\n", "    itk_img = sitk.ReadImage(name_image)\n", "    # origin =itk_img.GetOrigin()\n", "    # direction = itk_img.GetDirection()\n", "    # space = itk_img.GetSpacing()\n", "    itk_img = ImageResample(itk_img)\n", "    image = sitk.GetArrayFromImage(itk_img)\n", "    image = np.transpose(image, (2, 1, 0))\n", "\n", "    itk_label = sitk.ReadImage(name_label)\n", "    itk_label = ImageResample(itk_label, is_label=True)\n", "    label = sitk.GetArrayFromImage(itk_label)\n", "    label = np.transpose(label, (2, 1, 0))\n", "\n", "    assert (np.max(label) == 1 and np.min(label) == 0)\n", "    assert (np.unique(label).shape[0] == 2)\n", "    assert (np.shape(label) == np.shape(image))\n", "    image = set_window_wl_ww(image)\n", "\n", "    print(image.shape)\n", "    plt.figure(figsize=(10, 10))\n", "    plt.title('CT Slice_enhanced_100')\n", "    plt.imshow(image[:, :, 150], cmap='gray')\n", "    plt.show()\n", "\n", "    image, label = crop_roi(image, label)\n", "    image = (image - np.mean(image)) / np.std(image)\n", "    print(image.shape)\n", "\n", "    # 叠加显示图像和标签\n", "    plt.figure(figsize=(10, 10))\n", "    plt.title('CT with Label Overlay')\n", "    plt.imshow(image[:, :, 150], cmap='gray')\n", "    plt.imshow(label[:, :, 150], cmap='jet', alpha=0.5)  # 设置透明度为0.5\n", "    plt.show()\n", "\n", "    break\n"]}], "metadata": {"kernelspec": {"display_name": "oneshot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}