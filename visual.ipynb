{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import h5py\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import os\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.family\"] = [\"SimHei\", \"WenQuanYi Micro Hei\", \"Heiti TC\"]\n", "\n", "# 读取H5文件\n", "def load_pancreas_data(file_path):\n", "    \"\"\"\n", "    加载Pancreas数据集的H5文件\n", "    \n", "    参数:\n", "    file_path (str): H5文件路径\n", "    \n", "    返回:\n", "    tuple: (image数据, label数据)\n", "    \"\"\"\n", "    try:\n", "        with h5py.File(file_path, 'r') as f:\n", "            image = f['image'][:]\n", "            label = f['label'][:]\n", "        return image, label\n", "    except Exception as e:\n", "        print(f\"加载文件出错: {e}\")\n", "        return None, None\n", "\n", "# 中心裁剪函数\n", "def center_crop(volume, crop_size):\n", "    \"\"\"\n", "    对3D体积进行中心裁剪\n", "    \n", "    参数:\n", "    volume (np.ndarray): 3D体积数据\n", "    crop_size (tuple): 裁剪尺寸，格式为(z, y, x)\n", "    \n", "    返回:\n", "    np.ndarray: 裁剪后的3D体积\n", "    \"\"\"\n", "    z, y, x = volume.shape\n", "    z_crop, y_crop, x_crop = crop_size\n", "    \n", "    start_z = max(0, z // 2 - z_crop // 2)\n", "    end_z = min(z, start_z + z_crop)\n", "    \n", "    start_y = max(0, y // 2 - y_crop // 2)\n", "    end_y = min(y, start_y + y_crop)\n", "    \n", "    start_x = max(0, x // 2 - x_crop // 2)\n", "    end_x = min(x, start_x + x_crop)\n", "    \n", "    return volume[start_z:end_z, start_y:end_y, start_x:end_x]\n", "\n", "# 可视化函数 - 无ipywidgets版本\n", "def visualize(image, label=None, num_slices=5):\n", "    \"\"\"\n", "    可视化3D医学图像的多个切片\n", "    \n", "    参数:\n", "    image (np.ndarray): 3D图像数据\n", "    label (np.n<PERSON>ray, optional): 3D标签数据\n", "    num_slices (int): 要显示的切片数量\n", "    \"\"\"\n", "    # 确定要显示的切片索引\n", "    slice_indices = np.linspace(0, image.shape[0]-1, num_slices, dtype=int)\n", "    \n", "    fig, axes = plt.subplots(num_slices, 2 if label is not None else 1, figsize=(10, 3*num_slices))\n", "    \n", "    for i, slice_idx in enumerate(slice_indices):\n", "        # 显示图像\n", "        if label is not None:\n", "            ax1, ax2 = axes[i]\n", "        else:\n", "            ax1 = axes[i]\n", "        \n", "        ax1.imshow(image[slice_idx], cmap='gray')\n", "        ax1.set_title(f'Image Slice {slice_idx}')\n", "        ax1.axis('off')\n", "        \n", "        # 如果有标签，显示标签\n", "        if label is not None:\n", "            ax2.imshow(label[slice_idx], cmap='viridis')\n", "            ax2.set_title(f'Label Slice {slice_idx}')\n", "            ax2.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始图像形状: (142, 203, 155)\n", "原始标签形状: (142, 203, 155)\n", "裁剪后标签形状: (80, 112, 112)\n", "裁剪后图像形状: (80, 112, 112)\n"]}], "source": ["file_path = \"Pancreas/Pancreas_data/PANCREAS_0005.h5\"\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(file_path):\n", "    print(f\"错误: 文件 {file_path} 不存在\")\n", "    return\n", "\n", "# 加载数据\n", "image, label = load_pancreas_data(file_path)\n", "\n", "if image is not None:\n", "    # 打印原始数据形状\n", "    print(f\"原始图像形状: {image.shape}\")\n", "    if label is not None:\n", "        print(f\"原始标签形状: {label.shape}\")\n", "    \n", "    # 中心裁剪\n", "    crop_size = (80, 112, 112)  # (z, y, x)\n", "    cropped_image = center_crop(image, crop_size)\n", "    \n", "    # 如果有标签，也进行裁剪\n", "    cropped_label = None\n", "    if label is not None:\n", "        cropped_label = center_crop(label, crop_size)\n", "        print(f\"裁剪后标签形状: {cropped_label.shape}\")\n", "    \n", "    print(f\"裁剪后图像形状: {cropped_image.shape}\")\n", "    \n", "    # 可视化裁剪后的结果\n", "    visualize(cropped_image, cropped_label)"]}], "metadata": {"kernelspec": {"display_name": "oneshot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 2}