import numpy as np
from glob import glob
from tqdm import tqdm
import h5py
import nrrd
import nibabel as nib
import pandas as pd
# import xlrd
import pdb
import SimpleITK as sitk
from skimage import transform, measure
import os
import pydicom
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap


def visualize_slice(image, label, slice_idx=None, save_path=None, title_prefix=""):
    """
    可视化CT切片和对应的标签

    Args:
        image: 3D图像数组 (x, y, z)
        label: 3D标签数组 (x, y, z)
        slice_idx: 要显示的切片索引，如果为None则自动选择中间切片
        save_path: 保存路径，如果为None则显示图像
        title_prefix: 标题前缀
    """
    if slice_idx is None:
        slice_idx = image.shape[2] // 2  # 选择中间切片

    # 确保切片索引在有效范围内
    slice_idx = max(0, min(slice_idx, image.shape[2] - 1))

    # 获取切片
    img_slice = image[:, :, slice_idx]
    label_slice = label[:, :, slice_idx]

    # 创建图形
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))

    # 显示原始CT切片
    axes[0].imshow(img_slice, cmap='gray', origin='lower')
    axes[0].set_title(f'{title_prefix}CT Slice {slice_idx}')
    axes[0].axis('off')

    # 显示标签
    # 创建自定义颜色映射
    unique_labels = np.unique(label_slice)
    colors = ['black', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta']
    cmap = ListedColormap(colors[:len(unique_labels)])

    im_label = axes[1].imshow(label_slice, cmap=cmap, origin='lower', vmin=0, vmax=len(unique_labels)-1)
    axes[1].set_title(f'{title_prefix}Label Slice {slice_idx}')
    axes[1].axis('off')

    # 添加标签颜色条
    cbar = plt.colorbar(im_label, ax=axes[1], shrink=0.8)
    cbar.set_ticks(range(len(unique_labels)))
    cbar.set_ticklabels([f'Label {int(l)}' for l in unique_labels])

    # 显示叠加图像
    axes[2].imshow(img_slice, cmap='gray', origin='lower')
    # 创建掩码，只显示非零标签
    masked_label = np.ma.masked_where(label_slice == 0, label_slice)
    axes[2].imshow(masked_label, cmap=cmap, alpha=0.5, origin='lower', vmin=0, vmax=len(unique_labels)-1)
    axes[2].set_title(f'{title_prefix}Overlay Slice {slice_idx}')
    axes[2].axis('off')

    # 添加统计信息
    total_pixels = img_slice.size
    label_stats = []
    for label_val in unique_labels:
        count = np.sum(label_slice == label_val)
        percentage = count / total_pixels * 100
        label_stats.append(f'Label {int(label_val)}: {count} ({percentage:.1f}%)')

    # 在图像下方添加统计信息
    stats_text = '\n'.join(label_stats)
    fig.text(0.5, 0.02, stats_text, ha='center', fontsize=10,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为统计信息留出空间

    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    else:
        plt.show()

    plt.close()


def visualize_multiple_slices(image, label, num_slices=5, save_path=None, title_prefix=""):
    """
    可视化多个CT切片和对应的标签

    Args:
        image: 3D图像数组 (x, y, z)
        label: 3D标签数组 (x, y, z)
        num_slices: 要显示的切片数量
        save_path: 保存路径
        title_prefix: 标题前缀
    """
    z_dim = image.shape[2]
    slice_indices = np.linspace(z_dim//4, 3*z_dim//4, num_slices, dtype=int)

    fig, axes = plt.subplots(3, num_slices, figsize=(3*num_slices, 9))

    # 创建自定义颜色映射
    unique_labels = np.unique(label)
    colors = ['black', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta']
    cmap = ListedColormap(colors[:len(unique_labels)])

    for i, slice_idx in enumerate(slice_indices):
        img_slice = image[:, :, slice_idx]
        label_slice = label[:, :, slice_idx]

        # CT切片
        axes[0, i].imshow(img_slice, cmap='gray', origin='lower')
        axes[0, i].set_title(f'CT Slice {slice_idx}')
        axes[0, i].axis('off')

        # 标签
        axes[1, i].imshow(label_slice, cmap=cmap, origin='lower', vmin=0, vmax=len(unique_labels)-1)
        axes[1, i].set_title(f'Label Slice {slice_idx}')
        axes[1, i].axis('off')

        # 叠加
        axes[2, i].imshow(img_slice, cmap='gray', origin='lower')
        masked_label = np.ma.masked_where(label_slice == 0, label_slice)
        axes[2, i].imshow(masked_label, cmap=cmap, alpha=0.5, origin='lower', vmin=0, vmax=len(unique_labels)-1)
        axes[2, i].set_title(f'Overlay Slice {slice_idx}')
        axes[2, i].axis('off')

    # 添加行标签
    axes[0, 0].set_ylabel('CT Image', rotation=90, fontsize=12, labelpad=20)
    axes[1, 0].set_ylabel('Label', rotation=90, fontsize=12, labelpad=20)
    axes[2, 0].set_ylabel('Overlay', rotation=90, fontsize=12, labelpad=20)

    plt.suptitle(f'{title_prefix}Multiple Slices Visualization', fontsize=14)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Multiple slices visualization saved to: {save_path}")
    else:
        plt.show()

    plt.close()


def ImageResample(sitk_image, new_spacing = [1,1,1], is_label = False):
    '''
    sitk_image:
    new_spacing: x,y,z
    is_label: if True, using Interpolator `sitk.sitkNearestNeighbor`
    '''
    size = np.array(sitk_image.GetSize())
    spacing = np.array(sitk_image.GetSpacing())
    new_spacing = np.array(new_spacing)
    new_size = size * spacing / new_spacing
    new_spacing_refine = size * spacing / new_size
    new_spacing_refine = [float(s) for s in new_spacing_refine]
    new_size = [int(s) for s in new_size]
    if not is_label:
        print(f'Original size: {size}')
        print(f'New size: {new_size}')
        print(f'Original spacing: {spacing}')
        print(f'New spacing: {new_spacing_refine}')
    resample = sitk.ResampleImageFilter()
    resample.SetOutputDirection(sitk_image.GetDirection())
    resample.SetOutputOrigin(sitk_image.GetOrigin())
    resample.SetSize(new_size)
    resample.SetOutputSpacing(new_spacing_refine)

    if is_label:
        resample.SetInterpolator(sitk.sitkNearestNeighbor)
    else:
        resample.SetInterpolator(sitk.sitkBSpline)
    newimage = resample.Execute(sitk_image)
    return newimage



def set_window_wl_ww(tensor, sl_window=None):
    assert(sl_window != None)
    # sl_window = [75,400]
    # sl_window = [-125, 275]
    [wl,ww] = sl_window
    w_min, w_max = wl - ww//2, wl + ww//2
    tensor[tensor < w_min] = w_min
    tensor[tensor > w_max] = w_max
    tensor = (tensor - w_min) / (w_max - w_min)
    ### min max Normalization
    return tensor

# 根据标签图像（label）中的非零区域来裁剪图像（image）和标签本身
def crop_roi(image, label):
    assert(image.shape == label.shape)
    print(f'Before crop: {image.shape}')
    ### crop based on lung segmentation
    w, h, d = label.shape

    tempL = np.nonzero(label)
    minx, maxx = np.min(tempL[0]), np.max(tempL[0])
    miny, maxy = np.min(tempL[1]), np.max(tempL[1])
    minz, maxz = np.min(tempL[2]), np.max(tempL[2])
    # print('minz, maxz: ', minz, maxz)
    # input()

    # px py pz 是需要扩展的边界的长度
    px = max(output_size[0] - (maxx - minx), 0) // 2    # DCB: 扩展边界，防止连预设的96 96 96 都不够？
    py = max(output_size[1] - (maxy - miny), 0) // 2
    pz = max(output_size[2] - (maxz - minz), 0) // 2
    minx = max(minx - px - 25, 0) #np.random.randint(5, 10)
    maxx = min(maxx + px + 25, w) #np.random.randint(5, 10)
    miny = max(miny - py - 25, 0)
    maxy = min(maxy + py + 25, h)
    minz = max(minz - pz - 25, 0)
    maxz = min(maxz + pz + 25, d)
    
    image = image[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 不知道为什么，原始的代码只在xy这两个轴上做crop，不管z轴
    label = label[minx:maxx, miny:maxy, minz:maxz].astype(np.float32)   # 这里进行了修改使得在z轴上也进行裁剪
    return image, label


output_size =[96,96,96]
base_path = '/home/<USER>/data/tumor/Dataset017_Liver'    # 改
list_label = glob(f'{base_path}/imagesTr/*')
dataset_name = 'Liver'                                         # 改

# 可视化配置
ENABLE_VISUALIZATION = True  # 设置为True启用可视化
VISUALIZE_FIRST_N = 3        # 只可视化前N个样本
SAVE_VISUALIZATIONS = True   # 是否保存可视化结果

HU_window_dic = {
        'Lung': [-1000, 1000],
        'HepaticVessel': [0, 230],    # 221 和 178 的image和label的spacing不细小差异导致resample的new size不一样，单独处理用tmp.py处理
        'Spleen': [-125, 275],
        'Colon': [-57, 175],
        'Pancreas': [-87, 199],
        'Liver': [-21, 189],           # 41 和 34 和 89 没有肿瘤,在train.list里也手动剔除了， 85 的image和label的spacing不细小差异导致resample的new size不一样，单独处理用tmp.py处理
        'KiTS2023': [-79,304],   # https://blog.csdn.net/h201601060805/article/details/109197644
    }

processed_count = 0
for item in tqdm(list_label):
    name_image = str(item)
    name_label = name_image.replace('imagesTr', 'labelsTr')   # 注意一下是否需要修改 如果label有多个标签处理后
    if name_image == '/home/<USER>/data/tumor/Dataset017_Liver/imagesTr/liver_85.nii.gz':
        continue
    print('Processing image: ', name_image)
    print('Processing label: ', name_label)
#     pdb.set_trace
    itk_img = sitk.ReadImage(name_image)
    itk_img = ImageResample(itk_img)
    image = sitk.GetArrayFromImage(itk_img)
    image = np.transpose(image, (2,1,0))
    
    itk_label = sitk.ReadImage(name_label)
    itk_label = ImageResample(itk_label, is_label = True)
    label = sitk.GetArrayFromImage(itk_label)
    label = np.transpose(label, (2,1,0))

    # print(np.max(label))
    # try:
    #     if np.max(label) != 1:
    #         with open("max_label_errors.txt", "w") as f:
    #             f.write(f"{name_image}  max(label): {np.max(label)} \n")
    #         continue
    #     assert np.min(label) == 0, "Minimum value of label is not 0"
    # except AssertionError as e:
    #     print(f"Assertion failed: {e}")
    #     # 这里可以添加额外的错误处理逻辑
    # except Exception as e:
    #     print(f"An unexpected error occurred: {e}")
    # assert(np.max(label) == 1 and np.min(label) == 0)
    print(f'Unique values in label: {np.unique(label)}')
    # assert(np.unique(label).shape[0] == 2)
    # print(f'Image shape: {image.shape}, Label shape: {label.shape}')
    assert(np.shape(label)==np.shape(image))

    image = set_window_wl_ww(image, HU_window_dic[dataset_name])

    image, label = crop_roi(image, label)
    image = (image - np.mean(image)) / np.std(image) # z-score ?
    print(f'After crop: {image.shape}')

    # 可视化处理后的数据
    if ENABLE_VISUALIZATION and processed_count < VISUALIZE_FIRST_N:
        base_name = os.path.basename(name_image)[:-7]  # 移除.nii.gz

        # 创建可视化输出目录
        vis_dir = os.path.join(base_path, 'visualizations')
        os.makedirs(vis_dir, exist_ok=True)

        print(f"Creating visualizations for {base_name}...")

        # 单切片可视化 - 中间切片
        if SAVE_VISUALIZATIONS:
            single_slice_path = os.path.join(vis_dir, f'{base_name}_single_slice.png')
            visualize_slice(image, label, slice_idx=None,
                          save_path=single_slice_path,
                          title_prefix=f'{base_name} - ')
        else:
            visualize_slice(image, label, slice_idx=None,
                          title_prefix=f'{base_name} - ')

        # 多切片可视化
        if SAVE_VISUALIZATIONS:
            multi_slice_path = os.path.join(vis_dir, f'{base_name}_multiple_slices.png')
            visualize_multiple_slices(image, label, num_slices=5,
                                    save_path=multi_slice_path,
                                    title_prefix=f'{base_name} - ')
        else:
            visualize_multiple_slices(image, label, num_slices=5,
                                    title_prefix=f'{base_name} - ')

        # 打印标签统计信息
        unique_labels, counts = np.unique(label, return_counts=True)
        total_pixels = label.size
        print(f"Label statistics for {base_name}:")
        for label_val, count in zip(unique_labels, counts):
            percentage = count / total_pixels * 100
            print(f"  Label {int(label_val)}: {count:,} pixels ({percentage:.2f}%)")
        print()

    # 保存H5文件
    os.makedirs(f'{base_path}/h5/', exist_ok=True)
    base_name = os.path.basename(name_image)
    f = h5py.File((f'{base_path}/h5/'+base_name[:-7] + '.h5'), 'w')
    f.create_dataset('image', data=image, compression="gzip")
    f.create_dataset('label', data=label, compression="gzip")
    f.close()

    processed_count += 1

