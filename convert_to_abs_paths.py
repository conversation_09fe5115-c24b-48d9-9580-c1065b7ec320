#!/usr/bin/env python3
"""
Script to convert relative h5 file paths to absolute paths in train.list and test.list files
for all datasets in the tumor directory.
"""

import os
import glob

def convert_list_to_absolute(input_file, output_file, base_dir):
    """
    Convert relative paths in a list file to absolute paths.

    Args:
        input_file: Path to the input list file
        output_file: Path to the output list file with absolute paths
        base_dir: Base directory containing the h5 folder
    """
    if not os.path.exists(input_file):
        print(f"Warning: {input_file} does not exist, skipping...")
        return False

    h5_dir = os.path.join(base_dir, "h5")

    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            line = line.strip()
            if line:  # Skip empty lines
                # Create absolute path
                absolute_path = os.path.join(h5_dir, line)
                outfile.write(absolute_path + '\n')
    return True

def process_dataset(dataset_dir):
    """
    Process a single dataset directory.

    Args:
        dataset_dir: Path to the dataset directory
    """
    dataset_name = os.path.basename(dataset_dir)
    print(f"\n=== Processing {dataset_name} ===")

    # Convert train.list
    train_input = os.path.join(dataset_dir, "train.list")
    train_output = os.path.join(dataset_dir, "train_abs.list")
    if convert_list_to_absolute(train_input, train_output, dataset_dir):
        print(f"✓ Created {train_output}")

    # Convert test.list
    test_input = os.path.join(dataset_dir, "test.list")
    test_output = os.path.join(dataset_dir, "test_abs.list")
    if convert_list_to_absolute(test_input, test_output, dataset_dir):
        print(f"✓ Created {test_output}")

def main():
    # Base tumor directory
    tumor_dir = "/home/<USER>/data/tumor"

    # Find all dataset directories
    dataset_pattern = os.path.join(tumor_dir, "Dataset*")
    dataset_dirs = glob.glob(dataset_pattern)
    dataset_dirs.sort()

    print(f"Found {len(dataset_dirs)} datasets to process:")
    for dataset_dir in dataset_dirs:
        print(f"  - {os.path.basename(dataset_dir)}")

    # Process each dataset
    for dataset_dir in dataset_dirs:
        process_dataset(dataset_dir)

    print(f"\n🎉 Processing complete! Processed {len(dataset_dirs)} datasets.")

if __name__ == "__main__":
    main()
