#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze all NIfTI files in the liver dataset directory
Check the number of slices and spacing for each file
Uses multiprocessing for faster analysis
"""

import nibabel as nib
import numpy as np
import os
import glob
import pandas as pd
import multiprocessing as mp
import time

def analyze_nifti_file(file_path):
    """
    Analyze a single NIfTI file and return its properties
    
    Args:
        file_path (str): Path to the NIfTI file
        
    Returns:
        dict: Dictionary containing file properties
    """
    try:
        # Load the NIfTI file
        nii_img = nib.load(file_path)
        img_data = nii_img.get_fdata()
        header = nii_img.header
        
        # Get basic properties
        shape = img_data.shape
        voxel_sizes = header.get_zooms()
        
        # Extract filename
        filename = os.path.basename(file_path)
        
        result = {
            'filename': filename,
            'dimensions': len(shape),
            'shape': shape,
            'data_type': str(img_data.dtype),
            'min_value': float(np.min(img_data)),
            'max_value': float(np.max(img_data)),
        }
        
        # Handle different dimensionalities
        if len(shape) == 3:
            result.update({
                'width': shape[0],
                'height': shape[1],
                'slices': shape[2],
                'spacing_x': float(voxel_sizes[0]),
                'spacing_y': float(voxel_sizes[1]),
                'spacing_z': float(voxel_sizes[2]),
            })
        elif len(shape) == 4:
            result.update({
                'width': shape[0],
                'height': shape[1],
                'slices': shape[2],
                'fourth_dim': shape[3],
                'spacing_x': float(voxel_sizes[0]),
                'spacing_y': float(voxel_sizes[1]),
                'spacing_z': float(voxel_sizes[2]),
                'spacing_4th': float(voxel_sizes[3]) if len(voxel_sizes) > 3 else None,
            })
        
        return result
        
    except Exception as e:
        return {
            'filename': os.path.basename(file_path),
            'error': str(e)
        }

def analyze_liver_dataset(dataset_dir, num_processes=None):
    """
    Analyze all NIfTI files in the liver dataset directory using multiprocessing

    Args:
        dataset_dir (str): Path to the dataset directory
        num_processes (int): Number of processes to use (default: CPU count)
    """
    print(f"正在分析目录: {dataset_dir}")

    # Find all .nii.gz files
    pattern = os.path.join(dataset_dir, "*.nii.gz")
    nifti_files = glob.glob(pattern)

    if not nifti_files:
        print(f"在目录 {dataset_dir} 中没有找到 .nii.gz 文件")
        return

    print(f"找到 {len(nifti_files)} 个 NIfTI 文件")

    # Sort files for consistent ordering
    nifti_files.sort()

    # Determine number of processes
    if num_processes is None:
        num_processes = min(8, len(nifti_files))

    print(f"使用 {num_processes} 个进程进行并行处理...")

    # Use multiprocessing to analyze files
    start_time = time.time()

    with mp.Pool(processes=num_processes) as pool:
        # Create progress tracking
        total_files = len(nifti_files)

        # Process files in parallel
        results = []
        for i, result in enumerate(pool.imap(analyze_nifti_file, nifti_files)):
            results.append(result)
            if (i + 1) % 10 == 0 or (i + 1) == total_files:
                print(f"已处理: {i + 1}/{total_files} 文件")

    end_time = time.time()
    print(f"处理完成，耗时: {end_time - start_time:.2f} 秒")
    
    # Create DataFrame for better display
    df = pd.DataFrame(results)
    
    # Display summary
    print("\n" + "="*80)
    print("分析结果汇总")
    print("="*80)
    
    if 'error' in df.columns:
        error_files = df[df['error'].notna()]
        if not error_files.empty:
            print(f"错误文件数量: {len(error_files)}")
            for _, row in error_files.iterrows():
                print(f"  {row['filename']}: {row['error']}")
            print()
    
    # Filter successful results
    success_df = df[df['error'].isna()] if 'error' in df.columns else df
    
    if success_df.empty:
        print("没有成功分析的文件")
        return
    
    print(f"成功分析的文件数量: {len(success_df)}")
    print()
    
    # Display basic statistics
    if 'slices' in success_df.columns:
        print("切片数统计:")
        print(f"  最小切片数: {success_df['slices'].min()}")
        print(f"  最大切片数: {success_df['slices'].max()}")
        print(f"  平均切片数: {success_df['slices'].mean():.1f}")
        print(f"  中位数切片数: {success_df['slices'].median():.1f}")
        print()
    
    if 'spacing_z' in success_df.columns:
        print("Z轴spacing统计 (切片间距):")
        print(f"  最小spacing: {success_df['spacing_z'].min():.3f} mm")
        print(f"  最大spacing: {success_df['spacing_z'].max():.3f} mm")
        print(f"  平均spacing: {success_df['spacing_z'].mean():.3f} mm")
        print(f"  中位数spacing: {success_df['spacing_z'].median():.3f} mm")
        print()
    
    # Display detailed results for first few files
    print("前10个文件的详细信息:")
    print("-" * 80)
    
    for _, row in success_df.head(10).iterrows():
        print(f"文件: {row['filename']}")
        if 'slices' in row:
            print(f"  切片数: {row['slices']}")
        if 'shape' in row:
            print(f"  图像尺寸: {row['shape']}")
        if all(col in row for col in ['spacing_x', 'spacing_y', 'spacing_z']):
            print(f"  体素间距 (x,y,z): ({row['spacing_x']:.3f}, {row['spacing_y']:.3f}, {row['spacing_z']:.3f}) mm")
        print()
    
    # Save detailed results to CSV
    output_file = "tmp/liver_analysis_results.csv"
    success_df.to_csv(output_file, index=False)
    print(f"详细结果已保存到: {output_file}")

if __name__ == "__main__":
    import sys

    dataset_dir = "tumor/Dataset017_Liver/imagesTr"

    # Allow specifying number of processes via command line
    num_processes = None
    if len(sys.argv) > 1:
        try:
            num_processes = int(sys.argv[1])
            print(f"使用指定的进程数: {num_processes}")
        except ValueError:
            print("无效的进程数参数，使用默认值")

    analyze_liver_dataset(dataset_dir, num_processes)
