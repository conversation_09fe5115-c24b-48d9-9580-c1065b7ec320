#!/usr/bin/env python3
"""
重新分配训练集和测试集
0-100: 训练集
101-130: 测试集
"""

import os
import re
from pathlib import Path

def extract_patient_id(file_path):
    """
    从文件路径中提取病人ID
    例如: /path/to/liver_123_045.h5 -> 123
    """
    filename = os.path.basename(file_path)
    match = re.match(r'liver_(\d+)_\d+\.h5', filename)
    if match:
        return int(match.group(1))
    return None

def read_existing_files(train_file, test_file):
    """读取现有的训练和测试文件"""
    all_files = []
    
    # 读取训练文件
    if os.path.exists(train_file):
        with open(train_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    all_files.append(line)
    
    # 读取测试文件
    if os.path.exists(test_file):
        with open(test_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    all_files.append(line)
    
    return all_files

def redistribute_files(all_files):
    """重新分配文件到训练集和测试集"""
    train_files = []
    test_files = []
    
    for file_path in all_files:
        patient_id = extract_patient_id(file_path)
        if patient_id is not None:
            if 0 <= patient_id <= 100:
                train_files.append(file_path)
            elif 101 <= patient_id <= 130:
                test_files.append(file_path)
            else:
                print(f"⚠️  病人ID超出范围: {patient_id} - {file_path}")
        else:
            print(f"⚠️  无法解析病人ID: {file_path}")
    
    # 按病人ID和切片ID排序
    def sort_key(file_path):
        filename = os.path.basename(file_path)
        match = re.match(r'liver_(\d+)_(\d+)\.h5', filename)
        if match:
            patient_id = int(match.group(1))
            slice_id = int(match.group(2))
            return (patient_id, slice_id)
        return (999999, 999999)  # 无法解析的文件排在最后
    
    train_files.sort(key=sort_key)
    test_files.sort(key=sort_key)
    
    return train_files, test_files

def write_files(files, output_file):
    """写入文件列表到指定文件"""
    with open(output_file, 'w') as f:
        for file_path in files:
            f.write(file_path + '\n')

def analyze_distribution(files, label):
    """分析文件分布"""
    patient_counts = {}
    
    for file_path in files:
        patient_id = extract_patient_id(file_path)
        if patient_id is not None:
            patient_counts[patient_id] = patient_counts.get(patient_id, 0) + 1
    
    print(f"\n📊 {label} 分布:")
    print(f"   总文件数: {len(files)}")
    print(f"   病人数量: {len(patient_counts)}")
    print(f"   病人ID范围: {min(patient_counts.keys())} - {max(patient_counts.keys())}")
    
    # 显示每个病人的切片数量（前10个和后10个）
    sorted_patients = sorted(patient_counts.items())
    print(f"   切片数量分布:")
    
    # 显示前10个病人
    for i, (patient_id, count) in enumerate(sorted_patients[:10]):
        print(f"     liver_{patient_id}: {count} 切片")
    
    if len(sorted_patients) > 20:
        print(f"     ... (省略 {len(sorted_patients) - 20} 个病人)")
        
        # 显示后10个病人
        for patient_id, count in sorted_patients[-10:]:
            print(f"     liver_{patient_id}: {count} 切片")
    elif len(sorted_patients) > 10:
        # 显示剩余的病人
        for patient_id, count in sorted_patients[10:]:
            print(f"     liver_{patient_id}: {count} 切片")

def main():
    """主函数"""
    # 文件路径
    train_file = "/home/<USER>/data/tumor/split/train_abs.list"
    test_file = "/home/<USER>/data/tumor/split/test_abs.list"
    
    print("🔄 重新分配训练集和测试集")
    print("=" * 60)
    print(f"📁 训练文件: {train_file}")
    print(f"📁 测试文件: {test_file}")
    print(f"📋 分配规则:")
    print(f"   训练集: liver_0 到 liver_100")
    print(f"   测试集: liver_101 到 liver_130")
    print("=" * 60)
    
    # 读取现有文件
    print("🔍 读取现有文件...")
    all_files = read_existing_files(train_file, test_file)
    print(f"   总共找到 {len(all_files)} 个文件")
    
    # 重新分配
    print("\n🔄 重新分配文件...")
    train_files, test_files = redistribute_files(all_files)
    
    # 分析分布
    analyze_distribution(train_files, "训练集")
    analyze_distribution(test_files, "测试集")
    
    # 写入新文件
    print(f"\n📝 写入新的文件列表...")
    write_files(train_files, train_file)
    write_files(test_files, test_file)
    
    print(f"✅ 训练文件已更新: {train_file}")
    print(f"✅ 测试文件已更新: {test_file}")
    
    # 验证结果
    print(f"\n🔍 验证结果:")
    print(f"   训练集文件数: {len(train_files)}")
    print(f"   测试集文件数: {len(test_files)}")
    print(f"   总文件数: {len(train_files) + len(test_files)}")
    
    # 检查是否有重复或遗漏
    all_redistributed = set(train_files + test_files)
    original_set = set(all_files)
    
    if all_redistributed == original_set:
        print(f"✅ 文件重新分配完成，无遗漏或重复")
    else:
        missing = original_set - all_redistributed
        extra = all_redistributed - original_set
        if missing:
            print(f"⚠️  遗漏文件: {len(missing)} 个")
        if extra:
            print(f"⚠️  额外文件: {len(extra)} 个")

if __name__ == "__main__":
    main()
