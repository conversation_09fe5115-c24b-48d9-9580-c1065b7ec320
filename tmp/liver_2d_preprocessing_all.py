#!/usr/bin/env python3
"""
Liver数据集2D预处理脚本 - 处理所有切片版本
将3D NIfTI文件转换为2D H5切片，应用HU窗口、尺寸调整和spacing_z处理
保留所有切片，不进行ROI过滤
"""

import nibabel as nib
import numpy as np
import h5py
import os
from pathlib import Path
import cv2
from tqdm import tqdm
import argparse
from scipy import ndimage

def apply_hu_window(image, window_min=-21, window_max=189):
    """
    应用HU窗口并归一化到[0,1]
    
    Args:
        image (np.ndarray): 输入图像
        window_min (int): HU窗口最小值
        window_max (int): HU窗口最大值
    
    Returns:
        np.ndarray: 处理后的图像
    """
    # 应用HU窗口
    image_windowed = np.clip(image, window_min, window_max)
    
    # 归一化到[0,1]
    image_normalized = (image_windowed - window_min) / (window_max - window_min)
    
    return image_normalized.astype(np.float32)

def resize_image(image, target_size=(256, 256)):
    """
    调整图像尺寸到目标大小
    
    Args:
        image (np.ndarray): 输入图像
        target_size (tuple): 目标尺寸 (height, width)
    
    Returns:
        np.ndarray: 调整后的图像
    """
    if image.shape[:2] == target_size:
        return image
    
    # 使用双线性插值调整尺寸
    resized = cv2.resize(image, (target_size[1], target_size[0]), interpolation=cv2.INTER_LINEAR)
    return resized

def resize_label(label, target_size=(256, 256)):
    """
    调整标签尺寸到目标大小（使用最近邻插值）
    
    Args:
        label (np.ndarray): 输入标签
        target_size (tuple): 目标尺寸 (height, width)
    
    Returns:
        np.ndarray: 调整后的标签
    """
    if label.shape[:2] == target_size:
        return label
    
    # 使用最近邻插值调整尺寸，保持标签值不变
    resized = cv2.resize(label, (target_size[1], target_size[0]), interpolation=cv2.INTER_NEAREST)
    return resized

def resample_volume_z(volume, original_spacing_z, target_spacing_z=3.0):
    """
    在Z轴方向重采样体积数据到目标spacing
    
    Args:
        volume (np.ndarray): 输入体积数据 (H, W, D)
        original_spacing_z (float): 原始Z轴spacing
        target_spacing_z (float): 目标Z轴spacing
    
    Returns:
        np.ndarray: 重采样后的体积数据
    """
    if abs(original_spacing_z - target_spacing_z) < 1e-6:
        return volume
    
    # 计算缩放因子
    scale_factor = original_spacing_z / target_spacing_z
    
    # 计算新的Z轴尺寸
    original_depth = volume.shape[2]
    new_depth = int(round(original_depth * scale_factor))
    
    print(f"   Z轴重采样: {original_depth} -> {new_depth} (spacing: {original_spacing_z:.3f} -> {target_spacing_z:.3f})")
    
    # 使用scipy进行3D插值重采样
    zoom_factors = (1.0, 1.0, scale_factor)  # 只在Z轴方向缩放
    
    # 对图像使用线性插值
    if volume.dtype in [np.float32, np.float64]:
        resampled = ndimage.zoom(volume, zoom_factors, order=1, mode='nearest')
    else:
        # 对标签使用最近邻插值
        resampled = ndimage.zoom(volume, zoom_factors, order=0, mode='nearest')
    
    return resampled

def process_single_case(image_path, label_path, output_dir, case_name, 
                       hu_window=(-21, 189), target_size=(256, 256), 
                       target_spacing_z=3.0):
    """
    处理单个病例
    
    Args:
        image_path (str): 图像文件路径
        label_path (str): 标签文件路径
        output_dir (str): 输出目录
        case_name (str): 病例名称（如liver_1）
        hu_window (tuple): HU窗口 (min, max)
        target_size (tuple): 目标尺寸
        target_spacing_z (float): 目标Z轴spacing
    
    Returns:
        dict: 处理结果统计
    """
    try:
        # 加载数据
        print(f"🔍 处理病例: {case_name}")
        img_nii = nib.load(image_path)
        label_nii = nib.load(label_path)
        
        img_data = img_nii.get_fdata()
        label_data = label_nii.get_fdata()
        
        # 检查形状匹配
        if img_data.shape != label_data.shape:
            print(f"⚠️  警告: {case_name} 图像和标签形状不匹配!")
            return {"error": "Shape mismatch"}
        
        # 获取原始spacing
        original_spacing = img_nii.header.get_zooms()
        original_spacing_z = original_spacing[2]
        
        print(f"📊 原始形状: {img_data.shape}")
        print(f"📏 原始spacing: {original_spacing}")
        
        # Z轴重采样
        if abs(original_spacing_z - target_spacing_z) > 1e-6:
            print(f"🔄 进行Z轴重采样...")
            img_data = resample_volume_z(img_data, original_spacing_z, target_spacing_z)
            label_data = resample_volume_z(label_data, original_spacing_z, target_spacing_z)
            print(f"📊 重采样后形状: {img_data.shape}")
        else:
            print(f"✅ Z轴spacing已符合要求，跳过重采样")
        
        total_slices = img_data.shape[2]
        saved_slices = []
        
        # 处理每个切片（保留所有切片）
        for z in tqdm(range(total_slices), desc=f"处理 {case_name} 切片"):
            # 提取切片
            img_slice = img_data[:, :, z]
            label_slice = label_data[:, :, z]
            
            # 应用HU窗口
            img_processed = apply_hu_window(img_slice, hu_window[0], hu_window[1])
            
            # 调整尺寸
            img_resized = resize_image(img_processed, target_size)
            label_resized = resize_label(label_slice, target_size)
            
            # 确保标签是整数类型
            label_resized = label_resized.astype(np.uint8)
            
            # 生成输出文件名 - 使用连续编号（从001开始）
            slice_filename = f"{case_name}_{z+1:03d}.h5"
            output_path = os.path.join(output_dir, slice_filename)
            
            # 保存为H5文件
            with h5py.File(output_path, 'w') as f:
                f.create_dataset('image', data=img_resized, compression='gzip', compression_opts=9)
                f.create_dataset('label', data=label_resized, compression='gzip', compression_opts=9)
                
                # 保存元数据
                f.attrs['case_name'] = case_name
                f.attrs['slice_index'] = z  # 重采样后的切片索引
                f.attrs['slice_number'] = z + 1  # 切片编号（从1开始）
                f.attrs['original_shape'] = img_nii.shape
                f.attrs['resampled_shape'] = img_data.shape
                f.attrs['target_size'] = target_size
                f.attrs['hu_window'] = hu_window
                f.attrs['original_spacing'] = original_spacing
                f.attrs['target_spacing_z'] = target_spacing_z
                f.attrs['actual_spacing_z'] = target_spacing_z
            
            saved_slices.append({
                'slice_index': z,
                'slice_number': z + 1,
                'filename': slice_filename,
                'output_path': output_path
            })
        
        result = {
            'case_name': case_name,
            'total_slices': total_slices,
            'saved_slices': saved_slices,
            'original_shape': img_nii.shape,
            'resampled_shape': img_data.shape,
            'original_spacing': original_spacing,
            'target_spacing_z': target_spacing_z
        }
        
        print(f"✅ {case_name} 处理完成: {total_slices} 个切片全部保存")
        return result
        
    except Exception as e:
        print(f"❌ 处理 {case_name} 时出错: {str(e)}")
        return {"error": str(e), "case_name": case_name}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Liver数据集2D预处理 - 保留所有切片')
    parser.add_argument('--image_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/imagesTr',
                       help='图像目录路径')
    parser.add_argument('--label_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/labelsTr',
                       help='标签目录路径')
    parser.add_argument('--output_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/h5_all',
                       help='输出目录路径')
    parser.add_argument('--list_file', default='/home/<USER>/data/tumor/split/2d_all.list',
                       help='输出列表文件路径')
    parser.add_argument('--hu_min', type=int, default=-21, help='HU窗口最小值')
    parser.add_argument('--hu_max', type=int, default=189, help='HU窗口最大值')
    parser.add_argument('--size', type=int, default=256, help='目标图像尺寸')
    parser.add_argument('--spacing_z', type=float, default=3.0, help='目标Z轴spacing')
    parser.add_argument('--start_case', type=int, default=0, help='开始处理的病例编号')
    parser.add_argument('--end_case', type=int, default=None, help='结束处理的病例编号')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建列表文件目录
    list_dir = Path(args.list_file).parent
    list_dir.mkdir(parents=True, exist_ok=True)
    
    print("🏥 Liver数据集2D预处理 - 保留所有切片")
    print("=" * 60)
    print(f"📁 图像目录: {args.image_dir}")
    print(f"📁 标签目录: {args.label_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📁 列表文件: {args.list_file}")
    print(f"🪟 HU窗口: [{args.hu_min}, {args.hu_max}]")
    print(f"📏 目标尺寸: {args.size}×{args.size}")
    print(f"📏 目标Z轴spacing: {args.spacing_z} mm")
    print("=" * 60)
    
    # 获取所有病例文件
    image_files = sorted([f for f in os.listdir(args.image_dir) if f.endswith('.nii.gz')])
    
    if args.end_case is not None:
        image_files = image_files[args.start_case:args.end_case]
    else:
        image_files = image_files[args.start_case:]
    
    print(f"📋 找到 {len(image_files)} 个病例需要处理")
    
    # 处理统计
    all_results = []
    total_processed = 0
    total_saved = 0
    all_file_paths = []
    
    # 处理每个病例
    for i, image_file in enumerate(image_files):
        case_name = image_file.replace('.nii.gz', '')
        image_path = os.path.join(args.image_dir, image_file)
        label_path = os.path.join(args.label_dir, image_file)
        
        # 检查标签文件是否存在
        if not os.path.exists(label_path):
            print(f"⚠️  跳过 {case_name}: 标签文件不存在")
            continue
        
        print(f"\n📦 处理进度: {i+1}/{len(image_files)}")
        
        # 处理病例
        result = process_single_case(
            image_path, label_path, args.output_dir, case_name,
            hu_window=(args.hu_min, args.hu_max),
            target_size=(args.size, args.size),
            target_spacing_z=args.spacing_z
        )
        
        if "error" not in result:
            all_results.append(result)
            total_processed += result['total_slices']
            total_saved += result['total_slices']  # 保存所有切片
            
            # 收集所有文件路径
            for slice_info in result['saved_slices']:
                all_file_paths.append(slice_info['output_path'])
    
    # 按照要求排序：病人号优先级高于切片号
    print(f"\n🔄 排序文件列表...")
    all_file_paths.sort(key=lambda x: (
        int(os.path.basename(x).split('_')[1]),  # 病人号
        int(os.path.basename(x).split('_')[2].split('.')[0])  # 切片号
    ))
    
    # 写入列表文件
    print(f"📝 写入列表文件: {args.list_file}")
    with open(args.list_file, 'w') as f:
        for file_path in all_file_paths:
            f.write(file_path + '\n')
    
    # 输出最终统计
    print(f"\n🎉 处理完成!")
    print("=" * 60)
    print(f"📊 处理统计:")
    print(f"   成功处理病例数: {len(all_results)}")
    print(f"   总切片数: {total_processed}")
    print(f"   保存切片数: {total_saved}")
    print(f"   保存比例: 100.0% (保留所有切片)")
    print(f"   列表文件: {args.list_file}")
    print(f"   列表文件包含: {len(all_file_paths)} 个文件路径")
    
    # 保存处理日志
    log_file = os.path.join(args.output_dir, 'processing_log.txt')
    with open(log_file, 'w') as f:
        f.write("Liver数据集2D预处理日志 - 保留所有切片\n")
        f.write("=" * 50 + "\n")
        f.write(f"HU窗口: [{args.hu_min}, {args.hu_max}]\n")
        f.write(f"目标尺寸: {args.size}×{args.size}\n")
        f.write(f"目标Z轴spacing: {args.spacing_z} mm\n")
        f.write(f"处理病例数: {len(all_results)}\n")
        f.write(f"总切片数: {total_processed}\n")
        f.write(f"保存切片数: {total_saved}\n")
        f.write(f"保存比例: 100.0%\n\n")
        
        for result in all_results:
            f.write(f"{result['case_name']}: {result['total_slices']} 切片\n")
            f.write(f"  原始形状: {result['original_shape']}\n")
            f.write(f"  重采样后形状: {result['resampled_shape']}\n")
            f.write(f"  原始spacing: {result['original_spacing']}\n")
            f.write(f"  目标Z轴spacing: {result['target_spacing_z']}\n\n")
    
    print(f"📝 处理日志已保存到: {log_file}")

if __name__ == "__main__":
    main()
