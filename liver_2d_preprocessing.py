#!/usr/bin/env python3
"""
Liver数据集2D预处理脚本
将3D NIfTI文件转换为2D H5切片，应用HU窗口和尺寸调整
"""

import nibabel as nib
import numpy as np
import h5py
import os
from pathlib import Path
import cv2
from tqdm import tqdm
import argparse

def apply_hu_window(image, window_min=-21, window_max=189):
    """
    应用HU窗口并归一化到[0,1]
    
    Args:
        image (np.ndarray): 输入图像
        window_min (int): HU窗口最小值
        window_max (int): HU窗口最大值
    
    Returns:
        np.ndarray: 处理后的图像
    """
    # 应用HU窗口
    image_windowed = np.clip(image, window_min, window_max)
    
    # 归一化到[0,1]
    image_normalized = (image_windowed - window_min) / (window_max - window_min)
    
    return image_normalized.astype(np.float32)

def resize_image(image, target_size=(256, 256)):
    """
    调整图像尺寸到目标大小
    
    Args:
        image (np.ndarray): 输入图像
        target_size (tuple): 目标尺寸 (height, width)
    
    Returns:
        np.ndarray: 调整后的图像
    """
    if image.shape[:2] == target_size:
        return image
    
    # 使用双线性插值调整尺寸
    resized = cv2.resize(image, (target_size[1], target_size[0]), interpolation=cv2.INTER_LINEAR)
    return resized

def resize_label(label, target_size=(256, 256)):
    """
    调整标签尺寸到目标大小（使用最近邻插值）
    
    Args:
        label (np.ndarray): 输入标签
        target_size (tuple): 目标尺寸 (height, width)
    
    Returns:
        np.ndarray: 调整后的标签
    """
    if label.shape[:2] == target_size:
        return label
    
    # 使用最近邻插值调整尺寸，保持标签值不变
    resized = cv2.resize(label, (target_size[1], target_size[0]), interpolation=cv2.INTER_NEAREST)
    return resized

def calculate_roi_percentage(label_slice):
    """
    计算ROI区域百分比
    
    Args:
        label_slice (np.ndarray): 标签切片
    
    Returns:
        float: ROI百分比
    """
    roi_pixels = np.sum(label_slice > 0)
    total_pixels = label_slice.size
    return roi_pixels / total_pixels

def process_single_case(image_path, label_path, output_dir, case_name, 
                       hu_window=(-21, 189), target_size=(256, 256), 
                       roi_threshold=0.05):
    """
    处理单个病例
    
    Args:
        image_path (str): 图像文件路径
        label_path (str): 标签文件路径
        output_dir (str): 输出目录
        case_name (str): 病例名称（如liver_1）
        hu_window (tuple): HU窗口 (min, max)
        target_size (tuple): 目标尺寸
        roi_threshold (float): ROI阈值
    
    Returns:
        dict: 处理结果统计
    """
    try:
        # 加载数据
        print(f"🔍 处理病例: {case_name}")
        img_nii = nib.load(image_path)
        label_nii = nib.load(label_path)
        
        img_data = img_nii.get_fdata()
        label_data = label_nii.get_fdata()
        
        # 检查形状匹配
        if img_data.shape != label_data.shape:
            print(f"⚠️  警告: {case_name} 图像和标签形状不匹配!")
            return {"error": "Shape mismatch"}
        
        print(f"📊 原始形状: {img_data.shape}")
        
        total_slices = img_data.shape[2]
        valid_slices = 0
        saved_slices = []

        # 首先收集所有符合要求的切片
        valid_slice_data = []

        print(f"🔍 第一遍扫描：寻找符合ROI要求的切片...")
        for z in range(total_slices):
            # 提取切片
            img_slice = img_data[:, :, z]
            label_slice = label_data[:, :, z]

            # 计算ROI百分比
            roi_percentage = calculate_roi_percentage(label_slice)

            # 检查是否满足ROI阈值
            if roi_percentage >= roi_threshold:
                valid_slice_data.append({
                    'original_index': z,
                    'img_slice': img_slice,
                    'label_slice': label_slice,
                    'roi_percentage': roi_percentage
                })

        print(f"📊 找到 {len(valid_slice_data)} 个符合要求的切片，开始处理...")

        # 处理符合要求的切片，使用连续编号
        for valid_idx, slice_data in enumerate(tqdm(valid_slice_data, desc=f"处理 {case_name} 有效切片")):
            # 应用HU窗口
            img_processed = apply_hu_window(slice_data['img_slice'], hu_window[0], hu_window[1])

            # 调整尺寸
            img_resized = resize_image(img_processed, target_size)
            label_resized = resize_label(slice_data['label_slice'], target_size)

            # 确保标签是整数类型
            label_resized = label_resized.astype(np.uint8)

            # 生成输出文件名 - 使用连续编号（从001开始）
            slice_filename = f"{case_name}_{valid_idx+1:03d}.h5"
            output_path = os.path.join(output_dir, slice_filename)

            # 保存为H5文件
            with h5py.File(output_path, 'w') as f:
                f.create_dataset('image', data=img_resized, compression='gzip', compression_opts=9)
                f.create_dataset('label', data=label_resized, compression='gzip', compression_opts=9)

                # 保存元数据
                f.attrs['case_name'] = case_name
                f.attrs['original_slice_index'] = slice_data['original_index']  # 原始切片索引
                f.attrs['valid_slice_index'] = valid_idx + 1  # 有效切片编号（从1开始）
                f.attrs['roi_percentage'] = slice_data['roi_percentage']
                f.attrs['original_shape'] = img_data.shape
                f.attrs['target_size'] = target_size
                f.attrs['hu_window'] = hu_window
                f.attrs['original_spacing'] = img_nii.header.get_zooms()

            valid_slices += 1
            saved_slices.append({
                'original_slice_index': slice_data['original_index'],
                'valid_slice_index': valid_idx + 1,
                'filename': slice_filename,
                'roi_percentage': slice_data['roi_percentage']
            })
        
        result = {
            'case_name': case_name,
            'total_slices': total_slices,
            'valid_slices': valid_slices,
            'saved_slices': saved_slices,
            'original_shape': img_data.shape,
            'spacing': img_nii.header.get_zooms()
        }
        
        print(f"✅ {case_name} 处理完成: {valid_slices}/{total_slices} 切片保存")
        return result
        
    except Exception as e:
        print(f"❌ 处理 {case_name} 时出错: {str(e)}")
        return {"error": str(e), "case_name": case_name}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Liver数据集2D预处理')
    parser.add_argument('--image_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/imagesTr',
                       help='图像目录路径')
    parser.add_argument('--label_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/labelsTr',
                       help='标签目录路径')
    parser.add_argument('--output_dir', default='/home/<USER>/data/tumor/Dataset017_Liver/h5_2d',
                       help='输出目录路径')
    parser.add_argument('--hu_min', type=int, default=-21, help='HU窗口最小值')
    parser.add_argument('--hu_max', type=int, default=189, help='HU窗口最大值')
    parser.add_argument('--size', type=int, default=256, help='目标图像尺寸')
    parser.add_argument('--roi_threshold', type=float, default=0.05, help='ROI阈值')
    parser.add_argument('--start_case', type=int, default=0, help='开始处理的病例编号')
    parser.add_argument('--end_case', type=int, default=None, help='结束处理的病例编号')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("🏥 Liver数据集2D预处理")
    print("=" * 60)
    print(f"📁 图像目录: {args.image_dir}")
    print(f"📁 标签目录: {args.label_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"🪟 HU窗口: [{args.hu_min}, {args.hu_max}]")
    print(f"📏 目标尺寸: {args.size}×{args.size}")
    print(f"🎯 ROI阈值: {args.roi_threshold*100}%")
    print("=" * 60)
    
    # 获取所有病例文件
    image_files = sorted([f for f in os.listdir(args.image_dir) if f.endswith('.nii.gz')])

    if args.end_case is not None:
        image_files = image_files[args.start_case:args.end_case]
    else:
        image_files = image_files[args.start_case:]
    
    print(f"📋 找到 {len(image_files)} 个病例需要处理")
    
    image_files = ['liver_21.nii.gz', 'liver_42.nii.gz', 'liver_47.nii.gz']
    # 处理统计
    all_results = []
    total_processed = 0
    total_saved = 0
    
    # 处理每个病例
    for i, image_file in enumerate(image_files):
        case_name = image_file.replace('.nii.gz', '')
        image_path = os.path.join(args.image_dir, image_file)
        label_path = os.path.join(args.label_dir, image_file)
        
        # 检查标签文件是否存在
        if not os.path.exists(label_path):
            print(f"⚠️  跳过 {case_name}: 标签文件不存在")
            continue
        
        print(f"\n📦 处理进度: {i+1}/{len(image_files)}")
        
        # 处理病例
        result = process_single_case(
            image_path, label_path, args.output_dir, case_name,
            hu_window=(args.hu_min, args.hu_max),
            target_size=(args.size, args.size),
            roi_threshold=args.roi_threshold
        )
        
        if "error" not in result:
            all_results.append(result)
            total_processed += result['total_slices']
            total_saved += result['valid_slices']
    
    # 输出最终统计
    print(f"\n🎉 处理完成!")
    print("=" * 60)
    print(f"📊 处理统计:")
    print(f"   成功处理病例数: {len(all_results)}")
    print(f"   总切片数: {total_processed}")
    print(f"   保存切片数: {total_saved}")
    print(f"   有效切片比例: {total_saved/total_processed*100:.1f}%")
    
    # 保存处理日志
    log_file = os.path.join(args.output_dir, 'processing_log.txt')
    with open(log_file, 'w') as f:
        f.write("Liver数据集2D预处理日志\n")
        f.write("=" * 40 + "\n")
        f.write(f"HU窗口: [{args.hu_min}, {args.hu_max}]\n")
        f.write(f"目标尺寸: {args.size}×{args.size}\n")
        f.write(f"ROI阈值: {args.roi_threshold}\n")
        f.write(f"处理病例数: {len(all_results)}\n")
        f.write(f"总切片数: {total_processed}\n")
        f.write(f"保存切片数: {total_saved}\n")
        f.write(f"有效切片比例: {total_saved/total_processed*100:.1f}%\n\n")
        
        for result in all_results:
            f.write(f"{result['case_name']}: {result['valid_slices']}/{result['total_slices']} "
                   f"({result['valid_slices']/result['total_slices']*100:.1f}%)\n")
    
    print(f"📝 处理日志已保存到: {log_file}")

if __name__ == "__main__":
    main()
