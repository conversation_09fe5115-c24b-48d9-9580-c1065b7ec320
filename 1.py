import numpy as np
import argparse

def get_npy_info(file_path):
    """获取NPY文件的基本信息"""
    try:
        # 加载NPY文件
        data = np.load(file_path, allow_pickle=True)
        
        # 获取并打印基本信息
        print(f"文件路径: {file_path}")
        print(f"数据类型: {data.dtype}")
        print(f"形状: {data.shape}")
        print(f"维度: {data.ndim}")
        print(f"元素总数: {data.size}")
        
        # 显示数据的前几个元素（如果是多维数组则显示前几个切片）
        if data.size > 0:
            print("\n数据前几个元素信息:")
            if data.ndim == 1:
                print(data[:5])  # 一维数组显示前5个元素
            elif data.ndim == 2:
                print(data[:3, :3])  # 二维数组显示前3x3的切片
            else:
                print(f"多维数组 (仅显示基本信息，维度太多无法展示切片)")
        else:
            print("数据为空")
            
        return data.shape
    
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        return None
    except ValueError as e:
        print(f"错误: 文件格式错误 - {e}")
        return None
    except Exception as e:
        print(f"错误: 发生未知错误 - {e}")
        return None

if __name__ == "__main__":
    # 创建命令行参数解析器
    # parser = argparse.ArgumentParser(description='获取NPY文件的维度信息')
    # parser.add_argument('file_path', type=str, help='NPY文件的路径')
    
    # # 解析命令行参数
    # args = parser.parse_args()
    
    # 获取并打印NPY文件信息
    file_path = '/home/<USER>/data/Synapse/npy/0005_image.npy'
    get_npy_info(file_path)