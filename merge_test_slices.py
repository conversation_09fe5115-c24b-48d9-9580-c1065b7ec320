#!/usr/bin/env python3
"""
将2d_test.list中同一个病人的所有切片合并成一个H5文件
"""

import h5py
import numpy as np
import os
import re
from collections import defaultdict
from tqdm import tqdm
from pathlib import Path

def extract_patient_id(file_path):
    """
    从文件路径中提取病人ID
    例如: liver_123_045.h5 -> 123
    """
    filename = os.path.basename(file_path)
    match = re.match(r'liver_(\d+)_\d+\.h5', filename)
    if match:
        return int(match.group(1))
    return None

def extract_slice_id(file_path):
    """
    从文件路径中提取切片ID
    例如: liver_123_045.h5 -> 45
    """
    filename = os.path.basename(file_path)
    match = re.match(r'liver_\d+_(\d+)\.h5', filename)
    if match:
        return int(match.group(1))
    return None

def load_slice_data(file_path):
    """
    加载单个切片的数据
    """
    try:
        with h5py.File(file_path, 'r') as f:
            image = f['image'][:]
            label = f['label'][:]
            
            # 获取元数据
            metadata = {}
            for key, value in f.attrs.items():
                metadata[key] = value
                
            return {
                'image': image,
                'label': label,
                'metadata': metadata
            }
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {str(e)}")
        return None

def merge_patient_slices(patient_files, output_path, patient_id):
    """
    合并同一个病人的所有切片
    """
    print(f"🔄 合并病人 liver_{patient_id} 的 {len(patient_files)} 个切片...")
    
    # 按切片编号排序
    patient_files.sort(key=lambda x: extract_slice_id(x))
    
    all_images = []
    all_labels = []
    slice_metadata = []
    
    # 加载所有切片
    for file_path in tqdm(patient_files, desc=f"加载 liver_{patient_id}"):
        slice_data = load_slice_data(file_path)
        if slice_data is None:
            print(f"⚠️  跳过损坏的文件: {file_path}")
            continue
            
        all_images.append(slice_data['image'])
        all_labels.append(slice_data['label'])
        slice_metadata.append(slice_data['metadata'])
    
    if not all_images:
        print(f"❌ 病人 liver_{patient_id} 没有有效的切片数据")
        return False
    
    # 转换为numpy数组 (slices, height, width)
    images_array = np.stack(all_images, axis=0)
    labels_array = np.stack(all_labels, axis=0)
    
    print(f"📊 合并后形状: images={images_array.shape}, labels={labels_array.shape}")
    
    # 保存合并后的文件
    try:
        with h5py.File(output_path, 'w') as f:
            # 保存图像和标签数据
            f.create_dataset('image', data=images_array, compression='gzip', compression_opts=9)
            f.create_dataset('label', data=labels_array, compression='gzip', compression_opts=9)
            
            # 保存病人级别的元数据
            f.attrs['patient_id'] = patient_id
            f.attrs['case_name'] = f'liver_{patient_id}'
            f.attrs['num_slices'] = len(all_images)
            f.attrs['image_shape'] = images_array.shape
            f.attrs['label_shape'] = labels_array.shape
            
            # 保存第一个切片的元数据作为参考
            if slice_metadata:
                first_metadata = slice_metadata[0]
                for key, value in first_metadata.items():
                    if key not in ['slice_index', 'valid_slice_index']:  # 跳过切片特定的元数据
                        f.attrs[f'ref_{key}'] = value
            
            # 保存每个切片的原始索引信息
            original_indices = []
            valid_indices = []
            roi_percentages = []
            
            for metadata in slice_metadata:
                if 'original_slice_index' in metadata:
                    original_indices.append(metadata['original_slice_index'])
                if 'valid_slice_index' in metadata:
                    valid_indices.append(metadata['valid_slice_index'])
                if 'roi_percentage' in metadata:
                    roi_percentages.append(metadata['roi_percentage'])
            
            if original_indices:
                f.create_dataset('original_slice_indices', data=np.array(original_indices))
            if valid_indices:
                f.create_dataset('valid_slice_indices', data=np.array(valid_indices))
            if roi_percentages:
                f.create_dataset('roi_percentages', data=np.array(roi_percentages))
        
        print(f"✅ 成功保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 保存文件失败 {output_path}: {str(e)}")
        return False

def process_test_list(test_list_file, output_dir, new_list_file):
    """
    处理测试列表文件
    """
    print(f"🔍 读取测试列表: {test_list_file}")
    
    if not os.path.exists(test_list_file):
        print(f"❌ 文件不存在: {test_list_file}")
        return
    
    # 创建输出目录
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取所有文件路径并按病人分组
    patient_files = defaultdict(list)
    
    with open(test_list_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            patient_id = extract_patient_id(line)
            if patient_id is not None:
                patient_files[patient_id].append(line)
            else:
                print(f"⚠️  无法解析病人ID: {line}")
    
    print(f"📊 找到 {len(patient_files)} 个不同的病人")
    
    # 处理每个病人
    successful_patients = []
    
    for patient_id in sorted(patient_files.keys()):
        files = patient_files[patient_id]
        print(f"\n📦 处理病人 liver_{patient_id} ({len(files)} 个切片)")
        
        # 检查所有文件是否存在
        valid_files = []
        for file_path in files:
            if os.path.exists(file_path):
                valid_files.append(file_path)
            else:
                print(f"⚠️  文件不存在: {file_path}")
        
        if not valid_files:
            print(f"❌ 病人 liver_{patient_id} 没有有效文件")
            continue
        
        # 合并切片
        output_filename = f"liver_{patient_id}.h5"
        output_path = output_dir / output_filename
        
        if merge_patient_slices(valid_files, str(output_path), patient_id):
            successful_patients.append(patient_id)
    
    # 生成新的列表文件
    print(f"\n📝 生成新的列表文件: {new_list_file}")
    
    with open(new_list_file, 'w') as f:
        for patient_id in sorted(successful_patients):
            output_filename = f"liver_{patient_id}.h5"
            output_path = output_dir / output_filename
            f.write(str(output_path) + '\n')
    
    # 输出统计信息
    print(f"\n🎉 处理完成!")
    print("=" * 60)
    print(f"📊 处理统计:")
    print(f"   总病人数: {len(patient_files)}")
    print(f"   成功处理: {len(successful_patients)}")
    print(f"   输出目录: {output_dir}")
    print(f"   新列表文件: {new_list_file}")
    
    # 显示成功处理的病人
    print(f"\n📋 成功处理的病人:")
    for i, patient_id in enumerate(sorted(successful_patients)):
        files_count = len(patient_files[patient_id])
        print(f"   liver_{patient_id}: {files_count} 个切片")
        if i >= 9:  # 只显示前10个
            remaining = len(successful_patients) - 10
            if remaining > 0:
                print(f"   ... (还有 {remaining} 个病人)")
            break

def main():
    """主函数"""
    # 配置路径
    test_list_file = "/home/<USER>/data/tumor/split/2d_test.list"
    output_dir = "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d_val"
    new_list_file = "/home/<USER>/data/tumor/split/2d_test_merged.list"
    
    print("🔄 2D测试数据切片合并")
    print("=" * 60)
    print(f"📁 输入列表: {test_list_file}")
    print(f"📁 输出目录: {output_dir}")
    print(f"📁 新列表文件: {new_list_file}")
    print("=" * 60)
    
    # 处理测试列表
    process_test_list(test_list_file, output_dir, new_list_file)

if __name__ == "__main__":
    main()
