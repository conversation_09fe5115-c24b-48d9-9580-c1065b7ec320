#!/usr/bin/env python3
"""
可视化H5文件中的CT图像和标签数据
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import os
import argparse
from pathlib import Path

def load_h5_data(h5_file_path):
    """
    从H5文件加载图像和标签数据
    
    Args:
        h5_file_path (str): H5文件路径
    
    Returns:
        tuple: (image, label) 数组
    """
    if not os.path.exists(h5_file_path):
        raise FileNotFoundError(f"H5 file not found: {h5_file_path}")
    
    with h5py.File(h5_file_path, 'r') as f:
        # 检查可用的数据集
        available_keys = list(f.keys())
        print(f"Available datasets in H5 file: {available_keys}")
        
        if 'image' not in f.keys() or 'label' not in f.keys():
            raise KeyError(f"Required datasets 'image' and 'label' not found. Available: {available_keys}")
        
        image = f['image'][:]
        label = f['label'][:]
        
        print(f"Image shape: {image.shape}, dtype: {image.dtype}")
        print(f"Label shape: {label.shape}, dtype: {label.dtype}")
        print(f"Image range: [{np.min(image):.3f}, {np.max(image):.3f}]")
        print(f"Unique labels: {np.unique(label)}")
        
        return image, label

def visualize_slice(image, label, slice_idx=None, save_path=None, title_prefix=""):
    """
    可视化单个CT切片和对应的标签
    """
    if slice_idx is None:
        slice_idx = image.shape[2] // 2  # 选择中间切片
    
    # 确保切片索引在有效范围内
    slice_idx = max(0, min(slice_idx, image.shape[2] - 1))
    
    # 获取切片
    img_slice = image[:, :, slice_idx]
    label_slice = label[:, :, slice_idx]
    
    # 创建图形
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 显示原始CT切片
    axes[0].imshow(img_slice, cmap='gray', origin='lower')
    axes[0].set_title(f'{title_prefix}CT Slice {slice_idx}')
    axes[0].axis('off')
    
    # 显示标签
    unique_labels = np.unique(label_slice)
    colors = ['black', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta', 'orange', 'purple']
    cmap = ListedColormap(colors[:len(unique_labels)])
    
    im_label = axes[1].imshow(label_slice, cmap=cmap, origin='lower', vmin=0, vmax=len(unique_labels)-1)
    axes[1].set_title(f'{title_prefix}Label Slice {slice_idx}')
    axes[1].axis('off')
    
    # 添加标签颜色条
    if len(unique_labels) > 1:
        cbar = plt.colorbar(im_label, ax=axes[1], shrink=0.8)
        cbar.set_ticks(range(len(unique_labels)))
        cbar.set_ticklabels([f'Label {int(l)}' for l in unique_labels])
    
    # 显示叠加图像
    axes[2].imshow(img_slice, cmap='gray', origin='lower')
    # 创建掩码，只显示非零标签
    masked_label = np.ma.masked_where(label_slice == 0, label_slice)
    if np.any(~masked_label.mask):  # 如果有非零标签
        axes[2].imshow(masked_label, cmap=cmap, alpha=0.5, origin='lower', vmin=0, vmax=len(unique_labels)-1)
    axes[2].set_title(f'{title_prefix}Overlay Slice {slice_idx}')
    axes[2].axis('off')
    
    # 添加统计信息
    total_pixels = img_slice.size
    label_stats = []
    for label_val in unique_labels:
        count = np.sum(label_slice == label_val)
        percentage = count / total_pixels * 100
        label_stats.append(f'Label {int(label_val)}: {count} ({percentage:.1f}%)')
    
    # 在图像下方添加统计信息
    stats_text = '\n'.join(label_stats)
    fig.text(0.5, 0.02, stats_text, ha='center', fontsize=10, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为统计信息留出空间
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    else:
        plt.show()
    
    plt.close()

def visualize_multiple_slices(image, label, num_slices=5, save_path=None, title_prefix=""):
    """
    可视化多个CT切片和对应的标签
    """
    z_dim = image.shape[2]
    if z_dim < num_slices:
        num_slices = z_dim
        print(f"Warning: Requested {num_slices} slices but only {z_dim} available. Using {z_dim} slices.")
    
    slice_indices = np.linspace(z_dim//4, 3*z_dim//4, num_slices, dtype=int)
    
    fig, axes = plt.subplots(3, num_slices, figsize=(3*num_slices, 9))
    
    # 如果只有一个切片，确保axes是2D数组
    if num_slices == 1:
        axes = axes.reshape(3, 1)
    
    # 创建自定义颜色映射
    unique_labels = np.unique(label)
    colors = ['black', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta', 'orange', 'purple']
    cmap = ListedColormap(colors[:len(unique_labels)])
    
    for i, slice_idx in enumerate(slice_indices):
        img_slice = image[:, :, slice_idx]
        label_slice = label[:, :, slice_idx]
        
        # CT切片
        axes[0, i].imshow(img_slice, cmap='gray', origin='lower')
        axes[0, i].set_title(f'CT Slice {slice_idx}')
        axes[0, i].axis('off')
        
        # 标签
        axes[1, i].imshow(label_slice, cmap=cmap, origin='lower', vmin=0, vmax=len(unique_labels)-1)
        axes[1, i].set_title(f'Label Slice {slice_idx}')
        axes[1, i].axis('off')
        
        # 叠加
        axes[2, i].imshow(img_slice, cmap='gray', origin='lower')
        masked_label = np.ma.masked_where(label_slice == 0, label_slice)
        if np.any(~masked_label.mask):
            axes[2, i].imshow(masked_label, cmap=cmap, alpha=0.5, origin='lower', vmin=0, vmax=len(unique_labels)-1)
        axes[2, i].set_title(f'Overlay Slice {slice_idx}')
        axes[2, i].axis('off')
    
    # 添加行标签
    axes[0, 0].set_ylabel('CT Image', rotation=90, fontsize=12, labelpad=20)
    axes[1, 0].set_ylabel('Label', rotation=90, fontsize=12, labelpad=20)
    axes[2, 0].set_ylabel('Overlay', rotation=90, fontsize=12, labelpad=20)
    
    plt.suptitle(f'{title_prefix}Multiple Slices Visualization', fontsize=14)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Multiple slices visualization saved to: {save_path}")
    else:
        plt.show()
    
    plt.close()

def print_data_statistics(image, label, filename=""):
    """
    打印数据统计信息
    """
    print(f"\n=== Data Statistics for {filename} ===")
    print(f"Image shape: {image.shape}")
    print(f"Label shape: {label.shape}")
    print(f"Image dtype: {image.dtype}")
    print(f"Label dtype: {label.dtype}")
    print(f"Image range: [{np.min(image):.3f}, {np.max(image):.3f}]")
    print(f"Image mean: {np.mean(image):.3f}")
    print(f"Image std: {np.std(image):.3f}")
    
    unique_labels, counts = np.unique(label, return_counts=True)
    total_pixels = label.size
    print(f"\nLabel distribution:")
    for label_val, count in zip(unique_labels, counts):
        percentage = count / total_pixels * 100
        print(f"  Label {int(label_val)}: {count:,} pixels ({percentage:.2f}%)")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Visualize CT images and labels from H5 files')
    parser.add_argument('h5_file', help='Path to H5 file')
    parser.add_argument('--slice', '-s', type=int, help='Specific slice index to visualize')
    parser.add_argument('--num_slices', '-n', type=int, default=5, help='Number of slices for multi-slice visualization')
    parser.add_argument('--save_dir', '-o', help='Directory to save visualizations')
    parser.add_argument('--no_multi', action='store_true', help='Skip multi-slice visualization')
    parser.add_argument('--stats_only', action='store_true', help='Only print statistics, no visualization')
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        image, label = load_h5_data(args.h5_file)
        
        # 打印统计信息
        filename = os.path.basename(args.h5_file)
        print_data_statistics(image, label, filename)
        
        if args.stats_only:
            return
        
        # 设置保存路径
        if args.save_dir:
            os.makedirs(args.save_dir, exist_ok=True)
            base_name = os.path.splitext(filename)[0]
            single_save_path = os.path.join(args.save_dir, f'{base_name}_single_slice.png')
            multi_save_path = os.path.join(args.save_dir, f'{base_name}_multiple_slices.png')
        else:
            single_save_path = None
            multi_save_path = None
        
        # 单切片可视化
        visualize_slice(image, label, slice_idx=args.slice, 
                       save_path=single_save_path, 
                       title_prefix=f'{filename} - ')
        
        # 多切片可视化
        if not args.no_multi:
            visualize_multiple_slices(image, label, num_slices=args.num_slices, 
                                    save_path=multi_save_path, 
                                    title_prefix=f'{filename} - ')
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    
    return 0

# 快速检查特定文件的函数
def quick_check_liver():
    """快速检查liver_0.h5文件"""
    h5_file_path = "/home/<USER>/data/tumor/Dataset017_Liver/h5/liver_0.h5"
    
    print("Quick check for liver_0.h5")
    print("=" * 50)
    
    try:
        if not os.path.exists(h5_file_path):
            print(f"File not found: {h5_file_path}")
            return
        
        image, label = load_h5_data(h5_file_path)
        print_data_statistics(image, label, "liver_0.h5")
        
        # 可视化中间切片
        visualize_slice(image, label, title_prefix="liver_0.h5 - ")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) == 1:
        # 如果没有命令行参数，运行快速检查
        quick_check_liver()
    else:
        exit(main())
