#!/usr/bin/env python3
"""
获取ACDC数据集H5文件的基本信息
"""

import h5py
import numpy as np
import os
from pathlib import Path
import argparse

def get_h5_info(h5_file_path):
    """
    获取H5文件的详细信息
    
    Args:
        h5_file_path (str): H5文件路径
    
    Returns:
        dict: 包含文件信息的字典
    """
    if not os.path.exists(h5_file_path):
        return {"error": f"File not found: {h5_file_path}"}
    
    try:
        info = {
            "file_path": h5_file_path,
            "file_name": os.path.basename(h5_file_path),
            "file_size_mb": os.path.getsize(h5_file_path) / (1024 * 1024),
            "datasets": {}
        }
        
        with h5py.File(h5_file_path, 'r') as f:
            # 获取所有数据集的信息
            def visit_func(name, obj):
                if isinstance(obj, h5py.Dataset):
                    dataset_info = {
                        "shape": obj.shape,
                        "dtype": str(obj.dtype),
                        "size": obj.size,
                        "ndim": obj.ndim,
                        "compression": obj.compression,
                        "chunks": obj.chunks
                    }
                    
                    # 如果数据集不太大，获取数据的统计信息
                    if obj.size < 1e7:  # 小于10M个元素
                        data = obj[:]
                        if np.issubdtype(data.dtype, np.number):
                            dataset_info.update({
                                "min": float(np.min(data)),
                                "max": float(np.max(data)),
                                "mean": float(np.mean(data)),
                                "std": float(np.std(data)),
                                "unique_values": len(np.unique(data)) if data.size < 1e6 else "too_large_to_compute"
                            })
                            
                            # 如果是整数类型且唯一值不多，显示唯一值
                            if (np.issubdtype(data.dtype, np.integer) and 
                                data.size < 1e6 and 
                                len(np.unique(data)) <= 20):
                                dataset_info["unique_values_list"] = np.unique(data).tolist()
                    
                    info["datasets"][name] = dataset_info
            
            f.visititems(visit_func)
            
            # 获取文件级别的属性
            info["attributes"] = dict(f.attrs)
            
        return info
        
    except Exception as e:
        return {"error": f"Error reading file: {str(e)}"}

def print_h5_info(info):
    """
    打印H5文件信息
    """
    if "error" in info:
        print(f"❌ {info['error']}")
        return
    
    print(f"📁 File: {info['file_name']}")
    print(f"📍 Path: {info['file_path']}")
    print(f"💾 Size: {info['file_size_mb']:.2f} MB")
    
    if info.get("attributes"):
        print(f"🏷️  Attributes: {info['attributes']}")
    
    print(f"\n📊 Datasets ({len(info['datasets'])}):")
    print("-" * 80)
    
    for dataset_name, dataset_info in info["datasets"].items():
        print(f"\n🔹 Dataset: '{dataset_name}'")
        print(f"   Shape: {dataset_info['shape']}")
        print(f"   Data type: {dataset_info['dtype']}")
        print(f"   Dimensions: {dataset_info['ndim']}D")
        print(f"   Total elements: {dataset_info['size']:,}")
        
        if dataset_info.get('compression'):
            print(f"   Compression: {dataset_info['compression']}")
        if dataset_info.get('chunks'):
            print(f"   Chunks: {dataset_info['chunks']}")
        
        # 数值统计信息
        if 'min' in dataset_info:
            print(f"   Value range: [{dataset_info['min']:.6f}, {dataset_info['max']:.6f}]")
            print(f"   Mean: {dataset_info['mean']:.6f}")
            print(f"   Std: {dataset_info['std']:.6f}")
            
            if isinstance(dataset_info['unique_values'], int):
                print(f"   Unique values: {dataset_info['unique_values']}")
            
            if 'unique_values_list' in dataset_info:
                print(f"   Unique values list: {dataset_info['unique_values_list']}")

def compare_h5_files(file_paths):
    """
    比较多个H5文件的信息
    """
    print("🔍 ACDC H5 Files Comparison")
    print("=" * 80)
    
    all_info = []
    for file_path in file_paths:
        info = get_h5_info(file_path)
        all_info.append(info)
        print_h5_info(info)
        print("\n" + "=" * 80)
    
    # 比较分析
    print("\n📋 Comparison Summary:")
    print("-" * 50)
    
    valid_files = [info for info in all_info if "error" not in info]
    
    if len(valid_files) < 2:
        print("❌ Need at least 2 valid files for comparison")
        return
    
    # 比较文件大小
    sizes = [info['file_size_mb'] for info in valid_files]
    print(f"📏 File sizes: {[f'{s:.2f} MB' for s in sizes]}")
    
    # 比较数据集
    all_datasets = set()
    for info in valid_files:
        all_datasets.update(info['datasets'].keys())
    
    print(f"\n📊 Common datasets:")
    for dataset_name in sorted(all_datasets):
        shapes = []
        dtypes = []
        for info in valid_files:
            if dataset_name in info['datasets']:
                shapes.append(info['datasets'][dataset_name]['shape'])
                dtypes.append(info['datasets'][dataset_name]['dtype'])
            else:
                shapes.append("missing")
                dtypes.append("missing")
        
        print(f"   {dataset_name}:")
        for i, (shape, dtype) in enumerate(zip(shapes, dtypes)):
            file_name = valid_files[i]['file_name'] if i < len(valid_files) else "unknown"
            print(f"     {file_name}: {shape} ({dtype})")
        
        # 检查是否所有文件的该数据集都相同
        unique_shapes = set(str(s) for s in shapes if s != "missing")
        unique_dtypes = set(d for d in dtypes if d != "missing")
        
        if len(unique_shapes) == 1 and len(unique_dtypes) == 1:
            print(f"     ✅ Consistent across all files")
        else:
            print(f"     ⚠️  Inconsistent across files")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Get basic information from ACDC H5 files')
    parser.add_argument('files', nargs='*', help='H5 file paths (if not provided, will use default ACDC files)')
    parser.add_argument('--compare', '-c', action='store_true', help='Compare multiple files')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # 默认的ACDC文件路径
    default_files = [
        "/home/<USER>/data/ACDC/data/patient001_frame01.h5",
        "/home/<USER>/data/ACDC/data/patient001_frame02.h5"
    ]
    
    # 使用提供的文件路径或默认路径
    file_paths = args.files if args.files else default_files
    
    if args.compare or len(file_paths) > 1:
        compare_h5_files(file_paths)
    else:
        for file_path in file_paths:
            info = get_h5_info(file_path)
            print_h5_info(info)
            if len(file_paths) > 1:
                print("\n" + "=" * 80)

def quick_check_acdc():
    """快速检查默认的ACDC文件"""
    print("🏥 Quick ACDC H5 Files Check")
    print("=" * 50)
    
    files = [
        "/home/<USER>/data/ACDC/data/slices/patient001_frame01_slice_1.h5",
        "/home/<USER>/data/ACDC/data/slices/patient003_frame01_slice_3.h5"
    ]
    
    compare_h5_files(files)

if __name__ == "__main__":
    import sys
    if len(sys.argv) == 1:
        # 如果没有命令行参数，运行快速检查
        quick_check_acdc()
    else:
        main()
