#!/usr/bin/env python3
"""
专门用于可视化肝脏数据的脚本
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import os

def visualize_liver_slice_with_tumor(h5_file_path, slice_idx=None, save_path=None):
    """
    可视化包含肿瘤的肝脏切片
    
    Args:
        h5_file_path: H5文件路径
        slice_idx: 切片索引，如果为None则自动寻找包含肿瘤的切片
        save_path: 保存路径
    """
    
    # 加载数据
    with h5py.File(h5_file_path, 'r') as f:
        image = f['image'][:]
        label = f['label'][:]
    
    print(f"Data shape: {image.shape}")
    print(f"Unique labels: {np.unique(label)}")
    
    # 如果没有指定切片，寻找包含肿瘤(label=2)的切片
    if slice_idx is None:
        tumor_slices = []
        for z in range(label.shape[2]):
            if np.any(label[:, :, z] == 2):
                tumor_count = np.sum(label[:, :, z] == 2)
                tumor_slices.append((z, tumor_count))
        
        if tumor_slices:
            # 选择肿瘤像素最多的切片
            tumor_slices.sort(key=lambda x: x[1], reverse=True)
            slice_idx = tumor_slices[0][0]
            print(f"Found {len(tumor_slices)} slices with tumor")
            print(f"Using slice {slice_idx} with {tumor_slices[0][1]} tumor pixels")
            
            # 打印前5个肿瘤切片信息
            print("Top 5 slices with most tumor pixels:")
            for i, (z, count) in enumerate(tumor_slices[:5]):
                print(f"  Slice {z}: {count} tumor pixels")
        else:
            slice_idx = image.shape[2] // 2
            print("No tumor found, using middle slice")
    
    # 获取切片
    img_slice = image[:, :, slice_idx]
    label_slice = label[:, :, slice_idx]
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 定义颜色
    colors = ['black', 'blue', 'red']  # 背景、肝脏、肿瘤
    cmap = ListedColormap(colors)
    
    # 第一行：原始图像、标签、叠加
    axes[0, 0].imshow(img_slice, cmap='gray', origin='lower')
    axes[0, 0].set_title(f'CT Image - Slice {slice_idx}', fontsize=14)
    axes[0, 0].axis('off')
    
    im_label = axes[0, 1].imshow(label_slice, cmap=cmap, origin='lower', vmin=0, vmax=2)
    axes[0, 1].set_title(f'Labels - Slice {slice_idx}', fontsize=14)
    axes[0, 1].axis('off')
    
    # 添加颜色条
    cbar = plt.colorbar(im_label, ax=axes[0, 1], shrink=0.8)
    cbar.set_ticks([0, 1, 2])
    cbar.set_ticklabels(['Background', 'Liver', 'Tumor'])
    
    # 叠加图像
    axes[0, 2].imshow(img_slice, cmap='gray', origin='lower')
    masked_label = np.ma.masked_where(label_slice == 0, label_slice)
    axes[0, 2].imshow(masked_label, cmap=cmap, alpha=0.6, origin='lower', vmin=0, vmax=2)
    axes[0, 2].set_title(f'Overlay - Slice {slice_idx}', fontsize=14)
    axes[0, 2].axis('off')
    
    # 第二行：分别显示肝脏和肿瘤
    # 只显示肝脏
    liver_mask = (label_slice == 1).astype(float)
    axes[1, 0].imshow(img_slice, cmap='gray', origin='lower')
    axes[1, 0].imshow(liver_mask, cmap='Blues', alpha=0.5, origin='lower')
    axes[1, 0].set_title('Liver Only', fontsize=14)
    axes[1, 0].axis('off')
    
    # 只显示肿瘤
    tumor_mask = (label_slice == 2).astype(float)
    axes[1, 1].imshow(img_slice, cmap='gray', origin='lower')
    if np.any(tumor_mask):
        axes[1, 1].imshow(tumor_mask, cmap='Reds', alpha=0.8, origin='lower')
    axes[1, 1].set_title('Tumor Only', fontsize=14)
    axes[1, 1].axis('off')
    
    # 统计信息
    axes[1, 2].axis('off')
    
    # 计算统计信息
    total_pixels = img_slice.size
    bg_count = np.sum(label_slice == 0)
    liver_count = np.sum(label_slice == 1)
    tumor_count = np.sum(label_slice == 2)
    
    stats_text = f"""
Slice {slice_idx} Statistics:

Image Shape: {img_slice.shape}
Total Pixels: {total_pixels:,}

Label Distribution:
• Background: {bg_count:,} ({bg_count/total_pixels*100:.1f}%)
• Liver: {liver_count:,} ({liver_count/total_pixels*100:.1f}%)
• Tumor: {tumor_count:,} ({tumor_count/total_pixels*100:.1f}%)

Image Intensity:
• Min: {np.min(img_slice):.3f}
• Max: {np.max(img_slice):.3f}
• Mean: {np.mean(img_slice):.3f}
• Std: {np.std(img_slice):.3f}
"""
    
    axes[1, 2].text(0.1, 0.9, stats_text, transform=axes[1, 2].transAxes, 
                    fontsize=12, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray"))
    
    plt.suptitle(f'Liver CT Analysis - {os.path.basename(h5_file_path)}', fontsize=16)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    else:
        plt.show()
    
    plt.close()
    
    return slice_idx, tumor_count

def find_all_tumor_slices(h5_file_path):
    """
    找到所有包含肿瘤的切片
    """
    with h5py.File(h5_file_path, 'r') as f:
        label = f['label'][:]
    
    tumor_slices = []
    for z in range(label.shape[2]):
        tumor_count = np.sum(label[:, :, z] == 2)
        if tumor_count > 0:
            tumor_slices.append((z, tumor_count))
    
    return tumor_slices

def visualize_tumor_progression(h5_file_path, save_dir=None):
    """
    可视化肿瘤在不同切片中的分布
    """
    tumor_slices = find_all_tumor_slices(h5_file_path)
    
    if not tumor_slices:
        print("No tumor found in the data")
        return
    
    print(f"Found tumor in {len(tumor_slices)} slices")
    
    # 选择最多5个切片进行可视化
    tumor_slices.sort(key=lambda x: x[1], reverse=True)
    selected_slices = tumor_slices[:min(5, len(tumor_slices))]
    
    with h5py.File(h5_file_path, 'r') as f:
        image = f['image'][:]
        label = f['label'][:]
    
    fig, axes = plt.subplots(2, len(selected_slices), figsize=(4*len(selected_slices), 8))
    
    if len(selected_slices) == 1:
        axes = axes.reshape(2, 1)
    
    colors = ['black', 'blue', 'red']
    cmap = ListedColormap(colors)
    
    for i, (slice_idx, tumor_count) in enumerate(selected_slices):
        img_slice = image[:, :, slice_idx]
        label_slice = label[:, :, slice_idx]
        
        # CT图像
        axes[0, i].imshow(img_slice, cmap='gray', origin='lower')
        axes[0, i].set_title(f'Slice {slice_idx}\n{tumor_count} tumor pixels')
        axes[0, i].axis('off')
        
        # 叠加标签
        axes[1, i].imshow(img_slice, cmap='gray', origin='lower')
        masked_label = np.ma.masked_where(label_slice == 0, label_slice)
        axes[1, i].imshow(masked_label, cmap=cmap, alpha=0.6, origin='lower', vmin=0, vmax=2)
        axes[1, i].set_title(f'With Labels')
        axes[1, i].axis('off')
    
    plt.suptitle(f'Tumor Distribution - {os.path.basename(h5_file_path)}', fontsize=16)
    plt.tight_layout()
    
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        save_path = os.path.join(save_dir, f'{os.path.splitext(os.path.basename(h5_file_path))[0]}_tumor_progression.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Tumor progression saved to: {save_path}")
    else:
        plt.show()
    
    plt.close()

def main():
    """主函数"""
    h5_file_path = "/home/<USER>/data/tumor/Dataset017_Liver/h5/liver_0.h5"
    
    if not os.path.exists(h5_file_path):
        print(f"File not found: {h5_file_path}")
        return
    
    print("Visualizing liver_0.h5...")
    
    # 创建输出目录
    output_dir = "liver_visualizations"
    os.makedirs(output_dir, exist_ok=True)
    
    # 可视化最佳肿瘤切片
    slice_idx, tumor_count = visualize_liver_slice_with_tumor(
        h5_file_path, 
        save_path=os.path.join(output_dir, "liver_0_best_tumor_slice.png")
    )
    
    # 可视化肿瘤分布
    visualize_tumor_progression(
        h5_file_path,
        save_dir=output_dir
    )
    
    print(f"\nVisualization completed!")
    print(f"Best tumor slice: {slice_idx} with {tumor_count} tumor pixels")
    print(f"Results saved to: {output_dir}/")

if __name__ == "__main__":
    main()
