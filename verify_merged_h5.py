#!/usr/bin/env python3
"""
验证合并后的H5文件内容
"""

import h5py
import numpy as np
import os

def verify_merged_h5(h5_path):
    """验证合并后的H5文件"""
    print(f"🔍 验证文件: {os.path.basename(h5_path)}")
    
    try:
        with h5py.File(h5_path, 'r') as f:
            # 检查数据集
            print(f"📊 数据集: {list(f.keys())}")
            
            # 检查图像数据
            if 'image' in f:
                image = f['image']
                print(f"   图像形状: {image.shape}")
                print(f"   图像数据类型: {image.dtype}")
                print(f"   图像值范围: [{np.min(image):.6f}, {np.max(image):.6f}]")
                print(f"   图像均值: {np.mean(image):.6f}")
            
            # 检查标签数据
            if 'label' in f:
                label = f['label']
                print(f"   标签形状: {label.shape}")
                print(f"   标签数据类型: {label.dtype}")
                print(f"   标签唯一值: {np.unique(label[:])}")
            
            # 检查属性
            print(f"🏷️  主要属性:")
            for key, value in f.attrs.items():
                if not key.startswith('ref_'):  # 跳过参考属性
                    print(f"   {key}: {value}")
            
            # 检查额外数据集
            if 'original_slice_indices' in f:
                orig_indices = f['original_slice_indices'][:]
                print(f"📋 原始切片索引: {orig_indices[:10]}..." if len(orig_indices) > 10 else f"📋 原始切片索引: {orig_indices}")
            
            if 'roi_percentages' in f:
                roi_pcts = f['roi_percentages'][:]
                print(f"📈 ROI百分比统计:")
                print(f"   最小: {np.min(roi_pcts)*100:.2f}%")
                print(f"   最大: {np.max(roi_pcts)*100:.2f}%")
                print(f"   平均: {np.mean(roi_pcts)*100:.2f}%")
                
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def main():
    """主函数"""
    # 验证几个文件
    test_files = [
        "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d_val/liver_130.h5",
        "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d_val/liver_101.h5",
        "/home/<USER>/data/tumor/Dataset017_Liver/h5_2d_val/liver_129.h5"
    ]
    
    print("🔍 验证合并后的H5文件")
    print("=" * 60)
    
    for filepath in test_files:
        if os.path.exists(filepath):
            verify_merged_h5(filepath)
            print("-" * 60)
        else:
            print(f"❌ 文件不存在: {filepath}")

if __name__ == "__main__":
    main()
