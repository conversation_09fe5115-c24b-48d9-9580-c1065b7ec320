import os
import numpy as np
import nibabel as nib

def filter_label(input_path, output_path, target_label=2):
    """
    只保留标签为 target_label 和背景 0，其他标签设为 0（背景）
    
    :param input_path: 输入的标签 .nii.gz 文件路径
    :param output_path: 输出的文件路径
    :param target_label: 要保留的标签编号（int）
    """
    # 加载 NIfTI 文件
    nii = nib.load(input_path)
    data = nii.get_fdata().astype(np.uint8)  # 转换为整数类型

    # 创建新标签图：保留 target_label 为 1，其它非target_label标签置0
    new_data = np.where(data == target_label, 1, 0)

    # 创建新的 NIfTI 对象
    new_nii = nib.Nifti1Image(new_data, affine=nii.affine, header=nii.header)

    # 保存到指定路径
    nib.save(new_nii, output_path)
    print(f"Saved filtered label to: {output_path}")

# 示例用法
if __name__ == "__main__":
    input_dir = "/home/<USER>/data/tumor/Dataset023_KiTS2023/labelsTr"           # 原始标签目录
    output_dir = "/home/<USER>/data/tumor/Dataset023_KiTS2023/labelsTr_tumor_only" # 处理后输出目录
    os.makedirs(output_dir, exist_ok=True)

    target_label = 2  # 你想保留的标签，比如 label2

    for filename in os.listdir(input_dir):
        if filename.endswith(".nii.gz"):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            filter_label(input_path, output_path, target_label)
